//!This code is part of the Services provided by FullStory, Inc. For license information, please refer to https://www.fullstory.com/legal/terms-and-conditions/
//!Portions of this code are licensed under the following license:
//!  For license information please see fs.js.LEGAL.txt 
"use strict";!function(){var t=function(i,n){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,i){t.__proto__=i}||function(t,i){for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])})(i,n)};function i(i,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=i}t(i,n),i.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}var n=function(){return n=Object.assign||function(t){for(var i,n=1,r=arguments.length;n<r;n++)for(var e in i=arguments[n])Object.prototype.hasOwnProperty.call(i,e)&&(t[e]=i[e]);return t},n.apply(this,arguments)};function r(t,i,n,r){return new(n||(n=Promise))(function(e,s){function u(t){try{h(r.next(t))}catch(t){s(t)}}function o(t){try{h(r["throw"](t))}catch(t){s(t)}}function h(t){var i;t.done?e(t.value):(i=t.value,i instanceof n?i:new n(function(t){t(i)})).then(u,o)}h((r=r.apply(t,i||[])).next())})}function e(t,i){var n,r,e,s,u={label:0,sent:function(){if(1&e[0])throw e[1];return e[1]},trys:[],ops:[]};return s={next:o(0),"throw":o(1),"return":o(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function o(o){return function(h){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;s&&(s=0,o[0]&&(u=0)),u;)try{if(n=1,r&&(e=2&o[0]?r["return"]:o[0]?r["throw"]||((e=r["return"])&&e.call(r),0):r.next)&&!(e=e.call(r,o[1])).done)return e;switch(r=0,e&&(o=[2&o[0],e.value]),o[0]){case 0:case 1:e=o;break;case 4:return u.label++,{value:o[1],done:!1};case 5:u.label++,r=o[1],o=[0];continue;case 7:o=u.ops.pop(),u.trys.pop();continue;default:if(!((e=(e=u.trys).length>0&&e[e.length-1])||6!==o[0]&&2!==o[0])){u=0;continue}if(3===o[0]&&(!e||o[1]>e[0]&&o[1]<e[3])){u.label=o[1];break}if(6===o[0]&&u.label<e[1]){u.label=e[1],e=o;break}if(e&&u.label<e[2]){u.label=e[2],u.ops.push(o);break}e[2]&&u.ops.pop(),u.trys.pop();continue}o=i.call(t,u)}catch(t){o=[6,t],r=0}finally{n=e=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,h])}}}function s(t,i,n){if(n||2===arguments.length)for(var r,e=0,s=i.length;e<s;e++)!r&&e in i||(r||(r=Array.prototype.slice.call(i,0,e)),r[e]=i[e]);return t.concat(r||Array.prototype.slice.call(i))}function u(t){}function o(t){var i={};try{for(var n=t.cookie.split(";"),r=0;r<n.length;r++){var e=n[r].replace(/^\s+|\s+$/g,"").split("=");i[e[0]]||(i[e[0]]=e[1])}}catch(t){"Exception trying to parse cookies: ".concat(t)}return i}function h(t,i){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];try{return t.apply(this,n)}catch(t){try{i&&i(t)}catch(t){}}}}var a=function(){},c=1,f=9,v=4;function l(t,i){try{var n=function(t){var i,n=null!==(i=t.ownerDocument)&&void 0!==i?i:t;return n.nodeType===f?n:document}(i),r=n.createTreeWalker(n,NodeFilter.SHOW_ALL,null,!1);return r.currentNode=i,r}catch(t){return}}function d(t,i,n){var r=l(0,i);if(r)for(var e=r.firstChild();e;)n(e),e=r.nextSibling()}function p(t,i,n){var r=l(0,i);if(r)for(var e=r.lastChild();e;)n(e),e=r.previousSibling()}function w(t,i){var n=l(0,i);return n?n.nextSibling():null}function g(t,i){var n=l(0,i);return n?n.previousSibling():null}function m(t,i){var n=l(0,i);return n?n.parentNode():null}function y(t,i){var n=l(0,i);return n?n.firstChild():null}function b(t,i){return!!y(0,i)}function S(t,i){return t.arrayIsArray(i)}function k(t,i){for(var n=0,r=t;n<r.length;n++)if(i(r[n]))return!0;return!1}function _(t){return"function"==typeof t}function A(t){var i=parseInt(null!=t?t:"",10);return isNaN(i)?void 0:i}function I(t){return null!==t&&"object"==typeof t}function E(t,i){return I(i)&&!S(t,i)&&i.constructor!==Date}function T(t){if(null!=t){var i=C(t)?t:new Date(t);try{return i.toISOString()}catch(i){if("string"==typeof t)return t}}return null}function C(t){return null!=t&&t.constructor===Date}function x(t){return"string"==typeof t}function R(t,i){return 0==t.lastIndexOf(i,0)}function K(t,i,n){for(var r in i)t.objectHasOwnProp(i,r)&&n(i[r],r,i)}function j(t){for(var i in t)if(Object.prototype.hasOwnProperty.call(t,i))return t[i]}function M(t,i){var n=0;for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)&&++n>i)return!1;return n==i}function P(t,i){var n=0;for(var r in t)if(Object.prototype.hasOwnProperty.call(t,r)&&++n>i)return!0;return!1}function O(t){return function(){for(var i,n,r=this,e=[],s=0;s<arguments.length;s++)e[s]=arguments[s];return"function"==typeof(null===(n=null===(i=window.Zone)||void 0===i?void 0:i.root)||void 0===n?void 0:n.run)?window.Zone.root.run(function(){return t.apply(r,e)}):t.apply(this,e)}}function N(t,i,n){if(void 0===n&&(n=O),!t)return a;var r=n(t);return h(i?r.bind(i):r,function(t){"Unexpected error: ".concat(t)})}function L(t){var i=t.doctype;if(!i)return"";var n="<!DOCTYPE ";return n+=i.name,i.publicId&&(n+=" PUBLIC \"".concat(i.publicId,"\"")),i.systemId&&(n+=" \"".concat(i.systemId,"\"")),"".concat(n,">")}function U(t,i){return t.jsonParse(i)}function F(t,i,n){return B(null==t?void 0:t.prototype,i,n)}function B(t,i,r){if(!t)return function(){};var e=Object.getOwnPropertyDescriptor(t,i);if(!e||!e.set)return function(){};var s=e.set,u=N(r),o=!0;function h(t){s.call(this,t),o&&u(this,t)}return Object.defineProperty(t,i,n(n({},e),{set:h})),function(){o=!1;var r=Object.getOwnPropertyDescriptor(t,i);r&&h===r.set&&Object.defineProperty(t,i,n(n({},r),{set:s}))}}function H(t,i){var n=i.navigator.languages;return n&&S(t,n)?n.join(","):i.navigator.userLanguage||i.navigator.language}function D(t,i){if(!i)return!1;var n=i.isConnected;return"boolean"==typeof n?n:function(t,i){for(var n=i,r=void 0;n;n=r)if(!(r=m(0,n)))return n;return i}(0,i).nodeType===f}function W(t,i){void 0===t&&(t=[]),void 0===i&&(i=0);for(var n="",r=0,e=t;r<e.length;r++){var s=e[r];n+="/".concat(s)}return"".concat(n,"/").concat(i)}var z,q=["","0","1","-1","true","false","n/a","nan","undefined","null","nil","the_id_that_you_use_in_your_app_for_this_user"],V=["811c9dc5","350ca8af","340ca71c","14cd0a2b","4db211e5","0b069958","3613e041","2f8f13ba","9b61ad43","77074ba4","0da3f8ec","1c750511"];function $(t){var i=function(){return t.dateNow()};return"number"!=typeof i()&&(i=function(){return t.dateGetTime(new Date)}),i}function G(t){return z||(z=function(t){var i,n=$(t),r=null!==(i=function(t){var i;if(function(){var t=window.performance;return!(!t||!t.now)}()){var n=window.performance,r=n.now();if("number"==typeof r&&isFinite(r)&&!(r<=0)){var e=n.timeOrigin;if("number"!=typeof e){var s=$(t)()-n.now(),u=null===(i=n.timing)||void 0===i?void 0:i.navigationStart;e=u?Math.min(s,u):s}var o=Math.round(e);if("number"==typeof o&&isFinite(o)&&!(o<=0))return o}}}(t))&&void 0!==i?i:n();return{now:n,timeOrigin:r,msSinceDocumentStart:function(){return n()-r}}}(t)),z}function Q(t){return G(t).now()}function X(t){return G(t).msSinceDocumentStart()}var Y,J,Z,tt,it,nt,rt,et,st,ut,ot,ht,at=function(t){return t.mathFloor(Q(t)/1e3)},ct=function(t){return at(t)+31536e3};function ft(t,i){if(!i)return null;var n,r=i.split("#");if(r.length<3)return null;for(var e=2;e<r.length;e++){var s=r[e];if(s.indexOf("/")>=0){var u=s.split("/"),o=u[0],h=u[1];r[e]=o,n=h;break}}var a=function(t,i){var n=parseInt(null!=i?i:"",10),r=at(t),e=ct(t)+86400;return isNaN(n)?e:n<=r?void 0:n>e?e:n}(t,n);if(!a)return null;r[0];var c=r[1],f=r[2],v=r[3],l="";v&&(l=decodeURIComponent(v),(q.indexOf(l)>=0||V.indexOf(l)>=0)&&("Ignoring invalid app key \"".concat(l,"\" from cookie."),l=""));var d=(null!=f?f:"").split(":"),p=d[0],w=d[1],g=d[2];return d[3],{appKeyHash:l,expirationAbsTimeSeconds:a,userId:p,orgId:c,pageCount:A(d[4]),sessionId:null!=w?w:"",sessionStartTime:A(g)}}(J=Y||(Y={})).MUT_INSERT=2,J.MUT_REMOVE=3,J.MUT_ATTR=4,J.MUT_TEXT=6,J.MOUSEMOVE=8,J.MOUSEMOVE_CURVE=9,J.SCROLL_LAYOUT=10,J.SCROLL_LAYOUT_CURVE=11,J.MOUSEDOWN=12,J.MOUSEUP=13,J.CLICK=16,J.FOCUS=17,J.VALUECHANGE=18,J.RESIZE_LAYOUT=19,J.DOMLOADED=20,J.LOAD=21,J.PLACEHOLDER_SIZE=22,J.UNLOAD=23,J.BLUR=24,J.SET_FRAME_BASE=25,J.TOUCHSTART=32,J.TOUCHEND=33,J.TOUCHCANCEL=34,J.TOUCHMOVE=35,J.TOUCHMOVE_CURVE=36,J.NAVIGATE=37,J.PLAY=38,J.PAUSE=39,J.RESIZE_VISUAL=40,J.RESIZE_VISUAL_CURVE=41,J.RESIZE_DOCUMENT_CONTENT=42,J.RESIZE_SCROLLABLE_ELEMENT_CONTENT=43,J.LOG=48,J.ERROR=49,J.DBL_CLICK=50,J.FORM_SUBMIT=51,J.WINDOW_FOCUS=52,J.WINDOW_BLUR=53,J.HEARTBEAT=54,J.WATCHED_ELEM=56,J.PERF_ENTRY=57,J.REC_FEAT_SUPPORTED=58,J.SELECT=59,J.CSSRULE_INSERT=60,J.CSSRULE_DELETE=61,J.FAIL_THROTTLED=62,J.AJAX_REQUEST=63,J.SCROLL_VISUAL_OFFSET=64,J.SCROLL_VISUAL_OFFSET_CURVE=65,J.MEDIA_QUERY_CHANGE=66,J.RESOURCE_TIMING_BUFFER_FULL=67,J.MUT_SHADOW=68,J.DISABLE_STYLESHEET=69,J.FULLSCREEN=70,J.FULLSCREEN_ERROR=71,J.ADOPTED_STYLESHEETS=72,J.CUSTOM_ELEMENT_DEFINED=73,J.MODAL_OPEN=74,J.MODAL_CLOSE=75,J.LONG_FRAME=77,J.TIMING=78,J.STORAGE_WRITE_FAILURE=79,J.DOCUMENT_PROPERTIES=80,J.ENTRY_NAVIGATE=81,J.STATS=82,J.VIEWPORT_INTERSECTION=83,J.COPY=84,J.PASTE=85,J.URL_SALT=86,J.URL_ID=87,J.FRAME_STATUS=88,J.SCRIPT_COMPILED_VERSION=89,J.RESET_CSS_SHEET=90,J.ANIMATION_CREATED=91,J.ANIMATION_METHOD_CALLED=92,J.ANIMATION_PROPERTY_SET=93,J.DOCUMENT_TIMELINE_CREATED=94,J.KEYFRAME_EFFECT_CREATED=95,J.KEYFRAME_EFFECT_METHOD_CALLED=96,J.KEYFRAME_EFFECT_PROPERTY_SET=97,J.CAPTURE_SOURCE=98,J.PAGE_DATA=99,J.VISIBILITY_STATE=100,J.DIALOG=101,J.CSSRULE_UPDATE=102,J.CANVAS=103,J.CANVAS_DETACHED_DIMENSION=104,J.INIT_API=105,J.DEFERRED_RESOLVED=106,J.ELEMENT_PROP=109,J.BFCACHE_STATE=110,J.SESSION_INFO=111,J.EVENT_CANCELED=112,J.DIAGNOSTIC_INFO=113,J.RESOLVABLE_EVENT=114,J.IN_SESSION_PREDICTION=115,J.KEEP_ELEMENT=2e3,J.KEEP_URL=2001,J.KEEP_BOUNCE=2002,J.KEEP_CRASH=2003,J.SYS_SETVAR=8193,J.SYS_RESOURCEHASH=8195,J.SYS_SETCONSENT=8196,J.SYS_CUSTOM=8197,J.SYS_REPORTCONSENT=8198,J.SYS_LETHE_MOBILE_BUNDLE_SEQ=8199,(tt=Z||(Z={})).Animation=0,tt.CSSAnimation=1,tt.CSSTransition=2,(nt=it||(it={})).Internal=0,nt.Public=1,(et=rt||(rt={})).Unknown=0,et.Serialization=1,(ut=st||(st={})).Unknown=0,ut.DomSnapshot=1,ut.NodeEncoding=2,ut.LzEncoding=3,ut.ApplyRules=4,ut.ProcessMut=5,(ht=ot||(ot={})).Unknown=0,ht.Successful=1,ht.BlocklistedFrame=2,ht.PartiallyLoaded=3,ht.MissingWindowOrDocument=4,ht.MissingDocumentHead=5,ht.MissingBodyOrChildren=6,ht.AlreadyDefined=7,ht.NoNonScriptElement=8,ht.Exception=9;var vt,lt,dt,pt,wt,gt,mt,yt,bt,St,kt,_t,At,It,Et,Tt,Ct,xt,Rt,Kt,jt,Mt,Pt,Ot,Nt,Lt,Ut,Ft,Bt,Ht,Dt,Wt,zt,qt,Vt,$t,Gt,Qt,Xt,Yt,Jt,Zt,ti,ii,ni,ri,ei,si,ui,oi,hi=["print","alert","confirm"];(lt=vt||(vt={}))[lt.Unset=0]="Unset",lt[lt.Entering=1]="Entering",lt[lt.Restored=2]="Restored",(pt=dt||(dt={}))[pt.Index=1]="Index",pt[pt.Cached=2]="Cached",(gt=wt||(wt={})).GrantConsent=!0,gt.RevokeConsent=!1,(yt=mt||(mt={})).Page=0,yt.Document=1,(St=bt||(bt={})).Unknown=0,St.Api=1,St.FsShutdownFrame=2,St.Hibernation=3,St.Reidentify=4,St.SettingsBlocked=5,St.Size=6,St.Unload=7,St.Hidden=8,(_t=kt||(kt={})).Unknown=0,_t.NotEmpty=1,_t.EmptyBody=2,(It=At||(At={}))[It.UNSET=0]="UNSET",It[It.OK=1]="OK",It[It.ABORTED=2]="ABORTED",It[It.OPAQUE=3]="OPAQUE",It[It.ERROR=4]="ERROR",(Tt=Et||(Et={})).Timing=0,Tt.Navigation=1,Tt.Resource=2,Tt.Paint=3,Tt.Mark=4,Tt.Measure=5,Tt.Memory=6,Tt.TimeOrigin=7,Tt.LayoutShift=8,Tt.FirstInput=9,Tt.LargestContentfulPaint=10,Tt.LongTask=11,Tt.EventTiming=12,Tt.EventTimingCount=13,Tt.LongAnimationFrame=14,Tt.ScriptTiming=15,(xt=Ct||(Ct={})).Timing=["navigationStart","unloadEventStart","unloadEventEnd","redirectStart","redirectEnd","fetchStart","domainLookupStart","domainLookupEnd","connectStart","connectEnd","secureConnectionStart","requestStart","responseStart","responseEnd","domLoading","domInteractive","domContentLoadedEventStart","domContentLoadedEventEnd","domComplete","loadEventStart","loadEventEnd"],xt.Navigation=["name","startTime","duration","initiatorType","redirectStart","redirectEnd","fetchStart","domainLookupStart","domainLookupEnd","connectStart","connectEnd","secureConnectionStart","requestStart","responseStart","responseEnd","unloadEventStart","unloadEventEnd","domInteractive","domContentLoadedEventStart","domContentLoadedEventEnd","domComplete","loadEventStart","loadEventEnd","type","redirectCount","decodedBodySize","encodedBodySize","transferSize","activationStart"],xt.Resource=["name","startTime","duration","initiatorType","redirectStart","redirectEnd","fetchStart","domainLookupStart","domainLookupEnd","connectStart","connectEnd","secureConnectionStart","requestStart","responseStart","responseEnd","decodedBodySize","encodedBodySize","transferSize"],xt.Measure=["name","startTime","duration"],xt.LongAnimationFrame=["name","startTime","duration","renderStart","styleAndLayoutStart","blockingDuration","firstUIEventTimestamp"],xt.ScriptTiming=["name","startTime","duration","invokerType","invoker","executionStart","sourceURL","sourceFunctionName","sourceCharPosition","pauseDuration","forcedStyleAndLayoutDuration","windowAttribution"],xt.Memory=["jsHeapSizeLimit","totalJSHeapSize","usedJSHeapSize"],xt.TimeOrigin=["timeOrigin"],xt.LayoutShift=["startTime","value","hadRecentInput"],xt.FirstInput=["name","startTime","duration","processingStart"],xt.EventTiming=["name","startTime","duration","processingStart","processingEnd","interactionId","target"],xt.LargestContentfulPaint=["name","startTime","duration","renderTime","loadTime","size","element"],xt.EventTimingCount=["interactionCount"],(Kt=Rt||(Rt={})).Performance=0,Kt.PerformanceEntries=1,Kt.PerformanceMemory=2,Kt.Console=3,Kt.Ajax=4,Kt.PerformanceObserver=5,Kt.PerformanceTimeOrigin=7,Kt.WebAnimation=8,Kt.LayoutShift=9,Kt.FirstInput=10,Kt.LargestContentfulPaint=11,Kt.LongTask=12,Kt.HTMLDialogElement=13,Kt.CaptureOnStartupEnabled=14,Kt.CanvasWatcherEnabled=15,Kt.CanvasScreenShotMode=16,Kt.ResizeObserver=17,(Mt=jt||(jt={})).Node=1,Mt.Sheet=2,(Ot=Pt||(Pt={})).StyleSheetHooks=0,Ot.SetPropertyHooks=1,(Lt=Nt||(Nt={})).Document="document",Lt.Event="evt",Lt.Page="page",Lt.User="user",(Ft=Ut||(Ut={})).FsId="fsidentity",Ft.NewUid="newuid",(Ht=Bt||(Bt={})).Elide=0,Ht.Record=1,Ht.Allowlist=2,(Wt=Dt||(Dt={})).Any=0,Wt.Exclude=1,Wt.Mask=2,(qt=zt||(zt={})).Erase=0,qt.MaskText=1,qt.ScrubUrl=2,qt.ScrubCss=3,($t=Vt||(Vt={})).Static=0,$t.Prefix=1,(Qt=Gt||(Gt={})).SignalInvalid=0,Qt.SignalDeadClick=1,Qt.SignalRageClick=2,(Yt=Xt||(Xt={})).ReasonNoSuchOrg=1,Yt.ReasonOrgDisabled=2,Yt.ReasonOrgOverQuota=3,Yt.ReasonBlockedDomain=4,Yt.ReasonBlockedIp=5,Yt.ReasonBlockedUserAgent=6,Yt.ReasonBlockedGeo=7,Yt.ReasonBlockedTrafficRamping=8,Yt.ReasonInvalidURL=9,Yt.ReasonUserOptOut=10,Yt.ReasonInvalidRecScript=11,Yt.ReasonDeletingUser=12,Yt.ReasonNativeHookFailure=13,(Zt=Jt||(Jt={})).Unset=0,Zt.Exclude=1,Zt.Mask=2,Zt.Unmask=3,Zt.Watch=4,Zt.Keep=5,Zt.Defer=6,(ii=ti||(ti={})).Unset=0,ii.Click=1,(ri=ni||(ni={}))[ri.Page=1]="Page",ri[ri.Bundle=2]="Bundle",ri[ri.Event=6]="Event",ri[ri.Settings=8]="Settings",(si=ei||(ei={}))[si.Error=3]="Error",si[si.Page=4]="Page",si[si.Bundle=5]="Bundle",si[si.Event=7]="Event",si[si.Settings=9]="Settings",(oi=ui||(ui={})).MaxPerfMarksPerPage=16384,oi.MaxLogsPerPage=1024,oi.MaxUrlLength=2048,oi.MutationProcessingInterval=250,oi.CurveSamplingInterval=142,oi.DefaultBundleUploadInterval=5e3,oi.HeartbeatInterval=256200,oi.PageInactivityTimeout=18e5,oi.BackoffMax=3e5,oi.ScrollSampleInterval=oi.MutationProcessingInterval/5,oi.SyntheticClickTimeout=oi.ScrollSampleInterval+5,oi.InactivityThreshold=4e3,oi.MaxAjaxPayloadLength=16384,oi.DefaultOrgSettings={MaxPerfMarksPerPage:oi.MaxPerfMarksPerPage,MaxConsoleLogPerPage:oi.MaxLogsPerPage,MaxAjaxPayloadLength:oi.MaxAjaxPayloadLength,MaxUrlLength:oi.MaxUrlLength,RecordPerformanceResourceImg:!0,RecordPerformanceResourceTiming:!0,HttpRequestHeadersAllowlist:[],HttpResponseHeadersAllowlist:[],UrlPrivacyConfig:[{Exclude:{Hash:[{Expression:"#.*"}],QueryParam:[{Expression:"(=)(.*)"}]}}],AttributeBlocklist:[{Target:Dt.Any,Tag:"*",Name:"",Type:Vt.Prefix,Action:zt.Erase}]},oi.DefaultStatsSettings={MaxPayloadLength:8192,MaxEventTypeLength:1024},oi.BlockedFieldValue="__fs__redacted",oi.DefaultRecDisabledMessage="Capture disabled. Turn on debug mode for more information.",oi.ShutdownMessage="Shutdown called.",oi.TextPlain="text/plain";var ai,ci,fi,vi,li,di,pi,wi,gi,mi,yi="_fs_trust_event",bi="__fs",Si="data-fs-namespace",ki="gzip",_i="identity";(ci=ai||(ai={}))[ci.Inactive=1]="Inactive",ci[ci.Pending=2]="Pending",ci[ci.ShouldFlush=3]="ShouldFlush",(vi=fi||(fi={}))[vi.Shutdown=1]="Shutdown",vi[vi.Starting=2]="Starting",vi[vi.Started=3]="Started",vi[vi.Fatal=4]="Fatal",(di=li||(li={})).Set=0,di.Function=1,(wi=pi||(pi={}))[wi.Disabled=0]="Disabled",wi[wi.CaptureCanvasOps=1]="CaptureCanvasOps",wi[wi.ScreenshotCanvas=2]="ScreenshotCanvas",(mi=gi||(gi={})).EndPreviewMode="EndPreviewMode",mi.EvtBundle="EvtBundle",mi.GreetFrame="GreetFrame",mi.InitFrameMobile="InitFrameMobile",mi.RequestFrameId="RequestFrameId",mi.RestartFrame="RestartFrame",mi.SetConsent="SetConsent",mi.SetFrameId="SetFrameId",mi.ShutdownFrame="ShutdownFrame",mi.Unknown="Unknown";var Ai,Ii,Ei="_fs_dwell_passed",Ti="__wayfinder",Ci="__wayfinder_style_v1";(Ii=Ai||(Ai={}))[Ii.LoadEvent=0]="LoadEvent",Ii[Ii.MessageEvent=1]="MessageEvent",Ii[Ii.UnloadEvent=2]="UnloadEvent",Ii[Ii.ApiEvent=3]="ApiEvent",Ii[Ii.AllowlistCheck=4]="AllowlistCheck";var xi,Ri,Ki,ji,Mi="_fs_loaded",Pi="_fs_namespace",Oi="FS";function Ni(t){return t[Pi]||Oi}function Li(t){var i,n=t.document;return null!==(i=n.currentScript)&&void 0!==i?i:n._fs_currentScript}function Ui(t,i){return void 0===i&&(i=Ni(t)),t[i]}function Fi(t){return"localhost"==t||"127.0.0.1"==t}(Ri=xi||(xi={})).Consent="consent",Ri.Log="log",Ri.Record="rec",(ji=Ki||(Ki={})).Event="event",ji.Page="page",ji.Vars="setVars",ji.User="user";var Bi=/^([^.]+\.)*(fullstory|onfire).[^.]+(\/|$)/;function Hi(t){return zi(t,"rs","rs-2")}function Di(t){return zi(t,"edge")}var Wi=["rs","rs-2","edge","www","app"];function zi(t){for(var i=[],n=1;n<arguments.length;n++)i[n-1]=arguments[n];if(!t)return t;if(!Bi.test(t))return t;if(i.some(function(i){return 0===t.lastIndexOf("".concat(i,"."),0)}))return t;for(var r=i[0],e=0,s=Wi;e<s.length;e++){var u=s[e];if(0===t.lastIndexOf("".concat(u,"."),0))return"".concat(r,".").concat(t.slice("".concat(u,".").length))}return"".concat(r,".").concat(t)}function qi(t){return Bi.test(t)}function Vi(t,i){return i in t&&"function"==typeof t[i]}function $i(t){return t()}function Gi(t,i,n){void 0===n&&(n="string");var r=t[i];if(typeof r===n)return r}function Qi(t){return Gi(t,"_fs_script")||jn(Rn(t))}function Xi(t){var i;return null!==(i=Gi(t,"_fs_rec_settings_host"))&&void 0!==i?i:Di(Rn(t))}function Yi(t){return Gi(t,"_fs_rec_host")||Hi(Rn(t))}function Ji(t){return Gi(t,"_fs_app_host")||Kn(Rn(t))}function Zi(t){return Gi(t,"_fs_ext_org")||Gi(t,"_fs_org")}function tn(t){var i=Gi(t,"_fs_capture_on_startup","boolean"),n=Gi(t,"_fs_capture_on_start","boolean");return void 0===i?void 0===n||!n:i}function nn(t){return Gi(t,"_fs_asset_map_id")}function rn(t){return Gi(t,"_fs_cookie_domain")}function en(t){return Gi(t,"_fs_ready","function")}function sn(t){return!!Gi(t,"_fs_run_in_iframe","boolean")}function un(t){return!!Gi(t,"_fs_is_outer_script","boolean")}function on(t){return Gi(t,"_fs_replay_flags")}function hn(t){return Gi(t,"_fs_transport","object")}function an(t){return Gi(t,"_fs_pagestart","function")}function cn(t){return!!Gi(t,"_fs_use_socket","boolean")}function fn(t){return!!Gi(t,"_fs_use_mock_protocol","boolean")}function vn(t){return!!Gi(t,"_fs_use_polyfilled_apis","boolean")}function ln(t){return!!Gi(t,"_fs_force_local_resources","boolean")}function dn(t){return Gi(t,"_fs_request","function")}function pn(t){return Gi(t,"_fs_beacon","function")}function wn(t){return Gi(t,"_fs_settings","object")}function gn(t){return Gi(t,"_fs_clientstore","object")}function mn(t){return Gi(t,"_fs_identitystore","object")}function yn(t){return Gi(t,"_fs_multistorage","object")}function bn(t){return Gi(t,"_fs_is_wayfinder","boolean")}function Sn(t){return Gi(t,"_fs_disable_resume","boolean")}function kn(t){return!!Gi(t,"_fs_skip_iframe_injection","boolean")}function _n(t){return!!Gi(t,"_fs_clean_value_getter","boolean")}function An(t){return Gi(t,"_fs_network_budget","number")}function In(t){var i,n=t._fs_prehooks;if((i=n)instanceof WeakMap||i&&Vi(i,"get")&&Vi(i,"set")&&Vi(i,"has")&&Vi(i,"delete"))return n}function En(t,i){var n=Ui(t,i);if(!n)return[];var r=n.q;return r?(delete n.q,r):[]}function Tn(t,i,n){var r=Ui(t,n);if(r){var e=r.q;e||(e=r.q=[]),e.push(i)}}function Cn(t,i){var n=Ui(t,i);return(null==n?void 0:n._v)||"unknown"}function xn(t,i){var n,r=Ui(t,i);return null!==(n=null==r?void 0:r._native)&&void 0!==n&&n}function Rn(t){return function(t,i){var n=t;if(n){if(!i||!qi(n))return n;var r="";return 0===n.indexOf("www.")&&(n=n.slice(4),r="www."),0===n.indexOf("".concat(i,"."))&&(n=n.slice("".concat(i,".").length)),"".concat(r).concat(i,".").concat(n)}}(Gi(t,"_fs_ext_host")||Gi(t,"_fs_host"),function(t){return function(t){var i,n=null!==(i=null==t?void 0:t.split("-"))&&void 0!==i?i:[];if(!(n.length<3)){var r=n[0],e=n[n.length-1];if(!("na1"===e||r.length>1))return e}}(Zi(t))}(t))}function Kn(t){return t?Fi(function(t){var i=t,n=i.indexOf(":");return n>=0&&(i=i.slice(0,n)),i}(t))?t:0==t.indexOf("www.")?"app.".concat(t.slice(4)):"app.".concat(t):t}function jn(t){var i=Di(t);if(i)return"".concat(i,"/s/fs.js")}var Mn=function(t,i,n){this.name="ProtocolError",this.message=i,this.status=t,this.data=n};function Pn(t){return t>=400&&502!==t||202==t||206==t}function On(t){return t instanceof Mn&&Pn(t.status)}function Nn(t){return"function"==typeof t}var Ln,Un,Fn,Bn,Hn,Dn=Array.isArray?Array.isArray:function(t){return"[object Array]"===Object.prototype.toString.call(t)},Wn=0,zn=function(t,i){qn[Wn]=t,qn[Wn+1]=i,2===(Wn+=2)&&Ln()},qn=new Array(1e3);function Vn(){for(var t=0;t<Wn;t+=2)(0,qn[t])(qn[t+1]),qn[t]=void 0,qn[t+1]=void 0;Wn=0}function $n(t,i){var n=arguments,r=this,e=new this.constructor(Xn);void 0===e[Qn]&&lr(e);var s,u=r.t;return u?(s=n[u-1],zn(function(){return fr(u,e,s,r.i)})):or(r,e,t,i),e}function Gn(t){if(t&&"object"==typeof t&&t.constructor===this)return t;var i=new this(Xn);return rr(i,t),i}window.MutationObserver?(Fn=0,Bn=new MutationObserver(Vn),Hn=document.createTextNode(""),Bn.observe(Hn,{characterData:!0}),Ln=function(){var t=Fn=++Fn%2;Hn.data="".concat(t)}):(Un=setTimeout,Ln=function(){return Un(Vn,1)});var Qn=Math.random().toString(36).substring(16);function Xn(){}var Yn=void 0,Jn=1,Zn=2,tr=new ar;function ir(t){try{return t.then}catch(t){return tr.error=t,tr}}function nr(t,i,n){i.constructor===t.constructor&&n===$n&&i.constructor.resolve===Gn?function(t,i){i.t===Jn?sr(t,i.i):i.t===Zn?ur(t,i.i):or(i,void 0,function(i){return rr(t,i)},function(i){return ur(t,i)})}(t,i):n===tr?(ur(t,tr.error),tr.error=null):void 0===n?sr(t,i):Nn(n)?function(t,i,n){zn(function(t){var r=!1,e=function(n,e,s,u,o){try{n.call(e,function(n){r||(r=!0,i!==n?rr(t,n):sr(t,n))},function(i){r||(r=!0,ur(t,i))})}catch(t){return t}}(n,i,0,0,"Settle: ".concat(t.o||" unknown promise"));!r&&e&&(r=!0,ur(t,e))},t)}(t,i,n):sr(t,i)}function rr(t,i){var n;t===i?ur(t,new TypeError("You cannot resolve a promise with itself")):"function"==typeof(n=i)||"object"==typeof n&&null!==n?nr(t,i,ir(i)):sr(t,i)}function er(t){t.h&&t.h(t.i),hr(t)}function sr(t,i){t.t===Yn&&(t.i=i,t.t=Jn,0!==t.v.length&&zn(hr,t))}function ur(t,i){t.t===Yn&&(t.t=Zn,t.i=i,zn(er,t))}function or(t,i,n,r){var e=t.v,s=e.length;t.h=null,e[s]=i,e[s+Jn]=n,e[s+Zn]=r,0===s&&t.t&&zn(hr,t)}function hr(t){var i=t.v,n=t.t;if(0!==i.length){for(var r,e,s=t.i,u=0;u<i.length;u+=3)r=i[u],e=i[u+n],r?fr(n,r,e,s):e(s);t.v.length=0}}function ar(){this.error=null}var cr=new ar;function fr(t,i,n,r){var e,s,u=Nn(n),o=!1,h=!1;if(u){if(e=function(t,i){try{return t(i)}catch(t){return cr.error=t,cr}}(n,r),e===cr?(h=!0,s=e.error,e.error=null):o=!0,i===e)return void ur(i,new TypeError("A promises callback cannot return that same promise."))}else e=r,o=!0;i.t!==Yn||(u&&o?rr(i,e):h?ur(i,s):t===Jn?sr(i,e):t===Zn&&ur(i,e))}var vr=0;function lr(t){t[Qn]=vr++,t.t=void 0,t.i=void 0,t.v=[]}function dr(t,i){this.S=t,this.promise=new t(Xn),this.promise[Qn]||lr(this.promise),Dn(i)?(this.k=i,this.length=i.length,this._=i.length,this.i=new Array(this.length),0===this.length?sr(this.promise,this.i):(this.length=this.length||0,this.A(),0===this._&&sr(this.promise,this.i))):ur(this.promise,new Error("Array Methods must be provided an Array"))}dr.prototype.A=function(){for(var t=this.length,i=this.k,n=0;this.t===Yn&&n<t;n++)this.I(i[n],n)},dr.prototype.I=function(t,i){var n=this.S,r=n.resolve;if(r===Gn){var e=ir(t);if(e===$n&&t.t!==Yn)this.T(t.t,i,t.i);else if("function"!=typeof e)this._--,this.i[i]=t;else if(n===pr){var s=new n(Xn);nr(s,t,e),this.C(s,i)}else this.C(new n(function(i){return i(t)}),i)}else this.C(r(t),i)},dr.prototype.T=function(t,i,n){var r=this.promise;r.t===Yn&&(this._--,t===Zn?ur(r,n):this.i[i]=n),0===this._&&sr(r,this.i)},dr.prototype.C=function(t,i){var n=this;or(t,void 0,function(t){return n.T(Jn,i,t)},function(t){return n.T(Zn,i,t)})};var pr=function(t){this[Qn]=vr++,this.i=this.t=void 0,this.v=[],Xn!==t&&("function"!=typeof t&&function(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}(),this instanceof pr?function(t,i){try{i(function(i){rr(t,i)},function(i){ur(t,i)})}catch(i){ur(t,i)}}(this,t):function(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}())};pr.all=function(t){return new dr(this,t).promise},pr.race=function(t){var i=this;return Dn(t)?new i(function(n,r){for(var e=t.length,s=0;s<e;s++)i.resolve(t[s]).then(n,r)}):new i(function(t,i){return i(new TypeError("You must pass an array to race."))})},pr.resolve=Gn,pr.reject=function(t){var i=new this(Xn);return ur(i,t),i},pr.R=function(t){zn=t},pr.K=zn,pr.prototype={constructor:pr,then:$n,"catch":function(t){return this.then(null,t)}};var wr="boolean"==typeof window._fs_use_polyfilled_promise&&window._fs_use_polyfilled_promise,gr="function"==typeof window.Promise?window.Promise:pr,mr=wr?pr:gr;function yr(){var t,i;return{promise:new mr(function(n,r){t=n,i=r}),resolve:t,reject:i}}function br(t,i,n){return new mr(function(r){t.setWindowTimeout(i,h(r),n)})}function Sr(t){for(var i=t.reduce(function(t,i){return t+i.byteLength},0),n=new ArrayBuffer(i),r=new Uint8Array(n),e=0,s=0,u=t;s<u.length;s++){var o=u[s];r.set(o,e),e+=o.byteLength}return n}function kr(t,i){if(_(i.arrayBuffer))return i.arrayBuffer();var n=yr(),r=n.resolve,e=n.promise,s=new FileReader;return s.readAsArrayBuffer(i),s.onload=function(){r(s.result)},s.onerror=function(i){qs(t,"blob to AB",{err:i}),r(null)},e}function _r(t,i){var n;return void 0===i&&(i=-1),r(this,void 0,void 0,function(){var r,s,u,o,h;return e(this,function(e){switch(e.label){case 0:r=[],s=0,e.label=1;case 1:return[4,t.read()];case 2:return u=e.sent(),o=u.value,h=u.done,s+=null!==(n=null==o?void 0:o.length)&&void 0!==n?n:0,i>-1&&s>i&&o?(r.push(o.slice(0,o.length-(s-i))),t.cancel()["catch"](function(t){}),[2,r]):h?[2,r]:(void 0!==o&&r.push(o),[3,1]);case 3:return[2]}})})}var Ar=function(t){return r(void 0,void 0,mr,function(){return e(this,function(i){switch(i.label){case 0:return[4,_r(t)];case 1:return[2,Sr(i.sent())]}})})};function Ir(){return r(this,void 0,mr,function(){var t,i,n,r;return e(this,function(e){return t=new TextEncoderStream,i=new CompressionStream("gzip"),t.readable.pipeThrough(i),n=t.writable.getWriter(),r=i.readable.getReader(),[2,[{write:function(t){return n.ready.then(function(){return n.write(t)})},finalize:function(){return n.ready.then(function(){return n.close()})},onError:function(){n.abort()["catch"](function(t){}),r.cancel()["catch"](function(t){})}},Ar(r)]]})})}function Er(t){return r(this,void 0,mr,function(){var i,n,r,s;return e(this,function(e){switch(e.label){case 0:return e.trys.push([0,5,,6]),[4,Ir()];case 1:return s=e.sent(),i=s[0],n=s[1],[4,i.write(t)];case 2:return e.sent(),[4,i.finalize()];case 3:return e.sent(),[4,n];case 4:return[2,[e.sent(),null]];case 5:return r=e.sent(),null==i||i.onError(),[2,[null,r]];case 6:return[2]}})})}function Tr(){return r(this,void 0,mr,function(){return e(this,function(t){switch(t.label){case 0:return t.trys.push([0,2,,3]),[4,Er("fullstory")];case 1:return[2,null!=t.sent()[0]];case 2:return t.sent(),[3,3];case 3:return[2,!1]}})})}var Cr=function(t,i){var n=function(){try{var t=new MessageChannel;return t.port1.start(),t}catch(t){return null}}();return n?new mr(function(t){var i=n.port1,r=n.port2,e=function(){t(),i.removeEventListener("message",e),i.close()};i.addEventListener("message",e),r.postMessage(void 0),r.close()}):br(t,i,0)},xr=function(t,i){return r(void 0,void 0,mr,function(){var n;return e(this,function(r){switch(r.label){case 0:return(n=t.requestWindowAnimationFrame)?[4,new mr(function(t){return n(window,t)})]:[3,2];case 1:r.sent(),r.label=2;case 2:return[4,Cr(t,i)];case 3:return r.sent(),[2]}})})};function Rr(t,i){void 0===i&&(i=16);var n=Q(t)+i;return{timeRemaining:function(){return Math.max(0,n-Q(t))},didTimeout:!1}}function Kr(t,i,n){return r(this,void 0,mr,function(){var r,s,u,o;return e(this,function(e){switch(e.label){case 0:return(r=i.ResizeObserver)?(s=i.document,u=s.documentElement||s.body||s.head,o=null!=n?n:u,[2,new mr(function(n){var e=new r(function(){xr(t,i).then(function(){e.unobserve(o),n()})});e.observe(o)})]):[4,xr(t,i)];case 1:return e.sent(),[2]}})})}function jr(t,i){throw void 0===i&&(i="Reached unexpected case in exhaustive switch"),new Error(i)}var Mr=function(t){for(var i=[],n=0,r=t;n<r.length;n++){var e=r[n],s={Exclude:Pr(e.Exclude)};if(e.If){s.If=[];for(var u=0,o=e.If;u<o.length;u++){var h=o[u];s.If.push(Pr(h,!0))}}i.push(s)}return i};function Pr(t,i){return void 0===i&&(i=!1),{Hash:Nr(t.Hash,i),Host:Nr(t.Host,i),Path:Nr(t.Path,i),QueryParam:Nr(t.QueryParam,i),Query:Nr(t.Query,i)}}var Or="ig";function Nr(t,i){return t?i?[new RegExp(t.map(function(t){return t.Expression}).join("|"),Or)]:t.map(function(t){return new RegExp(t.Expression,Or)}):[]}var Lr="--blocked--",Ur=function(t,i,n,r){if(void 0===n&&(n=Lr),!i||0===t.length)return i;for(var e=[],s=0,u=t;s<u.length;s++)for(var o=u[s],h=0,a=Fr(o,i,r);h<a.length;h++){var c=a[h];e.push(c)}if(0===e.length)return i;for(var f="",v=0,l=0,d=e=Br(e);l<d.length;l++){var p=d[l],w=p[0],g=p[1];f+="".concat(i.substring(v,w)).concat(n),v=g}return f+i.slice(v)},Fr=function(t,i,n){var r,e=[];for(t.lastIndex=0;null!==(r=t.exec(i));)if(r){if(""===r[0])return".*"!==t.source&&n&&n("scrub: infinite loop",{re:t.source}),[[0,i.length]];if(1!==r.length)for(var s=r.index,u=0,o=1;o<r.length;o++){var h=r[o];if(h&&0!==h.length)if(o%2!=1){var a=s+u,c=a+h.length;e.push([a,c]),u+=h.length}else u+=h.length}else e.push([r.index,t.lastIndex])}return e},Br=function(t){if(t.length<=1)return t;t.sort(function(t,i){return t[0]-i[0]});for(var i=[],n=t[0],r=1;r<t.length;r++){var e=t[r];n[1]<=e[0]?(i.push(n),n=e):n[1]<e[1]&&(n[1]=e[1])}return i.push(n),i},Hr={},Dr="__default";function Wr(t){void 0===t&&(t=Dr);var i=Hr[t];return i||(i=function(){var t=document.implementation.createHTMLDocument("");return t.head||t.documentElement.appendChild(t.createElement("head")),t.body||t.documentElement.appendChild(t.createElement("body")),t}(),t!==Dr&&(i.open(),i.write(t),i.close()),Hr[t]=i),i}var zr=new(function(){function t(){var t=Wr(),i=t.getElementById("urlresolver-base");i||((i=t.createElement("base")).id="urlresolver-base",t.head.appendChild(i));var n=t.getElementById("urlresolver-parser");n||((n=t.createElement("a")).id="urlresolver-parser",t.head.appendChild(n)),this.base=i,this.parser=n}return t.prototype.parseUrl=function(t,i){var n=t;if("undefined"!=typeof URL)try{n||(n=document.baseURI);var r=n?new URL(i,n):new URL(i);if(r.href)return r}catch(t){}return this.parseUrlUsingBaseAndAnchor(n,i)},t.prototype.parseUrlUsingBaseAndAnchor=function(t,i){this.base.setAttribute("href",t),this.parser.setAttribute("href",i);var n=document.createElement("a");return n.href=this.parser.href,n},t.prototype.resolveUrl=function(t,i){return this.parseUrl(t,i).href},t.prototype.resolveToDocument=function(t,i){var n=Vr(t);return null==n?i:this.resolveUrl(n,i)},t}());function qr(t,i){return zr.parseUrl(t,i)}function Vr(t){var i=t.document,n=t.location.href;if("string"==typeof i.baseURI)n=i.baseURI;else{var r=i.getElementsByTagName("base")[0];r&&r.href&&(n=r.href)}return"about:blank"==n&&t.parent!=t?Vr(t.parent):n}var $r=function(t,i,n,r){void 0===n&&(n=Lr);for(var e,s,u=qr("",i),o={Hash:[],Host:[],Path:[],QueryParam:[],Query:[]},h=0,a=t;h<a.length;h++){var c=a[h];Xr(c.If,u)&&Jr(o,c.Exclude)}if(u.host){var f=Ur(o.Host,u.host,n,r);u.host=f,u.port&&(e=f,s=u.port,e.substring(e.length-s.length)!==s)&&(u.port="")}if(u.pathname=Ur(o.Path,Zr(u.pathname,u.host),n),u.hash&&(u.hash=Ur(o.Hash,u.hash,n)),u.search){var v=Qr(o.QueryParam,u.search,n);u.search=Ur(o.Query,v,n)}return u.href},Gr=function(t,i){if(0===t.length||!i)return!0;for(var n=0,r=i.split("?");n<r.length;n++)for(var e=0,s=r[n].replace("?","").split("&");e<s.length;e++){var u=s[e];if(Yr(t,u))return!0}return!1},Qr=function(t,i,n){return void 0===n&&(n=Lr),i.split("?").map(function(i){return i.replace("?","").split("&").map(function(i){return Ur(t,i,n)}).join("&")}).join("?")},Xr=function(t,i){if(!t)return!0;for(var n=Zr(i.pathname,i.host),r=0,e=t;r<e.length;r++){var s=e[r];if(Yr(s.Hash,i.hash)&&Yr(s.Host,i.host)&&Yr(s.Path,n)&&Gr(s.QueryParam,i.search)&&Yr(s.Query,i.search))return!0}return!1},Yr=function(t,i){return 0===t.length||!i||t.every(function(t){return t.lastIndex=0,t.test(i)})},Jr=function(t,i){var n,r,e,s,u;(n=t.Hash).push.apply(n,i.Hash),(r=t.Host).push.apply(r,i.Host),(e=t.Path).push.apply(e,i.Path),(s=t.QueryParam).push.apply(s,i.QueryParam),(u=t.Query).push.apply(u,i.Query)};function Zr(t,i){return i&&"/"!==t.charAt(0)?"/".concat(t):t}var te=new RegExp("[^\\s]"),ie=new RegExp("[\\s]*$");function ne(t){var i=te.exec(t);if(!i)return t;for(var n=i.index,r=(i=ie.exec(t))?t.length-i.index:0,e="\uFFFF",s=t.slice(n,t.length-r).split(/\r\n?|\n/g),u=0;u<s.length;u++)e+="".concat(s[u].length),u!=s.length-1&&(e+=":");return(n||r)&&(e+=" ".concat(n," ").concat(r)),e}String.prototype;var re,ee,se,ue,oe=[["@import\\s+\"","\""],["@import\\s+'","'"]].concat([["url\\(\\s*\"","\"\\s*\\)"],["url\\(\\s*'","'\\s*\\)"],["url\\(\\s*","\\s*\\)"]]),he=".*?".concat(/(?:[^\\](?:\\\\)*)/.source),ae=new RegExp(oe.map(function(t){var i=t[0],n=t[1];return"(".concat(i,")(").concat(he,")(").concat(n,")")}).join("|"),"g"),ce=/url\(["']?(.+?)["']?\)/g,fe=/^\s*\/\//,ve=/[-\\^$*+?.()|[\]{}]/g,le=new RegExp(ve.source),de=function(t){return le.test(t)?t.replace(ve,"\\$&"):t};(ee=re||(re={}))[ee.Exclude=2]="Exclude",ee[ee.Mask=4]="Mask",ee[ee.Unmask=8]="Unmask",ee[ee.Watch=16]="Watch",ee[ee.Keep=32]="Keep",ee[ee.Defer=64]="Defer",(ue=se||(se={}))[ue.Immediate=1]="Immediate",ue[ue.Deferred=2]="Deferred";var pe=[re.Exclude,re.Mask,re.Unmask],we=[re.Watch,re.Keep,re.Defer],ge=pe.concat(we);function me(t){var i=t.tagName;return i?"object"==typeof i?"form":i.toLowerCase():null}var ye,be,Se=/(\s*(\S+)(\s+(?:\d+w|[\d.]+x)){0,1}\s*[,])/gm,ke=/((\s*(\S+)(\s+(?:\d+w|[\d.]+x)){0,1}\s*(\s*\d+\S){0,1}(\s*\d+(.\d*){0,1}\S){0,1}\s*)[,])/gm,_e=1;function Ae(){ye=new Map,be=new Map,_e=1}function Ie(t,i){var n,r;return void 0===i&&(i=Ce(t)),null!==(r=null===(n=null==i?void 0:i.watchKind)||void 0===n?void 0:n.hasKinds())&&void 0!==r&&r}function Ee(t,i){var n,r;return void 0===i&&(i=Ce(t)),null!==(r=null===(n=null==i?void 0:i.watchKind)||void 0===n?void 0:n.has(re.Exclude))&&void 0!==r&&r}function Te(t,i){return void 0===i&&(i=Ce(t)),!i||!!i.mask}function Ce(t){return t?ye.get(t):null}function xe(t){var i;return null!==(i=Ce(Re(t)))&&void 0!==i?i:void 0}function Re(t){return be.get(t)}function Ke(t){try{if(t){var i=Ce(t);if(i)return i.id||0}}catch(t){}return 0}function je(t){return t&&!Ee(t)?Ke(t):0}function Me(t){var i=t.id;t.id=0;var n=Re(i);n&&ye["delete"](n),be["delete"](i)}function Pe(t,i,n){i.parent&&(n(i),i.parent.child==i&&(i.parent.child=i.next),i.parent.lastChild==i&&(i.parent.lastChild=i.prev),i.prev&&(i.prev.next=i.next),i.next&&(i.next.prev=i.prev),i.parent=i.prev=i.next=null,Me(i),i.child&&Oe(t,i.child,n),i.shadow&&Oe(t,i.shadow,n))}function Oe(t,i,n){for(var r=[i];r.length>0&&r.length<1e4;){var e=r.pop();n(e),Me(e),e.next&&r.push(e.next),e.child&&r.push(e.child),e.shadow&&r.push(e.shadow)}Ws(t,r.length<1e4,"clearIds is fast")}var Ne=["allowReorder","attributeName","attributeType","autoReverse","baseFrequency","baseProfile","calcMode","clipPathUnits","contentScriptType","contentStyleType","diffuseConstant","edgeMode","externalResourcesRequired","filterRes","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","referrerPolicy","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector","zoomAndPan"].reduce(function(t,i){return t[i]=i,t[i.toUpperCase()]=i,t},{});function Le(t,i){if(i)for(var n in i)t.push(":".concat(n)),t.push(i[n])}function Ue(t){return t.nodeType===c&&"http://www.w3.org/2000/svg"===t.namespaceURI}var Fe=16e6,Be=/^\s*((prefetch|preload|prerender)\s*)+$/i,He=/^\s*.*((worklet|script|worker|font|fetch)\s*)+$/i;function De(t,i,n,r,e){var s,u,o=i.tag;if(null===o||""===r)return null;if("link"===o&&Be.test(null!==(s=n.getAttribute("rel"))&&void 0!==s?s:"")&&!He.test(null!==(u=n.getAttribute("as"))&&void 0!==u?u:""))return null;var h,a="style"===r?qe(t,e):e,c=function(t,i,n,r){var e,s,u,o,h,a,c,f,v,l,d,p,w,g=void 0;(null===(e=null==n?void 0:n.watchKind)||void 0===e?void 0:e.has(re.Exclude))?g=Dt.Exclude:(null==n?void 0:n.mask)&&(g=Dt.Mask);var m=[],y=null===(o=null===(u=null===(s=os(t).blocklist[Dt.Any])||void 0===s?void 0:s[i])||void 0===u?void 0:u["static"])||void 0===o?void 0:o[r],b=null===(c=null===(a=null===(h=os(t).blocklist[Dt.Any])||void 0===h?void 0:h["*"])||void 0===a?void 0:a["static"])||void 0===c?void 0:c[r];if(void 0!==y&&m.push(y),void 0!==b&&m.push(b),g){var S=null===(l=null===(v=null===(f=os(t).blocklist[g])||void 0===f?void 0:f[i])||void 0===v?void 0:v["static"])||void 0===l?void 0:l[r],k=null===(w=null===(p=null===(d=os(t).blocklist[g])||void 0===d?void 0:d["*"])||void 0===p?void 0:p["static"])||void 0===w?void 0:w[r];void 0!==S&&m.push(S),void 0!==k&&m.push(k)}if(os(t).hasPrefix){var _=as(t,Dt.Any,i,r),A=as(t,Dt.Any,"*",r);if(void 0!==_&&m.push(_),void 0!==A&&m.push(A),g){var I=as(t,g,i,r),E=as(t,g,"*",r);void 0!==I&&m.push(I),void 0!==E&&m.push(E)}}return function(t,i){if(0!==i.length)return t.mathMin.apply(t,i)}(t,m)}(t,o,i,r);if(void 0===c&&!i)return null;switch(c){case void 0:return a;case zt.Erase:return null;case zt.MaskText:return ne(a);case zt.ScrubCss:return h=function(t,i,n){return"".concat(t).concat(us).concat(n)},a.replace(ae,function(t){for(var i=[],n=1;n<arguments.length;n++)i[n-1]=arguments[n];var r=i[0]||i[3]||i[6]||i[9]||i[12],e=(i[1]||i[4]||i[7]||i[10]||i[13],i[2]||i[5]||i[8]||i[11]||i[14]);return i[15],h(r,0,e)});case zt.ScrubUrl:if(!(""!==a||"img"!==o&&"source"!==o||"src"!==r&&"srcset"!==r))return"";var f=$e(t,a,{source:"dom",type:o});if("#"===a[0]){var v=f.indexOf("#");if(v>-1)return f.substring(v)}return f;default:return jr()}}var We={},ze="https://fs-currenturl.invalid";function qe(t,i,n){void 0===n&&(n=window);try{var r=n.location,e=r.origin,s=r.pathname,u=r.search,o="".concat(e).concat(s).concat(u),h=We[o];return h?h.lastIndex=0:(h=new RegExp("".concat(de(o),"/?(#)"),"g"),We[o]=h),i.replace(h,"".concat(ze,"$1"))}catch(n){return qs(t,"cleanCSS",{err:n}),i}}var Ve=/^data:/i;function $e(t,i,n){if(Ve.test(i))return i;switch(n.source){case"dom":switch(n.type){case"frame":case"iframe":return ts(t,i);default:return Ge(t,i)}case"event":switch(n.type){case Y.AJAX_REQUEST:case Y.NAVIGATE:return Ge(t,i);case Y.SET_FRAME_BASE:return ts(t,i);default:return jr()}case"log":return ts(t,i);case"page":switch(n.type){case"base":return ts(t,i);case"referrer":case"url":return Ge(t,i);default:return jr()}case"perfEntry":switch(n.type){case"frame":case"iframe":case"navigation":case"other":return ts(t,i);default:return Ge(t,i)}default:return jr()}}function Ge(t,i){return is(t,Ye,i)}var Qe=ui.DefaultOrgSettings.MaxUrlLength,Xe=Mr(ui.DefaultOrgSettings.UrlPrivacyConfig),Ye=Mr(ui.DefaultOrgSettings.UrlPrivacyConfig);function Je(t,i){Xe=Mr(ui.DefaultOrgSettings.UrlPrivacyConfig.concat(t)),Ye=Mr(t),Qe=i||ui.DefaultOrgSettings.MaxUrlLength}function Ze(t,i,n){Bs.send(t,i,n)}function ts(t,i){return is(t,Xe,i)}function is(t,i,n){return $r(i,n,Lr,Ze.bind(null,t)).substring(0,Qe)}var ns=/([a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+)/gi,rs=/(?:(http)|(ftp)|(ws)|(blob)|(file))[s]?:\/\/(?:[a-zA-Z]|[0-9]|[$-_@.&+#]|[!*(),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+/gi;function es(t,i){return i.replace(ns,"<email>").replace(rs,function(i){return $e(t,i,{source:"log",type:"debug"})})}var ss,us="https://fs-excluded.invalid";function os(t){return ss||hs(t),ss}function hs(t,i){var n,r,e,s,u,o,h,a,c,f,v,l,d,p,w,g;try{for(var m=(ss={blocklist:{},hasPrefix:!1}).blocklist,y=(null!==(e=null==i?void 0:i.length)&&void 0!==e?e:0)>0?i:ui.DefaultOrgSettings.AttributeBlocklist,b={},S=0,k=y;S<k.length;S++){var _=k[S],A=_.Action;switch(A){case zt.Erase:case zt.MaskText:case zt.ScrubCss:case zt.ScrubUrl:break;default:Bs.send(t,"initializeAttributeBlocklist#entryAction",{entryAction:A},"warning"),A=zt.Erase}if(null!==(s=m[f=_.Target])&&void 0!==s||(m[f]={}),null!==(u=(v=m[_.Target])[l=_.Tag])&&void 0!==u||(v[l]={"static":{},regex:{}}),_.Type===Vt.Prefix)null!==(o=b[d=_.Target])&&void 0!==o||(b[d]={}),null!==(h=(p=b[_.Target])[w=_.Tag])&&void 0!==h||(p[w]={}),null!==(a=(g=b[_.Target][_.Tag])[A])&&void 0!==a||(g[A]=[]),b[_.Target][_.Tag][A].push(null!==(c=_.Name)&&void 0!==c?c:"");else{if(!_.Name){Bs.send(t,"initializeAttributeBlocklist#emptyName",{entry:JSON.stringify(_)},"warning");continue}m[_.Target][_.Tag]["static"][_.Name]=A}}for(var I in b)for(var E in b[I])for(var T in b[I][E]){var C=b[I][E][T];m[I][E].regex[T]=new RegExp("^(?:".concat(C.join("|"),")"),"i"),ss.hasPrefix=!0}}catch(i){Bs.send(t,"initializeAttributeBlocklist#fail",{err:i},"warning"),ss={blocklist:(n={},n[Dt.Any]={"*":{"static":{},regex:(r={},r[zt.Erase]=/.*/,r)}},n),hasPrefix:!0}}}function as(t,i,n,r){var e,s,u=null===(s=null===(e=os(t).blocklist[i])||void 0===e?void 0:e[n])||void 0===s?void 0:s.regex;if(u)for(var o in u)if(u[o].test(r))return Number(o)}var cs={target:{any:Dt.Any,exclude:Dt.Exclude,mask:Dt.Mask},action:{erase:zt.Erase,maskText:zt.MaskText,scrubUrl:zt.ScrubUrl,scrubCss:zt.ScrubCss},type:{"static":Vt.Static,prefix:Vt.Prefix}};function fs(t){var i;return{Tag:t.tag,Name:t.name,Target:cs.target[t.target],Action:cs.action[t.action],Type:cs.type[null!==(i=t.type)&&void 0!==i?i:"static"]}}function vs(t){var i="Internal error: unable to determine what JSON error was";try{i=(i="".concat(t)).replace(/[^a-zA-Z0-9.:!, ]/g,"_")}catch(t){}return"\"".concat(i,"\"")}function ls(t,i,n){function r(i,n){var r=0;try{t(i,function(t,i){if(r++>n)throw"break";if("object"==typeof i)return i})}catch(t){return"break"!=t}return!1}var e=function(t,i,n){return void 0===n&&(n="..."),t.length<=i?t:t.length<=n.length||i<=n.length?t.substring(0,i):t.substring(0,i-n.length)+n};function s(r,u,o,h){if(u<1)return 0;var a=function(t){switch(!0){case function(t){return!(!t||t.constructor!=Date)}(t):return i=t,isNaN(i)?"Invalid Date":i.toUTCString();case function(t){return"object"==typeof Node?t instanceof Node:t&&"object"==typeof t&&t.nodeType>0&&"string"==typeof t.nodeName}(t):return function(t){return t.toString()}(t);case void 0===t:return"undefined";case"object"!=typeof t||null==t:return t;case t instanceof Error:return[t.toString(),t.stack].filter(Boolean).join(",")}var i}(r);if(void 0!==a){var c=function(i,n){var r=t(i);return r&&"\""==r[0]?e(r,n,"...\""):r}(a,u);return"string"==typeof c&&c.length<=u?(h.tokens.push(c),c.length):0}if(h.cyclic){h.opath.splice(o);var f=h.opath.lastIndexOf(r);if(f>-1){var v="<Cycle to ancestor #".concat(o-f-1,">");return v="\"".concat(e(v,u-2),"\""),h.tokens.push(v),v.length}h.opath.push(r)}var l=u,d=function(t){return l>=t.length&&(l-=t.length,h.tokens.push(t),!0)},p=function(t){var i=h.tokens.length-1;","===h.tokens[i]?h.tokens[i]=t:d(t)};if(l<2)return 0;if(i(r)){d("[");for(var w=0;w<r.length&&l>0;w++){var g=s(r[w],l-1,o+1,h);if(l-=g,0==g&&!d("null"))break;d(",")}p("]")}else{d("{");var m=n(r);for(w=0;w<m.length&&l>0;w++){var y=m[w],b=r[y];if(!d("\"".concat(y,"\":")))break;if(0==(g=s(b,l-1,o+1,h))){h.tokens.pop();break}l-=g,d(",")}p("}")}return u==1/0?1:u-l}return function(t,i){void 0===i&&(i=1024);try{var n={tokens:[],opath:[],cyclic:r(t,i/4)};return s(t,i,0,n),n.tokens.join("")}catch(t){return vs(t)}}}function ds(t,i){var n=ms(t,i),r=n[0];return n[1]||r}function ps(t,i){if(i in t){var n=t[i];if("number"==typeof n)return n}return 0}function ws(t,i,r,e){void 0===r&&(r={}),void 0===e&&(e="stringifyRawEventError");var s=I(i),u=s?ps(i,"When"):0,o=s?ps(i,"Kind"):0;return n({When:u,Kind:Y.DIAGNOSTIC_INFO,Args:[ds(t,{Name:e,Kind:o,err:r})]},s&&{PIds:i.PIds,FId:i.FId})}function gs(t,i){var n,r=ms(t,i),e=r[0],s=r[1];return s?(e=(n=ms(t,ws(t,i,s)))[0],(s=n[1])?ds(t,ws(t,void 0,s)):e):e}function ms(t,i){var n,r=Object.getOwnPropertyDescriptor(Array.prototype,"toJSON"),e=Object.getOwnPropertyDescriptor(String.prototype,"toJSON"),s=void 0;try{r&&r.value&&r.configurable&&delete Array.prototype.toJSON,e&&e.value&&e.configurable&&delete String.prototype.toJSON,n=t.jsonStringify(i)}catch(t){n="",s=vs(t)}finally{(null==r?void 0:r.value)&&h(function(){return Object.defineProperty(Array.prototype,"toJSON",r)})(),(null==e?void 0:e.value)&&h(function(){return Object.defineProperty(String.prototype,"toJSON",e)})()}return[n,s]}var ys={Ver:"ef20c0efe1b1e26cce373e77a01daaf332b487d7",TS:1752737333},bs=function(){function t(t){var i=this;this.j={bundle:0,event:0},this.M=!1,this.P=t.options.scheme,this.O=t.options.recSettingsHost,this.N=t,this.L=function(t,i){return r(this,void 0,mr,function(){return e(this,function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,mr.race([Tr(),br(t,i,500).then(function(){return!1})])];case 1:return[2,n.sent()];case 2:return n.sent(),[2,!1];case 3:return[2]}})})}(t.wdx,t.window).then(function(t){i.M=t})}return t.prototype.page=function(t){return r(this,void 0,mr,function(){var i;return e(this,function(n){switch(n.label){case 0:return[4,Is(this.N,this.P,Ps(this.N),"/rec/page",{type:"string",data:ds(this.N.wdx,t)},!0)];case 1:return i=n.sent().text,[2,U(this.N.wdx,i)]}})})},t.prototype.settings=function(t){return r(this,void 0,mr,function(){var i,n,r;return e(this,function(e){switch(e.label){case 0:if(i=this.N.options.settings)return[2,i];e.label=1;case 1:return e.trys.push([1,3,,4]),n=t.previewMode?Ps(this.N):this.O,[4,Ms(this.N,this.P,n,t)];case 2:return[2,e.sent()];case 3:if(r=e.sent(),t.previewMode)throw r;return[3,4];case 4:return[2,Ms(this.N,this.P,Ps(this.N),t)]}})})},t.prototype.bundle=function(t){return r(this,void 0,mr,function(){var i=this;return e(this,function(n){return[2,this.U(t,ks,function(t){return i.j.bundle+=t})]})})},t.prototype.event=function(t){return r(this,void 0,mr,function(){var i=this;return e(this,function(n){return[2,this.U(t,_s,function(t){return i.j.event+=t})]})})},t.prototype.U=function(t,i,s){var u;return r(this,void 0,mr,function(){var r,o,h,a,c,f,v,l,d;return e(this,function(e){switch(e.label){case 0:return[4,Cr(this.N.wdx,this.N.window)];case 1:return e.sent(),r=t.bundle,(o=r[2])>2e6?[4,Cr(this.N.wdx,this.N.window)]:[3,3];case 2:e.sent(),e.label=3;case 3:return this.M&&t.disableCompression&&(this.M=!1),h=n({},t),a=h,[4,this.F(t.bundle)];case 4:return a.bundle=e.sent(),c=h.bundle,f=c[1],window,v=i(this.N.wdx,h),[4,Is(this.N,this.P,null!==(u=h.recHost)&&void 0!==u?u:Ps(this.N),v,f)];case 5:return l=e.sent().text,d=U(this.N.wdx,l),window,[2,[s(o),d]]}})})},t.prototype.F=function(t){return r(this,void 0,mr,function(){var i,n,r,s,u;return e(this,function(e){switch(e.label){case 0:return i=t[0],"string"===(n=t[1]).type&&this.M&&this.N.recording.flags().UseCompression?[4,Er(n.data)]:[3,2];case 1:if(r=e.sent(),s=r[0],u=r[1],null!=s)return[2,[i,{type:"ArrayBuffer",data:s,encoding:ki},s.byteLength]];this.M=!1,qs(this.N.wdx,"compression failed",{err:u}),e.label=2;case 2:return[2,t]}})})},t.prototype.bundleBeacon=function(t){var i;return xs(this.N,this.P,null!==(i=t.recHost)&&void 0!==i?i:Ps(this.N),t)},t.prototype.startBeacon=function(t){return r(this,void 0,mr,function(){return e(this,function(i){return[2,Rs(this.N,this.P,Ps(this.N),t)]})})},t}(),Ss=function(){function t(t){this.P=t.options.scheme,this.N=t}return t.prototype.uploadResource=function(t){return r(this,void 0,mr,function(){return e(this,function(i){switch(i.label){case 0:return[4,Is(this.N,this.P,Ps(this.N),"/rec/uploadResource",t)];case 1:return[2,i.sent().text]}})})},t.prototype.queryResources=function(t){return r(this,void 0,mr,function(){var i;return e(this,function(n){switch(n.label){case 0:return[4,Is(this.N,this.P,Ps(this.N),"/rec/queryResources",{type:"string",data:ds(this.N.wdx,t)})];case 1:return i=n.sent().text,[2,U(this.N.wdx,i)]}})})},t}();function ks(t,i,n){return void 0===n&&(n=Q(t)),As("/rec/bundle".concat("v2"===i.version?"/v2":""),i,n)}function _s(t,i,n){return void 0===n&&(n=Q(t)),As("/rec/event",i,n)}function As(t,i,n){var r=i.bundle,e=r[0],s=r[1],u="encoding"in s?s.encoding:_i,o="".concat(t,"?OrgId=").concat(i.orgId,"&UserId=").concat(i.userId,"&SessionId=").concat(i.sessionId,"&PageId=").concat(i.pageId,"&Seq=").concat(e,"&ClientTime=").concat(n,"&CompiledVersion=").concat(ys.Ver);return null!=i.serverPageStart&&(o+="&PageStart=".concat(i.serverPageStart)),null!=i.serverBundleTime&&(o+="&PrevBundleTime=".concat(i.serverBundleTime)),null!=i.lastUserActivity&&(o+="&LastActivity=".concat(i.lastUserActivity)),i.isNewSession&&(o+="&IsNewSession=true"),null!=i.deltaT&&(o+="&DeltaT=".concat(i.deltaT)),u===ki&&(o+="&ContentEncoding=".concat(ki)),o}function Is(t,i,n,s,u,o){return void 0===o&&(o=!1),r(this,void 0,mr,function(){return e(this,function(r){return[2,Ts(t,"POST",i,n,Os(s),o,u)]})})}function Es(t,i,n,s){return r(this,void 0,mr,function(){return e(this,function(r){return[2,Ts(t,"GET",i,n,Os(s),!1)]})})}function Ts(t,i,n,s,u,o,h){return r(this,void 0,mr,function(){var r,a,c,f,v,l,d;return e(this,function(e){switch(e.label){case 0:switch(r=t.options.request||Cs,a="//".concat(s).concat(u),c={},null==h?void 0:h.type){case"string":case"ArrayBuffer":c["Content-Type"]=ui.TextPlain}e.label=1;case 1:return e.trys.push([1,3,,4]),[4,r(n,i,a,o,c,h)];case 2:return f=e.sent(),[3,4];case 3:throw v=e.sent(),Bs.send(t.wdx,"fsRequest",{err:v,method:i,endpoint:u,isCustom:"".concat(r!==Cs)}),v;case 4:if(l={text:f.responseText},200==f.status)return[2,l];try{d=U(t.wdx,l.text)}catch(t){}throw new Mn(f.status,l.text,d)}})})}function Cs(t,i,n,s,u,o){return r(this,void 0,mr,function(){var r;return e(this,function(e){return r=function(t){switch(null==t?void 0:t.type){case"string":case"ArrayBuffer":return t.data;case"FormData":var i=new FormData;for(var n in t.data){var r=t.data[n];if(void 0!==r)if("string"==typeof r)i.append(n,r);else{var e=new Blob([r.data],{type:r.contentType});i.append(n,e,r.filename)}}return i;default:return}}(o),[2,new mr(function(e){var o=!1,h=new XMLHttpRequest;for(var a in h.onreadystatechange=function(){h.readyState!==v||o||(o=!0,e({status:h.status,responseText:h.responseText}))},h.open(i,"".concat(t).concat(n),!0),h.withCredentials=s,u)h.setRequestHeader(a,u[a]);h.send(r)})]})})}function xs(t,i,n,r){var e="".concat(i,"//").concat(n).concat(ks(t.wdx,r),"&SkipResponseBody=true"),s=r.bundle[1];return Ks(t.options.beacon,e,s)}function Rs(t,i,n,s){return r(this,void 0,mr,function(){var r,u,o,h,a;return e(this,function(e){switch(e.label){case 0:r=t.window.document.referrer,u=r?$e(t.wdx,r,{source:"page",type:"referrer"}):"",o="orgId=".concat(s.orgId,"&userId=").concat(s.userId,"&sessionId=").concat(s.sessionId),h={referrer:u},e.label=1;case 1:return e.trys.push([1,3,,4]),[4,Is(t,i,n,"/rec/beacon?".concat(o),{type:"string",data:ds(t.wdx,h)})];case 2:return e.sent(),[3,4];case 3:return a=e.sent(),"failed to send session start beacon ".concat(a),[3,4];case 4:return[2]}})})}function Ks(t,i,n){return(t||js)(i,n)}function js(t,i){if("function"==typeof navigator.sendBeacon)try{return navigator.sendBeacon.bind(navigator)(t,i.data)}catch(t){}return!1}function Ms(t,i,n,s){var u;return r(this,void 0,mr,function(){var r,o,h;return e(this,function(e){switch(e.label){case 0:return r=null!==(u=s.version)&&void 0!==u?u:"v1",o=s.previewMode?"?previewMode=true":"",[4,Es(t,i,n,"/s/settings/".concat(s.orgId,"/").concat(r,"/web").concat(o))];case 1:return h=e.sent().text,[2,U(t.wdx,h)]}})})}function Ps(t){var i,n=null===(i=t.recording.pageResponse())||void 0===i?void 0:i.GCLBSubdomain,r=t.options.recHost;return n&&qi(r)?r.replace(/^rs\./,"".concat(n,".")):r}function Os(t){if(!window.Zone)return t;var i="?";return t.indexOf(i)>-1&&(i="&"),"".concat(t).concat(i,"ngsw-bypass=true")}var Ns=/function\s*([\w\-$]+)?\s*\(/i;function Ls(t){return t.stack||t.backtrace||t.stacktrace}function Us(){var t,i;try{throw new Error("")}catch(n){t="<generated>\n",i=Ls(n)}if(!i){t="<generated-ie>\n";var n=[];try{for(var r=arguments.callee.caller.caller;r&&n.length<10;){var e=Ns.test(r.toString())&&RegExp.$1||"[anonymous]";n.push(e),r=r.caller}}catch(t){t.toString()}i=n.join("\n")}return t+i}function Fs(){try{return window.self!==window.top}catch(t){return!0}}var Bs=function(){function t(){}return t.wrap=function(i,r,e,s){return void 0===s&&(s={}),h(r,function(r){t.send(i,e,n({err:r},s))})},t.B=15,t.send=function(i,n,r,e){if(void 0===r&&(r={}),void 0===e&&(e="error"),!(t.B<=0||(t.B--,fn(window)))){var s=function(t,i,n,r){var e;void 0===r&&(r={});var s=function(t){return I(t)&&"message"in t&&"name"in t}(r.err)?r.err:new Error(i),u=i||s.message||"".concat(null!==(e=r.err)&&void 0!==e?e:"")||"unknown error";s.message!==u&&(r.msg=s.message);var h=o(document).fs_uid,a=h?ft(t,h):void 0;a&&a.orgId!=Zi(window)&&(a=void 0);var c={projectRoot:window.location.origin,deviceTime:Q(t),inIframe:Fs(),CompiledVersion:ys.Ver,CompiledTimestamp:ys.TS,orgId:Zi(window),"userId:sessionId":a?"".concat(a.userId,":").concat(a.sessionId):"NA",context:document.location&&document.location.pathname,message:u,severity:n,language:H(t,window),stacktrace:Ls(s)||Us()},f=function(t,i,n){var r="".concat(encodeURIComponent(i),"=").concat(encodeURIComponent(n));t.push(r)},v=[];for(var l in c)f(v,l,c[l]||"");for(var l in r){var d=Hs(t,r[l]);f(v,"aux_".concat(l),d)}return"https://".concat(Yi(window),"/rec/except?").concat(v.join("&"))}(i,n,e,r);Ks(pn(window),s,{data:"",type:"string"})||(new Image().src=s)}},t}();function Hs(t,i){try{var n=typeof i;switch(n){case"string":case"number":case"boolean":case"undefined":return"".concat(i);default:var r="".concat(n,": ").concat(ds(t,i));return"function"==typeof i.toString&&(r+=" (toString: ".concat(i.toString(),")")),r}}catch(t){return"failed to serialize \"".concat(null==t?void 0:t.message,"\"")}}var Ds={};function Ws(t,i,n,r){return void 0===r&&(r=1),!!i||(Ds[n]=Ds[n]||0,Ds[n]++,!(Ds[n]>r)&&(Bs.send(t,"Assertion failed: ".concat(n)),i))}var zs={};function qs(t,i,n){var r;void 0===n&&(n={}),zs[i]=null!==(r=zs[i])&&void 0!==r?r:0,zs[i]++,zs[i]>1||Bs.send(t,i,n)}var Vs=navigator.userAgent,$s=Vs.indexOf("MSIE ")>-1||Vs.indexOf("Trident/")>-1,Gs=($s&&Vs.indexOf("Trident/5"),$s&&Vs.indexOf("Trident/6"),$s&&Vs.indexOf("rv:11")>-1),Qs=Vs.indexOf("Edge/")>-1,Xs=Vs.indexOf("Opera/")>-1,Ys=(Vs.indexOf("CriOS"),Vs.indexOf("Snapchat")>-1),Js=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(navigator.userAgent),Zs=/^((?!chrome|android).)*(safari)/i.test(window.navigator.userAgent),tu=Js||Zs;function iu(){var t=window.navigator.userAgent.match(/Version\/(\d+)/);return t&&t[1]?parseInt(t[1],10):-1}function nu(t){if(!tu)return!1;var i=iu();return i>=0&&i===t}function ru(t){if(!tu)return!1;var i=iu();return i>=0&&i<t}var eu=nu(9),su=(nu(10),ru(8));function uu(t,i){for(var n=0===i.indexOf("on")?function(t){return"on".concat(t).concat(i.slice(2))}:function(t){return"".concat(t).concat(i.charAt(0).toUpperCase()).concat(i.slice(1))},r=0,e=[function(){return i},function(){return n("webkit")},function(){return n("moz")},function(){return n("ms")}];r<e.length;r++){var s=(0,e[r])();if(s in t)return s}return i}function ou(t,i){var n;if(void 0===i&&(i=0),!t)return!1;try{t.call(function(){})}catch(t){return!1}var r=function(t){try{return void t.call(null)}catch(t){return(t.stack||"").replace(/__fs_nomangle_check_stack(.|\n)*$/,"")}},e=void 0;0!==i&&"number"==typeof Error.stackTraceLimit&&(e=Error.stackTraceLimit,Error.stackTraceLimit=Number.POSITIVE_INFINITY);var s=[function(){throw new Error("")},t],u=function __fs_nomangle_check_stack(){return s.map(r)}(),o=u[0],h=u[1];if(void 0!==e&&(Error.stackTraceLimit=e),!o||!h)return!1;for(var a="\n".charCodeAt(0),c=o.length>h.length?h.length:o.length,f=1,v=f;v<c;v++){var l=o.charCodeAt(o.length-v),d=h.charCodeAt(h.length-v);if(l!=d)break;d!=a&&v!=c-1||(f=v)}return(null!==(n=h.slice(0,h.length-f+1).match(/\.js:\d+([:)]|$)/gm))&&void 0!==n?n:[]).length<=i}ru(10),ru(12);var hu="[native code]";function au(t,i){try{return t.call(i).indexOf(hu)>=0}catch(t){return!1}}var cu="__zone_symbol__OriginalDelegate",fu=[cu,"nr@original"];function vu(t,i){return lu(t,i)[0]}function lu(t,i,n){void 0===n&&(n=!1);var r=[];if(!i)return[void 0,r];for(var e=0,s=fu;e<s.length;e++){var u=i[s[e]];if((_(u)||n&&I(u))&&(r.push(u),au(t,u)))return[u,r]}return au(t,i)?[i,r]:[void 0,r]}function du(t,i,n){if("arrayIsArray"===n){var r=vu(t,i.objectToString);if(!r)return;return i.objectToString=r,function(t){return"[object Array]"==r.call(t)}}}var pu="_fs_weak_map_key",wu=function(){function t(t,i){void 0===i&&(i=!1),this.H=i,this.D=new WeakMap(t)}return t.prototype.get=function(t){return this.H||!this.D.has(t)?this.D.get(gu(t)):this.D.get(t)},t.prototype.set=function(t,i){if(!this.H)try{return this.D.set(t,i),this}catch(t){}return this.W(t,i)},t.prototype.W=function(t,i){return t[pu]=gu(t),this.D.set(t[pu],i),this},t.prototype["delete"]=function(t){return this.D["delete"](t)||this.D["delete"](gu(t))},t.prototype.has=function(t){return this.D.has(t)||this.D.has(gu(t))},t}();function gu(t){return Object.prototype.hasOwnProperty.call(t,pu)?t[pu]:{}}var mu=function(){function t(t,i,n,r){var e;void 0===n&&(n=!1),this.V=t,this.$=i,this.G=n,this.N=r,this.X=a,this.Y=void 0,this.J=a,this.Z=a,this.tt=!1,this.it=null===(e=r.options.preHooks)||void 0===e?void 0:e.get(t)}return t.prototype.before=function(t){return this.X=N(t),this},t.prototype.replaceSync=function(t){return this.Y=N(t),this},t.prototype.afterSync=function(t){return this.it?this.J=yu(this.N.wdx,this.N.window,t):this.J=N(t),this},t.prototype.afterAsync=function(t){return this.Z=yu(this.N.wdx,this.N.window,t),this},t.prototype.disable=function(){if(this.tt=!1,this.nt){var t=this.nt,i=t.override,n=t["native"];this.it&&this.it[this.$]===i?(delete this.it[this.$],this.nt=void 0):this.V[this.$]===i&&(this.V[this.$]=n,this.nt=void 0)}},t.prototype.enable=function(){if(this.tt=!0,this.nt)return!0;if(this.nt=this.rt(),this.it)this.it[this.$]=this.nt.override;else try{this.V[this.$]=this.nt.override}catch(t){return!1}return!0},t.prototype.getTarget=function(){return this.V},t.prototype.rt=function(){var t=this,i=this,r=this.V[this.$],e=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var s,u={that:this,args:t,result:null};return i.tt&&i.X(u),i.it||(s=i.Y?i.Y(n(n({},u),{origFn:r})):r.apply(this,t),u.result=s),i.tt&&(i.J(u),i.Z(u)),u.result};return this.G&&(e.toString=function(){var i;return"function ".concat(null!==(i=t.$)&&void 0!==i?i:"","() { ").concat(hu," }")}),{"native":r,override:e}},t}();function yu(t,i,n){return N(function(r){t.setWindowTimeout(i,h(function(){n(r)}),0)})}var bu={};function Su(t,i,n,r){var e;if(void 0===r&&(r=!1),!i||"function"!=typeof i[n])return null;bu[n]=null!==(e=bu[n])&&void 0!==e?e:new wu;var s=bu[n].get(i);return s||(s=new mu(i,n,r,t),bu[n].set(i,s)),s.enable()?s:null}function ku(t){var i=t.target,n=t.type,r=t.fn,e=t.options;void 0!==r&&null!=i&&("function"==typeof i.addEventListener?i.addEventListener(n,r,e):"function"==typeof i.addListener?i.addListener(r):"Target of ".concat(n," doesn't seem to support listeners"))}function _u(t){var i=t.target,n=t.type,r=t.fn,e=t.options;void 0!==r&&null!=i&&("function"==typeof i.removeEventListener?i.removeEventListener(n,r,e):"function"==typeof i.removeListener?i.removeListener(r):"Target of ".concat(n," doesn't seem to support listeners"))}function Au(t){t.target&&(_u(t),t.target=null,t.fn=void 0)}var Iu=function(){function t(t){var i=this;this.N=t,this.et=[],this.st=[],this.ut=!0,this.ot=!1;try{var n=Object.defineProperty({},"passive",{get:function(){i.ut={capture:!0,passive:!0},i.ot={capture:!1,passive:!0}}});window.addEventListener("test",a,n)}catch(t){}}return t.prototype.add=function(t,i,n,r,e){return void 0===e&&(e=!1),this.addCustom(t,i,n,r,e)},t.prototype.addCustom=function(t,i,n,r,e){void 0===e&&(e=!1);var s={target:t,type:i,fn:Bs.wrap(this.N.wdx,O(function(t){(e||!1!==t.isTrusted||"message"==i||t[yi])&&r(t)}),"domlistener#custom",{type:i}),options:n?this.ut:this.ot,index:this.et.length};return this.et.push(s),ku(s),s},t.prototype.clearAll=function(){for(var t=0,i=this.et;t<i.length;t++){var n=i[t];n.target&&Au(n)}this.et=[];for(var r=0,e=this.st;r<e.length;r++)e[r].clearAll();this.st=[]},t.prototype.clearChild=function(t){var i=this.ht(t);null==i||i.clearAll(),Ws(this.N.wdx,!!i,"clearChild")},t.prototype.createChild=function(){var i=new t(this.N);return this.st.push(i),i},t.prototype.refresh=function(){for(var t=0,i=this.et;t<i.length;t++){var n=i[t];n.target&&(_u(n),ku(n))}for(var r=0,e=this.st;r<e.length;r++)e[r].refresh()},t.prototype.ht=function(t){var i=this.st.indexOf(t);if(i>-1)return this.st.splice(i,1)[0]},t}();function Eu(t,i){return i&&t.pageLeft==i.pageLeft&&t.pageTop==i.pageTop}function Tu(t,i){return i&&t.width==i.width&&t.height==i.height}function Cu(t){return{pageLeft:t.pageLeft,pageTop:t.pageTop,width:t.width,height:t.height}}function xu(t){return"BackCompat"==t.compatMode}function Ru(t){return t&&t.body&&t.documentElement?xu(t)?[t.body.clientWidth,t.body.clientHeight]:[t.documentElement.clientWidth,t.documentElement.clientHeight]:[0,0]}var Ku=function(){function t(t,i,n){var r,e,s,u;this.hasKnownPosition=!1,this.pageLeft=0,this.pageTop=0,this.width=0,this.height=0,this.clientWidth=0,this.clientHeight=0;var o=i.document;if(o&&o.documentElement&&o.body){r=Ru(o),this.clientWidth=r[0],this.clientHeight=r[1];var h=i.visualViewport;if(h){this.hasKnownPosition=!0,this.pageTop=h.pageTop-h.offsetTop,this.pageLeft=h.pageLeft-h.offsetLeft,0==this.pageTop&&(this.pageTop=0),0==this.pageLeft&&(this.pageLeft=0);var a=null!==(s=Nu(i,"innerWidth"))&&void 0!==s?s:0,c=null!==(u=Nu(i,"innerHeight"))&&void 0!==u?u:0;if(a>0&&c>0)return this.width=a,void(this.height=c)}if(void 0!==n&&this.clientWidth==n.clientWidth&&this.clientHeight==n.clientHeight&&n.width>0&&n.height>0)return this.width=n.width,void(this.height=n.height);e=this.ct(t,i),this.width=e[0],this.height=e[1]}}return t.prototype.ct=function(t,i){var n=ju(t,i,"width",this.clientWidth,this.clientWidth+128);void 0===n&&(n=Nu(i,"innerWidth")),void 0===n&&(n=this.clientWidth);var r=ju(t,i,"height",this.clientHeight,this.clientHeight+128);return void 0===r&&(r=Nu(i,"innerHeight")),void 0===r&&(r=this.clientHeight),[n,r]},t}();function ju(t,i,n,r,e){if(t.matchMedia){var s=r,u=e,o=t.matchMedia(i,"(min-".concat(n,": ").concat(s,"px)"));if(null!=o){if(o.matches&&t.matchMedia(i,"(max-".concat(n,": ").concat(s,"px)")).matches)return s;for(;s<=u;){var h=t.mathFloor((s+u)/2);if(t.matchMedia(i,"(min-".concat(n,": ").concat(h,"px)")).matches){if(t.matchMedia(i,"(max-".concat(n,": ").concat(h,"px)")).matches)return h;s=h+1}else u=h-1}}}}function Mu(t,i,n){return new Ku(t,i,n)}var Pu,Ou=function(t,i){this.offsetLeft=0,this.offsetTop=0,this.pageLeft=0,this.pageTop=0,this.width=0,this.height=0,this.scale=0;var n=t.document;if(n.body){"pageXOffset"in t?(this.pageLeft=t.pageXOffset,this.pageTop=t.pageYOffset):n.scrollingElement?(this.pageLeft=n.scrollingElement.scrollLeft,this.pageTop=n.scrollingElement.scrollTop):xu(n)?(this.pageLeft=n.body.scrollLeft,this.pageTop=n.body.scrollTop):n.documentElement&&(n.documentElement.scrollLeft>0||n.documentElement.scrollTop>0)?(this.pageLeft=n.documentElement.scrollLeft,this.pageTop=n.documentElement.scrollTop):(this.pageLeft=n.body.scrollLeft||0,this.pageTop=n.body.scrollTop||0),this.offsetLeft=this.pageLeft-i.pageLeft,this.offsetTop=this.pageTop-i.pageTop;var r=0,e=0;try{r=t.innerWidth,e=t.innerHeight}catch(t){return}if(0!=r&&0!=e){this.scale=i.width/r,this.scale<1&&(this.scale=1);var s=i.width-i.clientWidth,u=i.height-i.clientHeight;this.width=r-s/this.scale,this.height=e-u/this.scale}}};function Nu(t,i){try{return t[i]}catch(t){return}}function Lu(t){var i=[t.clientWidth,t.clientHeight];return t.width===t.clientWidth&&t.height===t.clientHeight||i.push(t.width,t.height),i}function Uu(t){return Pu||(Pu=ls(t.jsonStringify,t.arrayIsArray,t.objectKeys)),Pu}var Fu,Bu,Hu=function(){function t(i,n){var r=this;this.ft=i,this.vt=n,this.lt=0,this.dt=t.wt++,this.gt=Bs.wrap(this.ft,function(){r.yt(),r.bt&&r.bt()},"time#wrappedTick")}return t.checkForBrokenSchedulers=function(i,n){return r(this,void 0,mr,function(){var r,s;return e(this,function(e){switch(e.label){case 0:return!i.requestWindowAnimationFrame||t.St||(r=Q(i))-t.kt<100?[2,!1]:(t.kt=r,t.St=!0,[4,new mr(function(t){return i.requestWindowAnimationFrame(n,t)})]);case 1:return e.sent(),s=[],K(i,t._t,function(t){var i=t.At(r);i&&s.push(i)}),[4,mr.all(s)];case 2:return e.sent(),i.requestWindowAnimationFrame(n,Bs.wrap(i,function(){t.St=!1},"checkForBrokenSchedulers")),[2,!0]}})})},t.stopAll=function(t){K(t,this._t,function(t){return t.stop()})},t.prototype.It=function(t){this.bt=t},t.prototype.stop=function(){this.Et(),delete t._t[this.dt]},t.prototype.Tt=function(i){this.lt=Q(this.ft)+100+1.5*i,t._t[this.dt]=this},t.prototype.Ct=function(){return null!=t._t[this.dt]},t.prototype.yt=function(){delete t._t[this.dt]},t.prototype.At=function(t){if(t>this.lt)return mr.resolve().then(this.gt)["catch"](function(){})},t._t={},t.wt=0,t.St=!1,t.kt=0,t}(),Du=function(t){function n(i,n,r){var e=t.call(this,i,n)||this;return e.xt=r,e.Rt=-1,e}return i(n,t),n.prototype.start=function(t,i){var n=this;void 0===i&&(i=this.xt),-1==this.Rt&&(this.xt=i,this.It(function(){t(),n.Tt(n.xt)}),this.Rt=this.ft.setWindowInterval(this.vt,this.gt,this.xt),this.Tt(this.xt))},n.prototype.Et=function(){-1!=this.Rt&&(this.ft.clearWindowInterval(this.vt,this.Rt),this.Rt=-1,this.It(function(){}))},n}(Hu),Wu=function(t){function n(i,n,r,e,s){void 0===e&&(e=0);for(var u=[],o=5;o<arguments.length;o++)u[o-5]=arguments[o];var h=t.call(this,i,n)||this;return h.Kt=e,h.jt=-1,h.It(function(){h.stop(),r.apply(void 0===s?window:s,u)}),h}return i(n,t),n.prototype.start=function(t){return void 0===t&&(t=this.Kt),this.Kt=t,this.ft.clearWindowTimeout(this.vt,this.jt),this.jt=this.ft.setWindowTimeout(this.vt,this.gt,this.Kt),this.Tt(t),this},n.prototype.isRunning=function(){return this.Ct()},n.prototype.Et=function(){-1!=this.jt&&(this.ft.clearWindowTimeout(this.vt,this.jt),this.jt=-1)},n}(Hu),zu=function(){function t(t,i,n,r,e){this.Mt=n,this.Pt=e,this._=0,this.Ot=new Du(t,i,r),this.open()}return t.prototype.guard=function(t){var i=this;return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return 0==i._?(i.Pt(),void i._--):i._<0?void 0:(i._--,t.apply(this,n))}},t.prototype.close=function(){return this.Ot.stop(),this},t.prototype.open=function(){var t=this;return this._=this.Mt,this.Ot.start(function(){t._=t.Mt}),this},t}(),qu=function(){function t(t){this.ft=t,this.Nt=0,this.Lt=0,this.Lt=Q(this.ft)}return t.prototype.wallTime=function(){return Q(this.ft)},t.prototype.now=function(){var t=this.wallTime()-this.Lt;return t<0&&this.Ut("timekeeper now() is negative"),t},t.prototype.startTime=function(){return this.Lt},t.prototype.setStartTime=function(t){var i=this.wallTime();this.Lt=t,t>i&&(this.Nt=t-i,this.Nt>1e3&&this.Ut("timekeeper set with future ts"))},t.prototype.Ut=function(t){Uu(this.ft)({msg:t,skew:this.Nt,startTime:this.Lt,wallTime:this.wallTime()},1024)},t}();(Bu=Fu||(Fu={})).Indeterminate="indeterminate",Bu.Checked="checked",Bu.Value="value";var Vu=function(){function t(t,i){var n;this.N=t,this.Ft=i,this.Bt=!1,this.Ht={},this.Dt=((n={})[Fu.Checked]={},n[Fu.Indeterminate]={},n[Fu.Value]={},n),this.Wt={},this.zt=[],this.qt={},this.Vt=!1,this.$t=!1,this.Gt={},this.Qt=null,this.Xt=t.window.document}return t.prototype.start=function(){this.Yt()||(this.Bt=!0)},t.prototype.hookInstance=function(t,i){if("input"===me(i))switch(i.type){case"checkbox":case"radio":this.Jt(t,i,"checked");break;default:this.Jt(t,i,"value")}},t.prototype.addInput=function(t){if(t){var i=je(t);if(i){if("input"===me(t)){var n=t;this.Zt(i,n),n.indeterminate&&this.ti(n,!0)}var r=!1;if(function(t){switch(t.type){case"checkbox":return t.checked!=t.hasAttribute("checked");case"radio":return t.checked||t.hasAttribute("checked");default:return(t.value||"")!=function(t){if("select"!=me(t))return t.getAttribute("value")||"";var i=t,n=i.querySelector("option[selected]")||i.querySelector("option");return n&&n.value||""}(t)}}(t)&&(this.ii(t,!1,!0),r=!0),this.Bt&&(this.Ht[i]={elem:t}),!r)if(Xu(t)){var e=$u(this.N.wdx,t);t.checked&&(this.Wt[e]=i)}else this.ni(i,Fu.Value,Qu(this.N,t))}}},t.prototype.ni=function(t,i,n){this.Dt[i][t]=n},t.prototype.ri=function(t,i){return this.Dt[i][t]},t.prototype.ei=function(t){for(var i in this.Dt)delete this.Dt[i][t]},t.prototype.Zt=function(t,i){if(this.Vt)this.$t&&this.hookInstance(t,i);else{var n="checkbox"===i.type||"radio"===i.type?"checked":"value",r=Object.getOwnPropertyDescriptor(HTMLInputElement.prototype,n),e=Object.getOwnPropertyDescriptor(i,n);r&&e&&r!==e&&(this.$t=!0,this.hookInstance(t,i)),this.Vt=!0}},t.prototype.si=function(t,i){void 0===i&&(i=Qu(this.N,t));var n=je(t);if(!t||!n)return!1;if(Xu(t)){var r=$u(this.N.wdx,t);return this.Wt[r]===n!=("true"===i)}return this.ri(n,Fu.Value)!==i},t.prototype.onChange=function(t,i,n){void 0===n&&(n=Qu(this.N,t));var r=je(t);t&&r&&(i||this.si(t,n))&&this.ii(t,i)},t.prototype.onInputChange=function(t){if(t){var i=t;if(i.nodeType===Node.ELEMENT_NODE){var n=Ke(i);if(n){var r=i,e=Qu(this.N,r);this.ui(n,r,e)}}}},t.prototype.onKeyboardChange=function(t){var i,n=function(t){for(var i=t.activeElement;i&&i.shadowRoot;){var n=i.shadowRoot.activeElement;if(!n)return i;i=n}return i}(this.Xt);n&&("value"in(i=n)||"checked"in i)&&!Ee(n)&&this.si(n)&&this.ii(n,t)},t.prototype.tick=function(){for(var t in this.Ht){var i=this.Ht[t],n=i.elem;if(je(n))try{delete this.Ht[t];var r=Qu(this.N,n);if(this.si(n,r))this.ii(n);else if(i.noFsIdInOption){var e=n;Array.prototype.slice.call(e.options).every(function(t){return je(t)})&&(i.noFsIdInOption=!1,this.ii(n))}}finally{this.Bt&&(this.Ht[t]=i)}else delete this.Ht[t],this.ei(t),Xu(n)&&delete this.Wt[$u(this.N.wdx,n)]}},t.prototype.stop=function(){for(var t,i=0,n=this.zt;i<n.length;i++)(e=n[i])();for(var r in this.qt){var e;(e=this.qt[r])&&"function"==typeof e&&e()}this.Dt=((t={})[Fu.Checked]={},t[Fu.Indeterminate]={},t[Fu.Value]={},t),this.zt=[],this.qt={},this.Vt=!1,this.$t=!1},t.prototype.removeInput=function(t){var i=je(t);i&&(this.qt[i]&&(this.qt[i](),delete this.qt[i]),this.ei(i))},t.prototype.ii=function(t,i,n){void 0===i&&(i=!1),void 0===n&&(n=!1);var r=je(t);if(t&&r&&!this.oi(r,t)){var e=Qu(this.N,t);if(Xu(t)){var s=$u(this.N.wdx,t);"false"===e&&this.Wt[s]===r?delete this.Wt[s]:"true"===e&&(this.Wt[s]=r)}else this.ni(r,Fu.Value,e);var u=this.hi(t,r,e,i,n);this.Ft.enqueue({Kind:Y.RESOLVABLE_EVENT,Args:[Y.VALUECHANGE,u]})}},t.prototype.oi=function(t,i){if(this.Ht[t])return!0;if("select"!==me(i))return!1;for(var n=i.options,r=0;r<n.length;r++)if(!je(n[r]))return this.Ht[t]={elem:i,noFsIdInOption:!0},!0;return!1},t.prototype.ti=function(t,i){var n=je(t);n&&this.ai(n,Fu.Indeterminate,i)},t.prototype.ai=function(t,i,n){this.ri(t,i)!==n&&(this.ni(t,i,n),this.Ft.enqueue({Kind:Y.ELEMENT_PROP,Args:[t,i,n]}))},t.prototype.Yt=function(){var t=this,i=Object.getOwnPropertyDescriptor(HTMLInputElement.prototype,"value");return!(!i||!i.set||(this.ci(HTMLInputElement,"value"),this.ci(HTMLInputElement,"checked"),this.ci(HTMLSelectElement,"value"),this.ci(HTMLTextAreaElement,"value"),this.ci(HTMLSelectElement,"selectedIndex"),this.ci(HTMLOptionElement,"selected"),this.zt.push(F(HTMLInputElement,Fu.Indeterminate,function(i,n){return t.ti(i,!!n)})),0))},t.prototype.fi=function(){try{for(var t in this.Gt){var i=this.Gt[t],n=i[0],r=i[1];this.onChange(n,!1,r)}}finally{this.Qt=null,this.Gt={}}},t.prototype.vi=function(t){var i=this;return function(n,r){var e=Gu(i.N.wdx,n),s=je(e);if(s){t===Fu.Checked&&i.ai(s,Fu.Checked,r);var u=e!==n?Qu(i.N,e):r;i.si(e,"".concat(u))&&i.ui(s,e,"".concat(u))}}},t.prototype.ui=function(t,i,n){this.Gt[t]=[i,n],this.Qt||(this.Qt=new Wu(this.N.wdx,this.N.window,this.fi.bind(this)),this.Qt.start())},t.prototype.ci=function(t,i){this.zt.push(F(t,i,this.vi(i)))},t.prototype.Jt=function(t,i,n){this.qt[t]=B(i,n,this.vi(n))},t.prototype.hi=function(t,i,n,r,e){var s=this.N.measurer.enqueue(function(){return{done:!0,result:Yu(t)}},!0),u=Te(t);return function(){var o=u?ne(n):n,h=function(t,i){var n;if(_(i)){var r=i();if(r&&r[0])return null!==(n=r[0])&&void 0!==n&&n}return Yu(t)}(t,s);return[i,o,r,h,e]}},t}();function $u(t,i){if(!i)return"";for(var n=i;n&&"form"!==me(n);)n=m(0,n);var r=n&&"form"==me(n)?Ke(n):0,e=i.name||"_fs_".concat(Ke(i));return"".concat(r,":").concat(e)}function Gu(t,i,n){if(void 0===n&&(n=2),n<=0)return i;var r=me(i),e=m(0,i);return"option"!==r&&"optgroup"!==r||(null==e?void 0:e.nodeType)!==c?i:Gu(t,e,n-1)}function Qu(t,i){if(!i)return"";switch(i.type){case"checkbox":case"radio":return"".concat(i.checked);default:var n=t.options.cleanValueGetter&&t.wdx.inputGetValue?t.wdx.inputGetValue(i):i.value;return n||(n=""),"".concat(n)}}function Xu(t){return t&&"radio"==t.type}function Yu(t){var i=!1;if(t&&t.getBoundingClientRect)try{var n=t.getBoundingClientRect();i=n.width>0&&n.height>0}catch(t){}return i}var Ju,Zu,to,io,no=(Zu=(Ju=window.navigator).vendor,to=void 0!==window.opr,io=Ju.userAgent.indexOf("Edge")>-1,!!Ju.userAgent.match("CriOS")||"Google Inc."===Zu&&!to&&!io),ro=/Firefox/.test(window.navigator.userAgent);function eo(){var t=window.navigator.userAgent.match(/Firefox\/(\d+)/);return t&&t[1]?parseInt(t[1],10):-1}function so(t){return!!ro&&eo()<t}function uo(){var t=window.navigator.userAgent.match(/Chrome\/(\d+)/);return t&&t[1]?parseInt(t[1],10):-1}function oo(t){if(!no)return!1;var i=uo();return-1!==i&&i<t}!!no&&uo(),ro&&eo(),so(28),so(27),oo(23),oo(38),oo(57),oo(60);var ho=function(){function t(t,i,n){var r=this;this.N=t,this.et=n,this.li=new wu(void 0,tu),this.di=!1,this.pi=!1,this.wt=1,this.wi=[],this.gi=[],this.mi=Bs.wrap(this.N.wdx,function(t){var i,n=function(t,i){void 0===i&&(i=window);var n=i.KeyframeEffect,r=t.effect;return r&&(void 0===n||r instanceof n)?r:null}(t,r.yi),e=function(t,i){void 0===i&&(i=window);var n=i.DocumentTimeline,r=t.timeline;return r&&(void 0===n||r instanceof n)?r:null}(t,r.yi),u=vo(t);if((null==n?void 0:n.target)&&0!==je(null==n?void 0:n.target)&&e===r.yi.document.timeline&&u===Z.Animation){var o=Ke(n.target);if(r.li.has(n)){var h=r.li.get(n)||[],a=(c=h[0],h.slice(1));-1===a.indexOf(o)&&(r.bi(n,o),r.li.set(n,s(s([c],a,!0),[o],!1)))}else{var c=r.wt++;r.li.set(n,[c,o]),r.Si.enqueue({Kind:Y.KEYFRAME_EFFECT_CREATED,Args:[c,o,fo(r.N.wdx,n),co(n)]})}if(!r.li.has(t)){var f=r.wt++,v=(c=r.ki(n),r.ki(e));switch(r.li.set(t,[f]),r.Si.enqueue({Kind:Y.ANIMATION_CREATED,Args:[f,vo(t),c,v]}),t.playState){case"finished":r.Si.enqueue({Kind:Y.ANIMATION_METHOD_CALLED,Args:[f,["finish",[]]]});break;case"paused":case"running":r.Si.enqueue({Kind:Y.ANIMATION_PROPERTY_SET,Args:[f,["currentTime",(i=t.currentTime,"number"==typeof i?i:"undefined"!=typeof CSSNumericValue&&i instanceof CSSNumericValue?i.to("ms").value:null)]]});var l="paused"===t.playState?"pause":"play";r.Si.enqueue({Kind:Y.ANIMATION_METHOD_CALLED,Args:[f,[l,[]]]})}r.et.add(t,"cancel",!0,function(){r.Si.enqueue({Kind:Y.ANIMATION_METHOD_CALLED,Args:[f,["cancel",[]]]})}),r.et.add(t,"finish",!0,function(){r.Si.enqueue({Kind:Y.ANIMATION_METHOD_CALLED,Args:[f,["finish",[]]]})})}}},"_snapshotAnimation"),this.Si=i,this.yi=t.window,this.pi=function(t){void 0===t&&(t=window);var i=t.Animation,n=t.Document,r=t.Element;return void 0!==i&&void 0!==i.prototype&&"effect"in i.prototype&&"getAnimations"in n.prototype&&"animate"in r.prototype}(this.yi)}return Object.defineProperty(t.prototype,"nextId",{get:function(){return this.wt},enumerable:!1,configurable:!0}),t.prototype.snapshot=function(t){var i;if(this._i(t))for(var n=0,r=null!==(i=t.getAnimations())&&void 0!==i?i:[];n<r.length;n++){var e=r[n];this.mi(e)}},t.prototype._i=function(t){return this.di&&this.pi&&"getAnimations"in t},t.prototype.start=function(t){var i,n=this;t.CaptureAnimations&&(this.Si.enqueue({Kind:Y.REC_FEAT_SUPPORTED,Args:[Rt.WebAnimation,this.pi]}),this.pi&&(this.di=!0,this.Ai(null===(i=Su(this.N,this.yi.Element.prototype,"animate"))||void 0===i?void 0:i.afterSync(function(t){n.mi(t.result)})),this.Ii("play"),this.Ii("pause"),this.Ii("cancel"),this.Ii("finish"),this.Ei("updateTiming"),this.Ei("setKeyframes"),this.wi.push(F(KeyframeEffect,"target",function(t,i){n.bi(t,Ke(i))}))))},t.prototype.stop=function(){this.et.clearAll(),this.di=!1,this.gi.forEach(function(t){return t&&t.disable()}),this.gi=[],this.wi.forEach(function(t){return t()}),this.wi=[]},t.prototype.bi=function(t,i){var n=this.ki(t);n&&this.Si.enqueue({Kind:Y.KEYFRAME_EFFECT_PROPERTY_SET,Args:[n,["target",i]]})},t.prototype.ki=function(t){return(this.li.get(t)||[])[0]},t.prototype.Ai=function(t){t&&this.gi.push(t)},t.prototype.Ii=function(t){var i,n=this;this.Ai(null===(i=Su(this.N,Animation.prototype,t))||void 0===i?void 0:i.afterSync(function(i){var r=n.ki(i.that);r&&n.Si.enqueue({Kind:Y.ANIMATION_METHOD_CALLED,Args:[r,[t,[]]]})}))},t.prototype.Ei=function(t){var i,n=this;this.Ai(null===(i=Su(this.N,KeyframeEffect.prototype,t))||void 0===i?void 0:i.afterSync(function(i){var r,e,s=n.ki(i.that);if(s)switch(t){case"updateTiming":var u=i.args[0];n.Si.enqueue({Kind:Y.KEYFRAME_EFFECT_METHOD_CALLED,Args:[s,["updateTiming",[ao(u)]]]});break;case"setKeyframes":var o=i.args[0];n.Si.enqueue({Kind:Y.KEYFRAME_EFFECT_METHOD_CALLED,Args:[s,["setKeyframes",[(r=n.N.wdx,e=o,(r.arrayIsArray(e)?e:[e]).map(function(t){var i=[];return K(r,t,function(t,n){i.push([n,t])}),[i,null,null,null]}))]]]})}}))},t}();function ao(t){return[t.delay,t.direction,t.duration?(i=t.duration,"number"==typeof i||"string"==typeof i?i:i.toString()):void 0,t.easing,t.endDelay,t.fill,t.iterationStart,"".concat(t.iterations)];var i}function co(t){var i=t.getTiming(),n=t.composite,r=void 0===n?"replace":n,e=t.iterationComposite,u=void 0===e?"replace":e;return s(s([],ao(i),!0),[r,u],!1)}function fo(t,i){return i.getKeyframes().map(function(i){var n=i.composite,r=i.easing,e=i.offset,s=[];return K(t,i,function(t,i){"composite"!==i&&"computedOffset"!==i&&"easing"!==i&&"offset"!==i&&s.push([i,t])}),[s,n,r,e]})}function vo(t){return"animationName"in t?Z.CSSAnimation:"transitionProperty"in t?Z.CSSTransition:Z.Animation}var lo,po,wo,go,mo="#polyfillshadow";function yo(t,i,n,r,e){var s;try{var u="invalid: no sanitizers";if(!Ws(t,i.length>0,u))throw u;for(var o=0,h=i;o<h.length;o++)h[o].sanitize(r)}catch(i){r[wo.Attrs]=void 0,r[wo.Text]=void 0,"sanitizer failed: ".concat(i),Bs.send(t,"visitorPipeline#sanitize",{err:i})}var a=r;if(e)try{e(a)}catch(i){"visitorPipeline action failed: ".concat(i),Bs.send(t,"visitorPipeline#action",{err:i})}if(!(null===(s=r[wo.Mirror].watchKind)||void 0===s?void 0:s.has(re.Exclude)))for(var c=0,f=n;c<f.length;c++){var v=f[c];try{v.visit(a)}catch(i){"visitor failed: ".concat(i),Bs.send(t,"visitorPipeline#visit",{err:i})}}}(po=lo||(lo={}))[po.New=0]="New",po[po.Update=1]="Update",(go=wo||(wo={}))[go.Windex=0]="Windex",go[go.NodeContext=1]="NodeContext",go[go.Mirror=2]="Mirror",go[go.Node=3]="Node",go[go.Attrs=4]="Attrs",go[go.Text=5]="Text";var bo=function(){function t(){this.Ti=0,this.Ci={},this.Ci.next=this.Ci.prev=this.Ci}return t.prototype.first=function(){return this.Ci.next.value},t.prototype.last=function(){return this.Ci.prev.value},t.prototype.size=function(){return this.Ti},t.prototype.push=function(t){this.Ti++,So(this.Ci.prev,{value:t})},t.prototype.unshift=function(t){this.Ti++,So(this.Ci,{value:t})},t.prototype.pop=function(){return this.Ti>0&&this.Ti--,ko(this.Ci.prev)},t.prototype.shift=function(){return this.Ti>0&&this.Ti--,ko(this.Ci.next)},t}();function So(t,i){var n=t.next;i.next=n,i.prev=t,t.next=n.prev=i}function ko(t){var i=t.prev,n=t.next;return i.next=n,n.prev=i,t.value}var _o,Ao,Io={timeRemaining:function(){return 1},didTimeout:!1};(Ao=_o||(_o={}))[Ao.Idle=0]="Idle",Ao[Ao.Scheduled=1]="Scheduled",Ao[Ao.Processing=2]="Processing";var Eo=function(){function t(t,i){void 0===i&&(i=1),this.N=t,this.xi=i,this.wt=1,this.Ri=_o.Idle,this.Ki=new bo,this.ji={},this.Mi=1}return t.prototype.enqueue=function(t,i){var n=this;if(void 0===i&&(i=!1),!(this.Ri===_o.Processing&&this.Mi>16)){var r={id:this.wt++,isCompleted:!1,process:t,depth:this.Mi,store:i};return this.Ki.push(r),this.Pi(),i?function(){return n.Oi(r)}:void 0}qs(this.N.wdx,"deep recursive task")},t.prototype.Oi=function(t){try{var i=t.id,n=this.ji[i];return n?(delete this.ji[i],[n.result,n.err]):(To(Io,t),Ws(this.N.wdx,t.isCompleted,"queue#task"),[t.result,t.err])}finally{t.result=void 0,t.err=void 0}},t.prototype.flush=function(){this.Ni(Io)},t.prototype.Pi=function(){this.Ri===_o.Idle&&(this.Ri=_o.Scheduled,this.Li())},t.prototype.Ni=function(t){if(this.Ri===_o.Scheduled){var i=0;this.Ri=_o.Processing;for(var n=this.Ki.first();n&&Co(i,this.xi,t);)this.Mi=n.depth+1,To(t,n),n.isCompleted&&(this.Ki.shift(),!n.store||void 0===n.result&&void 0===n.err||(this.ji[n.id]=n)),n=this.Ki.first(),i++;this.Ri=_o.Idle,this.Mi=1,this.Ki.size()>0&&this.Pi()}},t}();function To(t,i){var n;if(!i.isCompleted)try{var r=(null===(n=i.process)||void 0===n?void 0:n.call(i,t))||{done:!0};r.done&&(i.isCompleted=!0,i.result=r.result,delete i.process)}catch(t){i.isCompleted=!0,i.err=t,delete i.process}}function Co(t,i,n){return t<i||n.timeRemaining()>0}var xo=function(t){function n(i){return t.call(this,i)||this}return i(n,t),n.prototype.Li=function(){var t=this;Cr(this.N.wdx,this.N.window).then(function(){t.Ni(Rr(t.N.wdx,36))})},n}(Eo),Ro={INPUT:!0,TEXTAREA:!0,NOSCRIPT:!0},Ko=function(){function t(t,i,n,r,e){this.N=t,this.Ui=i,this.Fi=n,this.Bi=r,this.Hi=e,Ae(),this.Ki=new xo(this.N)}return t.prototype.tokenizeNode=function(t,i,n,r,e,s,u){var o=this,h=Ce(i),a=Ce(n),c=[];return function(i,n){var f=_e;try{return o.Di(t,h,a,r,c,e,s,u),!0}catch(t){return qs(i,"tryTokenize",{err:t}),_e=f,!1}}(this.N.wdx)||(c=[]),c},t.prototype.Di=function(t,i,n,r,e,s,u,o){for(var h,a,f=[{parentMirror:i,nextMirror:n,node:r}],v=function(t,i){return function(n){n&&t.push({parentMirror:i,nextMirror:null,node:n})}};f.length;){var l=f.pop();if(l)if("string"!=typeof l){var d=l.node,w=this.Wi(t,l,e,s,u);if(null!=w&&!(null===(h=w.watchKind)||void 0===h?void 0:h.has(re.Exclude))){var g=w.type===c?d.shadowRoot:null;(g||b(this.N.wdx,d))&&((null===(a=w.watchKind)||void 0===a?void 0:a.has(re.Defer))?o(d,se.Deferred):(f.push("]"),p(this.N.wdx,d,v(f,w)),g&&f.push({parentMirror:w,nextMirror:null,node:g}),f.push("[")))}}else"<"===l[0]&&++_e,e.push(l)}},t.prototype.Wi=function(t,i,n,r,e){var s,u,o,h,a=this,v=i.node,l=i.parentMirror,d=i.nextMirror,p=me(v),w=v.nodeName,g=v.nodeType;if("script"===p||8===g)return null;var y=function(t){var i;if(t===(null===(i=null==t?void 0:t.host)||void 0===i?void 0:i.shadowRoot))return function(t){return function(t){if(!_(t))return!1;try{return au(Function.prototype.toString,t)}catch(t){return!1}}(t.constructor)}(t)?"#shadow":mo}(v);if(y===mo)return null;var b,S,k,A,I,E=function(t,i,n,r){void 0===i&&(i=t.nodeName),void 0===n&&(n=t.nodeType),void 0===r&&(r=me(t));var e={id:_e++,name:i,type:n,tag:r};return ye.set(t,e),be.set(e.id,t),e}(v,w,g,p);E.shadowRootType=y||(null==l?void 0:l.shadowRootType),l&&(y?(l.shadow=E,E.parent=l):(b=l,k=d,Pe(this.N.wdx,S=E,this.Hi.bind(this)),S.parent=b,S.next=k,k&&(S.prev=k.prev,k.prev=S),null==S.next?(S.prev=b.lastChild,b.lastChild=S):S.next.prev=S,null==S.prev?b.child=S:S.prev.next=S)),E.mask=null===(s=E.parent)||void 0===s?void 0:s.mask;try{switch(g){case 3:if(void 0===E.mask&&(E.mask=!E.parent||E.parent.mask),E.mask){var T=m(this.N.wdx,v);(null==T?void 0:T.nodeType)===c&&this.Fi.observe(T)}I=null!==(u=v.textContent)&&void 0!==u?u:"";break;case c:var C=v,x=this.getWatchState(C,w,!!E.shadowRootType,t);if(null!=x){E.watchKind=x;var R=!1;x.has(re.Watch)&&(R=!0,null===(o=this.Bi)||void 0===o||o.observe(C)),x.has(re.Unmask)&&(E.mask=!1),x.has(re.Mask)&&(E.mask=!0),(x.has(re.Exclude)||x.has(re.Defer))&&(R=!0),R&&this.Fi.observe(C)}A=function(t,i,n){try{if(Qs&&"output"===n||!i.hasAttributes())return;try{if(_(Element.prototype.getAttributeNames))return function(t){for(var i,n={},r=0,e=null!==(i=Element.prototype.getAttributeNames.call(t))&&void 0!==i?i:[];r<e.length;r++){var s=e[r];jo(n,s,t.getAttribute(s))}return n}(i)}catch(t){}return function(t){for(var i,n,r={},e=null!==(n=null===(i=t.attributes)||void 0===i?void 0:i.length)&&void 0!==n?n:0,s=0;s<e;s++){var u=t.attributes[s];null!=u&&jo(r,u.name,u.value)}return r}(i)}catch(i){"get attrs failed: ".concat(i),Bs.send(t,"nodeEncoder#getAttrs",{err:i,tagName:n})}}(this.N.wdx,C,p)}}catch(t){Bs.send(this.N.wdx,"nodeEncoder#_encodeNode",{err:t})}if(!(null===(h=E.watchKind)||void 0===h?void 0:h.has(re.Exclude)))for(var K=0,j=e;K<j.length;K++){var M=j[K];try{M.preVisit&&M.preVisit(E,v)}catch(t){"pre-visitor failed: ".concat(t),Bs.send(this.N.wdx,"visitor.preVisit",{err:t})}}var P=[this.N.wdx,lo.New,E,v,A,I],O=this.Ki.enqueue(function(t){var i=[];return yo(a.N.wdx,r,e,P,function(t){!function(t,i){i[0],i[1];var n=i[2],r=i[3],e=i[4],s=i[5],u=n.shadowRootType,o=n.watchKind,h=n.name,a=n.type,v=n.mask,l=Ue(r),d=e;if(10===a){d||(d={});var p=r;d.name=p.name||"",d.publicId=p.publicId||"",d.systemId=p.systemId||""}!function(t,i,n,r,e,s,u,o,h){switch(i){default:t.push("<".concat(n));break;case 10:t.push("<!DOCTYPE"),Le(t,u);break;case 11:case f:var a=void 0;a=r||n,t.push("<".concat(a));break;case 8:t.push("<object",":_fs_comment_ignored","");break;case 3:t.push("<".concat(n),null!=s?s:"");break;case c:if(a=n,h&&(a="svg:".concat(a)),"script"===a){t.push("<object",":_fs_script_ignored","");break}t.push("<".concat(a)),e&&(e.has(re.Exclude)&&t.push(":".concat("_fs_excluded"),"true"),e.has(re.Defer)&&t.push(":".concat("_fs_deferred"),"true")),o&&t.push(":".concat("_fs_masked"),"true"),Le(t,u)}}(t,a,h,u,o,s,d,!!v,l)}(i,t)}),{done:!0,result:i}},!0);return Ws(this.N.wdx,"function"==typeof O,"nodeEnc#resolver"),O&&n.push(function(){var t=O();return t&&t[0]||[]}),E},t.prototype.getWatchState=function(t,i,n,r){return n||null==r||Ro[i]?this.Ui.isWatched(t):r.get(t)},t}();function jo(t,i,n){null!=i&&null!=n&&(t[i]="".concat(n))}var Mo=[{Selector:"input[type=password]",Consent:!1,Type:1},{Selector:"input[type=hidden]",Consent:!1,Type:1},{Selector:"[autocomplete^=cc-]",Consent:!1,Type:1},{Selector:"object:not([type^=\"image/\"])",Consent:!1,Type:1},{Selector:"embed:not([type^=\"image/\"])",Consent:!1,Type:1},{Selector:"noscript",Consent:!1,Type:1},{Selector:".fs-hide",Consent:!1,Type:1},{Selector:".fs-exclude",Consent:!1,Type:1},{Selector:".fs-exclude-without-consent",Consent:!0,Type:1},{Selector:".fs-mask",Consent:!1,Type:2},{Selector:".fs-mask-without-consent",Consent:!0,Type:2},{Selector:".fs-unmask",Consent:!1,Type:3},{Selector:".fs-unmask-with-consent",Consent:!0,Type:3},{Selector:".fs-block",Consent:!1,Type:1},{Selector:".fs-record-with-consent",Consent:!0,Type:1}],Po=function(t,i){return t|i},Oo=we.reduce(Po,0),No=pe.reduce(Po,0),Lo=function(){function t(t){void 0===t&&(t=0),this.zi=t}return t.prototype.has=function(t){return!!(this.zi&t)},t.prototype.set=function(i){if(this.zi&No&&i&No){var n=this.zi|i,r=t.qi(n);this.zi=t.Vi(r,this.zi)}else this.zi|=i},t.prototype.hasKinds=function(){return 0!==this.zi},t.areEqual=function(t,i){return null==t&&null==i||null!=t&&null!=i&&t.equals(i)},t.prototype.getStrictestPrivacyKind=function(){return t.qi(this.zi)},t.qi=function(t){for(var i=0,n=pe;i<n.length;i++){var r=n[i];if(t&r)return r}return null},t.needsToObserve=function(t,i){var n,r,e=null!==(n=null==t?void 0:t.zi)&&void 0!==n?n:0;return!!((null!==(r=null==i?void 0:i.zi)&&void 0!==r?r:0)&~e&Oo)},t.combineKindsPreservePrivacy=function(i,n){var r,e,s=t.Vi(null!==(r=null==i?void 0:i.zi)&&void 0!==r?r:0,null!==(e=null==n?void 0:n.zi)&&void 0!==e?e:0);return new t(s)},t.Vi=function(t,i){return t&No|i&Oo},t.prototype.equals=function(t){return!!t&&this.zi===t.zi},t}();function Uo(t,i){var n=document.documentElement||document.createElement("div");try{return t.elMatches(n,i),!0}catch(n){return Bs.send(t,"Browser rejected rule",{selector:i,err:n},"warning"),!1}}var Fo={1:"exclude",2:"mask",3:"unmask",4:"watch",5:"keep",6:"defer"},Bo=function(){function t(t){this.ft=t,this.$i=Do(),this.Gi=Do()}return t.prototype.forConsentState=function(t){return t?this.$i:this.Gi},t.prototype.addElementBlockRules=function(t){var i=this,n=Do(),r=Do();t.map(zo).filter(function(t){return Wo(t.selector)}).forEach(function(t){if(t.consent)return t.kind===re.Unmask?void n[t.kind].push(t):void r[t.kind].push(t);n[t.kind].push(t),r[t.kind].push(t)});for(var e=document.documentElement||document.createElement("div"),s=function(t,n){try{if(0===t.length)return;var r=t.map(function(t){return t.selector}).join(", ");i.ft.elMatches(e,r),n.push(r)}catch(n){Bs.send(i.ft,"Browser rejected optimistic merge rule",{err:n,ruleCount:t.length},"warning"),i.Bt(t)}},u=0,o=ge;u<o.length;u++){var h=o[u];s(n[h],this.$i[h]),s(r[h],this.Gi[h])}},t.prototype.addRule=function(t,i,n){if(this.Qi(t,i,n))return!0;switch(t){case re.Watch:case re.Unmask:case re.Keep:case re.Defer:break;case re.Mask:case re.Exclude:default:this.$i[t].length=0,this.$i[t].push("*"),this.Gi[t].length=0,this.Gi[t].push("*")}return!1},t.prototype.Qi=function(t,i,n){try{if(Wo(n)){if(i){var r=this.Xi(t);return qo(this.ft,t,r,n)}return qo(this.ft,t,this.Gi,n)&&qo(this.ft,t,this.$i,n)}return!0}catch(t){return Bs.send(this.ft,"Error adding block rule",{selector:n,err:t}),!1}},t.prototype.addElementBlock=function(t){var i=zo(t),n=i.kind,r=i.consent,e=i.selector;return this.addRule(n,r,e)},t.prototype.Bt=function(t){for(var i=0,n=t;i<n.length;i++){var r=n[i],e=r.kind,s=r.consent,u=r.selector;this.addRule(e,s,u)}},t.prototype.Xi=function(t){var i=t===re.Unmask;return this.forConsentState(i)},t}(),Ho=function(){return Object.create?Object.create(null):{}};function Do(){for(var t=Ho(),i=0,n=ge;i<n.length;i++)t[n[i]]=[];return t}function Wo(t){return!t.match(fe)&&""!=t.trim()}function zo(t){var i=re.Exclude;switch(t.Type){case Jt.Unset:case Jt.Exclude:i=re.Exclude;break;case Jt.Mask:i=re.Mask;break;case Jt.Unmask:i=re.Unmask;break;case Jt.Watch:i=re.Watch;break;case Jt.Keep:i=re.Keep;break;case Jt.Defer:i=re.Defer;break;default:jr(t.Type,"Unexpected block type: ".concat(t.Type))}return{kind:i,consent:t.Consent,selector:t.Selector}}function qo(t,i,n,r){var e=document.documentElement||document.createElement("div"),s=i;switch(s){case re.Exclude:case re.Mask:case re.Unmask:case re.Watch:case re.Keep:case re.Defer:break;default:s=re.Exclude}if(0==n[s].length)return!!Uo(t,r)&&(n[s].push(r),!0);var u=n[s].length-1,o=n[s][u].concat(", ",r);try{t.elMatches(e,o)}catch(i){return!!Uo(t,r)&&(n[s].push(r),Bs.send(t,"Browser rejected merged rule",{kind:Fo[s],selector:r,err:i},"warning"),!0)}return n[s][u]=o,!0}function Vo(t){if(!(null==t?void 0:t.hasKinds()))return 0;var i=t.getStrictestPrivacyKind();return null===i?0:pe.length-pe.indexOf(i)}var $o=function(){function t(t,i){void 0===i&&(i=new Bo(t)),this.ft=t,this.Yi=i,this.Ji=!Ys}return t.prototype.initialize=function(t){var i=t.blocks,n=t.deferreds,r=t.keeps,e=t.watches,u=t.canvasWatcherMode,o=t.noDefaultExclusions?[]:s([],Mo,!0);if(i)for(var h=0,a=i;h<a.length;h++){var c=a[h];o.push(c)}if((null!=u?u:pi.Disabled)===pi.Disabled&&o.push({Consent:!1,Selector:"canvas",Type:Jt.Exclude}),e)for(var f=0,v=e;f<v.length;f++){var l=v[f];o.push({Type:Jt.Watch,Consent:wt.RevokeConsent,Selector:l.Selector})}if(n)for(var d=0,p=n;d<p.length;d++){var w=p[d];o.push({Type:Jt.Defer,Consent:wt.RevokeConsent,Selector:w.Selector})}if(this.Yi.addElementBlockRules(o),r)for(var g=0,m=r;g<m.length;g++){var y=m[g];this.addElementKeep(y)}},t.prototype.isWatched=function(t){var i,n=new Lo,r=this.Yi.forConsentState(null!==(i=this.Zi)&&void 0!==i?i:wt.RevokeConsent),e=function(t,i,n,r){return Go(t,i,n,r,!0)}(this.ft,t,pe,r),s=e|Go(this.ft,t,we,r);return n.set(s),n.hasKinds()?n:null},t.prototype.addElementBlock=function(t){return this.Yi.addElementBlock(t)},t.prototype.addElementKeep=function(t){var i=re.Keep;return t.Type===ti.Click&&this.tn(i,t.Consent,t.Selector)},t.prototype.tn=function(t,i,n){return this.Yi.addRule(t,i,n)},t.prototype.getConsent=function(){return this.Zi},t.prototype.initializeConsent=function(t){void 0===this.Zi&&this.nn(t,!1)},t.prototype.setConsent=function(t){this.nn(t,!0)},t.prototype.nn=function(t,i){void 0===i&&(i=!0),this.Zi!==t&&(this.Zi=t,i&&this.onConsentChange&&this.onConsentChange())},t.prototype.allWatchedElements=function(t){var i;if(!this.Ji)return null;for(var n=new WeakMap,r=!1,e=function(t,i){var e,s=null!==(e=n.get(t))&&void 0!==e?e:new Lo;s.set(i),n.set(t,s),r=!0},s=this.Yi.forConsentState(null!==(i=this.Zi)&&void 0!==i&&i),u=0,o=ge;u<o.length;u++)for(var h=o[u],a=0,c=s[h];a<c.length;a++){var f=c[a];Xo(t)&&this.ft.elMatches(t,f)&&e(t,h);for(var v=Qo(this.ft,t,f),l=0;l<v.length;l++)e(v[l],h)}return r?n:null},t}();function Go(t,i,n,r,e){void 0===e&&(e=!1);for(var s=0,u=0,o=n;u<o.length;u++){for(var h=o[u],a=!1,c=0,f=r[h];c<f.length;c++){var v=f[c];if(t.elMatches(i,v)){s|=h,a=!0;break}}if(a&&e)break}return s}function Qo(t,i,n){return Xo(i)?t.elQuerySelectorAll(i,n):function(t){return t.nodeType===f}(i)?t.docQuerySelectorAll(i,n):function(t){return 11===t.nodeType}(i)?t.docFragQuerySelectorAll(i,n):i.querySelectorAll(n)}function Xo(t){return t.nodeType===c}var Yo=function(){function t(t){this.N=t,this.rn={},this.en={},this.sn={},this.un={}}return t.create=function(t){var i,n=Jo.isSupported(t.window);return null===(i=t.queue())||void 0===i||i.enqueue({Kind:Y.REC_FEAT_SUPPORTED,Args:[Rt.ResizeObserver,n]}),n?new Jo(t):new Zo(t)},t.prototype.collect=function(t){var i=[];for(var n in this.en)this.on(t,i,parseInt(n,10));return this.en={},i},t.prototype.isObserved=function(t){return!!this.rn[t]},t.prototype.hn=function(t,i){try{var n=Ke(t);if(!n)return;if(t.nodeType!=c)return;var r=t,e=function(t,i,n){if((null==i?void 0:i.nodeType)!==c)return{width:0,height:0};if(!n||t.arrayIsArray(n)&&0===n.length)return i.getBoundingClientRect();var r=Array.isArray(n)?n[0]:n;switch(getComputedStyle(i).writingMode){case"horizontal-tb":return{width:r.inlineSize,height:r.blockSize};case"vertical-lr":case"vertical-rl":return{width:r.blockSize,height:r.inlineSize};default:return i.getBoundingClientRect()}}(this.N.wdx,r,i);this.en[n]=e;var s=this.N.queue();if(!this.rn[n]&&void 0!==s)for(var u=this.an(n),o=0,h=u;o<h.length;o++){var a=h[o];s.enqueue(a)}}catch(t){}},t.prototype.on=function(t,i,r){for(var e=0,s=this.an(r);e<s.length;e++){var u=s[e];i.push(n(n({},u),{When:t}))}},t.prototype.an=function(t){var i=this.en[t];if(!i)return[];var n=xe(t);if(!n)return[];var r=n.watchKind,e=i.width,s=i.height,u=this.rn[t];if(u&&u.width==e&&u.height==s)return[];this.rn[t]={width:e,height:s};var o=[];if(null==r?void 0:r.has(re.Watch)){var h=0!=e&&0!=s;(!!u&&0!=u.width&&0!=u.height)!=h&&o.push({Kind:Y.WATCHED_ELEM,Args:[t,h]})}var a=Re(n.id),c=a&&"scrollLeft"in a;if((null==r?void 0:r.has(re.Exclude))||(null==r?void 0:r.has(re.Defer))||n.mask||c){var f=this.sn[t],v=0===e&&0===s;if(v&&(!f||0===f.width&&0===f.height)||(o.push({Kind:Y.PLACEHOLDER_SIZE,Args:[t,e,s]}),v?delete this.sn[t]:this.sn[t]={width:e,height:s}),c){var l=a.scrollWidth,d=a.scrollHeight,p=this.un[t],w=0===l&&0===d;w&&(!p||0===p.width&&0===p.height)||(o.push({Kind:Y.RESIZE_SCROLLABLE_ELEMENT_CONTENT,Args:[t,l,d]}),w?delete this.un[t]:this.un[t]={width:l,height:d})}}return o},t}(),Jo=function(t){function n(i){var n=t.call(this,i)||this;return n.cn=new WeakMap,n.vn=new WeakMap,n.ln=new i.window.ResizeObserver(function(t){xr(i.wdx,i.window).then(function(){for(var i=0,r=t;i<r.length;i++){var e=r[i],s=e.target,u=e.borderBoxSize;n.hn(s,u)}})}),n.dn=new i.window.ResizeObserver(function(t){xr(i.wdx,i.window).then(function(){for(var i=0,r=t;i<r.length;i++){var e=r[i].target;n.pn(e)}})}),n}return i(n,t),n.isSupported=function(t){return"function"==typeof t.ResizeObserver},n.prototype.observe=function(t){var i=this;if(t&&t.nodeType==c)try{var n=t;this.ln.unobserve(n),this.ln.observe(n),this.N.measurer.enqueue(function(){i.wn(n)})}catch(t){"ResizeWatcher.observe: caught exception ".concat(t),qs(this.N.wdx,"resize.observe",{err:t})}},n.prototype.unobserveSubtree=function(t){try{t&&t.nodeType===c&&(this.ln.unobserve(t),this.dn.unobserve(t))}catch(t){}},n.prototype.nodeChanged=function(t){var i=this,n=this.vn.get(t);"number"==typeof n&&Ke(t)===n&&this.N.measurer.enqueue(function(){i.hn(t)})},n.prototype.pn=function(t){var i=this.cn.get(t);if(i)for(var n in i){var r=xe(i[n]);if(r){var e=Re(r.id);e&&this.hn(e)}else delete i[n]}},n.prototype.wn=function(t){var i=this,n=Ke(t);if(n){var r=function(t,i){for(var n=0,r=i;;){if(n++>1e3)return null;if(!r||r.nodeType!=c)return null;var e=r;if(getComputedStyle(e).display.indexOf("inline")<0)return e;r=m(0,r)}}(this.N.wdx,t);if(r&&r!==t){this.vn.set(t,n),this.hn(t);var e=this.cn.get(r);e||(e=Object.create(null),this.cn.set(r,e)),e[n]=n,this.N.wdx.setWindowTimeout(this.N.window,h(function(){i.dn.unobserve(r),i.dn.observe(r)}),0)}}},n}(Yo),Zo=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return i(n,t),n.prototype.observe=function(t){var i=this;if(t&&t.nodeType==c){var n=t;!function(t){if(t){var i=Re(t.id);if(i&&Ie(i,t))for(var n=t,r=t.parent;r;r=r.parent){if(th(r)||(r.watchedChildren={}),th(n))for(var e in th(n))delete th(r)[e];if(th(r)[n.id]=n,M(th(r),2))n=r;else if(P(th(r),2))break}}}(Ce(t)),this.N.measurer.enqueue(function(){i.hn(n)})}},n.prototype.unobserveSubtree=function(t){var i=Ce(t);i&&function(t){var i=Re(t.id);if(i&&(P(th(t),0)||Ie(i,t)))for(var n=th(t)&&P(th(t),1)||Ie(i,t)?t.id:function(t){for(var i in t)if(Object.prototype.hasOwnProperty.call(t,i))return i}(th(t)),r=n?t.parent:null;r&&th(r)&&th(r)[n];){if(delete th(r)[n],M(th(r),1)){var e=r.id,s=j(th(r));for(r=r.parent;r&&th(r)&&th(r)[e];)delete th(r)[e],th(r)[s.id]=s,r=r.parent;break}r=r.parent}}(i)},n.prototype.nodeChanged=function(t){var i=this,n=function(t,i){var n=[],r=Ce(i);if(r)for(var e=[r],s=0;e.length&&++s<1e3;){var u=e.pop(),o=Re(u.id);o&&Ie(o,u)&&n.push(o),th(u)&&K(t,th(u),function(t){e.push(t)})}else{for(var h=i;h&&!Ke(h);)h=m(0,h);h&&Ie(h)&&n.push(h)}return n}(this.N.wdx,t);this.N.measurer.enqueue(function(){for(var t=0,r=n;t<r.length;t++){var e=r[t];i.hn(e)}})},n}(Yo);function th(t){return t.watchedChildren}var ih={attributeName:null,attributeNamespace:null,addedNodes:[],removedNodes:[],nextSibling:null,previousSibling:null,oldValue:null};function nh(t){return n(n(n({},ih),t),{type:"childList"})}function rh(t,i,n){return 0===n.length?nh({target:i}):nh({addedNodes:n,nextSibling:w(0,n[n.length-1]),previousSibling:g(0,n[0]),target:i})}function eh(t,i){var n=[];return d(0,i,function(t){n.push(t)}),rh(0,i,n)}var sh,uh,oh,hh={sanitize:function(t){var i,n=t[0],r=(t[1],t[2]),e=t[3],s=t[4],u=t[5];if(s){var o={};for(var h in s){var a=s[h],c=Ne[i=h]||i.toLowerCase(),f=De(n,r,e,c,a);null!==f&&(o[c]=f)}t[wo.Attrs]=o}if(u){var v=function(t,i,n){if(!n)return"";if(i.parent&&"style"===i.parent.tag){var r=Re(i.parent.id);if(r&&!Ue(r))return""}var e=n.length;return e>Fe?(Bs.send(t,"Ignoring huge text node",{length:e,max:Fe},"warning"),""):i.mask?ne(n):n}(n,r,u);t[wo.Text]=v}}};function ah(t,i){return sh||(sh=function(t,i){var n,r;try{if(n=null==(r=i.MutationObserver)?void 0:r.prototype.constructor,vn(i))return r;var e=eu,s=lu(Function.prototype.toString,n,e),u=s[0],o=s[1];if(u)return u;var h=lu(Function.prototype.toString,r,e),a=h[0],c=h[1];return a||o[0]||c[0]||n||r}catch(t){return n||r||void 0}}(0,i)),sh}(oh=uh||(uh={}))[oh.Unknown=0]="Unknown",oh[oh.Clean=1]="Clean",oh[oh.Mixed=2]="Mixed",oh[oh.UnrecoverableFailure=3]="UnrecoverableFailure";var ch=function(){function t(t){this.rebuildFromSnapshot(t)}return t.create=function(i){return new t(fh(i))},t.prototype.rebuildFromSnapshot=function(t){var i=this.snapshot;if(this.snapshot=t,!i||i.functions!==t.functions){var n=t.functions;this.arrayIsArray=n.arrayIsArray,this.clearWindowInterval=vh(n.clearWindowInterval),this.clearWindowTimeout=vh(n.clearWindowTimeout),this.dateGetTime=vh(n.dateGetTime),this.dateNow=n.dateNow,this.docFragQuerySelectorAll=vh(n.docFragQuerySelectorAll),this.docQuerySelectorAll=vh(n.docQuerySelectorAll),this.elMatches=vh(n.elMatches),this.elQuerySelectorAll=vh(n.elQuerySelectorAll),this.jsonParse=n.jsonParse,this.jsonStringify=n.jsonStringify,this.matchMedia=lh(n.matchMedia),this.mathAbs=n.mathAbs,this.mathFloor=n.mathFloor,this.mathMax=n.mathMax,this.mathMin=n.mathMin,this.mathPow=n.mathPow,this.mathRandom=n.mathRandom,this.mathRound=n.mathRound,this.objectHasOwnProp=vh(n.objectHasOwnProp),this.objectKeys=n.objectKeys,this.objectValues=n.objectValues||null,this.requestWindowAnimationFrame=lh(n.requestWindowAnimationFrame),this.requestWindowIdleCallback=lh(n.requestWindowIdleCallback),this.setWindowInterval=vh(n.setWindowInterval),this.setWindowTimeout=vh(n.setWindowTimeout),this.inputGetValue=lh(n.inputGetValue),this.mutationObserve=lh(n.mutationObserve),this.mutationDisconnect=lh(n.mutationDisconnect),this.snapshot.dirty=t.dirty}},t}();function fh(t,i){void 0===i&&(i=uh.Unknown);var n=i,r=[],e=function(t){return n=uh.UnrecoverableFailure,r.push("Snapshot failed: ".concat(t)),function(){throw new Error("Invoked failed snapshot")}},s=function(t){try{return t()}catch(t){return e(t.message)}},u=function(t){try{return t()||e("snapshot not found")}catch(t){return e(t.message)}},o={arrayIsArray:s(function(){return t.Array.isArray}),clearWindowInterval:s(function(){return t.clearInterval}),clearWindowTimeout:s(function(){return t.clearTimeout}),dateGetTime:s(function(){return t.Date.prototype.getTime}),dateNow:s(function(){return t.Date.now}),docFragQuerySelectorAll:u(function(){var i;return null===(i=t.DocumentFragment)||void 0===i?void 0:i.prototype.querySelectorAll}),docQuerySelectorAll:u(function(){var i;return null!==(i=t.Document.prototype.querySelectorAll)&&void 0!==i?i:t.document.querySelectorAll}),elMatches:u(function(){return wh(t,dh)}),elQuerySelectorAll:u(function(){return wh(t,ph)}),jsonParse:s(function(){return t.JSON.parse}),jsonStringify:s(function(){return t.JSON.stringify}),matchMedia:s(function(){return t.matchMedia}),mathAbs:s(function(){return t.Math.abs}),mathFloor:s(function(){return t.Math.floor}),mathMax:s(function(){return t.Math.max}),mathMin:s(function(){return t.Math.min}),mathPow:s(function(){return t.Math.pow}),mathRandom:s(function(){return t.Math.random}),mathRound:s(function(){return t.Math.round}),objectHasOwnProp:s(function(){return t.Object.prototype.hasOwnProperty}),objectKeys:s(function(){return t.Object.keys}),objectValues:s(function(){return t.Object.values}),requestWindowAnimationFrame:s(function(){return t.requestAnimationFrame}),requestWindowIdleCallback:s(function(){return t.requestIdleCallback}),setWindowInterval:s(function(){return t.setInterval}),setWindowTimeout:s(function(){return t.setTimeout}),mutationObserve:s(function(){var i;return null===(i=t.MutationObserver)||void 0===i?void 0:i.prototype.observe}),mutationDisconnect:s(function(){var i;return null===(i=t.MutationObserver)||void 0===i?void 0:i.prototype.disconnect}),inputGetValue:s(function(){var i;return t.HTMLInputElement&&(null===(i=Object.getOwnPropertyDescriptor(t.HTMLInputElement.prototype,"value"))||void 0===i?void 0:i.get)})},h={functionToString:s(function(){return t.Function.prototype.toString}),objectToString:s(function(){return t.Object.prototype.toString})};return{status:n,functions:o,helpers:h,errors:r}}function vh(t){return function(i){for(var n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];return t.apply(i,n)}}function lh(t){return t?vh(t):null}function dh(t){return t.matches||t.webkitMatchesSelector||t.msMatchesSelector||t.mozMatchesSelector}function ph(t){return t.querySelectorAll}function wh(t,i){var n,r,e=(null===(n=t.Element)||void 0===n?void 0:n.prototype)?i(null===(r=t.Element)||void 0===r?void 0:r.prototype):void 0,s=t.document?t.document.documentElement:void 0;if(!s||e&&s instanceof t.Element||(e=i(s)),!e)throw new Error("Unable to find Element proto function");return e}function gh(t,i){for(var n=0;n<i.length;n++){var r=i[n];if(r.Kind===Y.RESOLVABLE_EVENT){var e=r.Args,s=e[0],u=e[1];Ws(t,"function"==typeof u,"resolveEvents"),i[n].Kind=s,i[n].Args=u()}Ws(t,r.Kind!==Y.RESOLVABLE_EVENT,"resolveEvents")}return i}function mh(t){for(var i=[],n=0,r=t;n<r.length;n++){var e=r[n];if("function"==typeof e)for(var s=0,u=e();s<u.length;s++){var o=u[s];i.push(o)}else i.push(e)}return i}var yh=function(){function t(t,i,n,r,e,s){void 0===n&&(n=function(){}),void 0===e&&(e=function(){}),void 0===s&&(s=function(){});var u=this;this.N=t,this.Ui=i,this.Bi=r,this.gn=e,this.mn=s,this.yn=!1,this.bn=[],this.Sn=[],this.kn=[],this._n=[],this.An=[],this.In=!1,this.En=[],this.Tn=[],this.vt=this.N.window,this.acceptSanitizer(hh),this.acceptVisitor(this),this.Fi=Yo.create(this.N),this.Hi=function(t){var i=Re(t.id);Sh(u.N.wdx,i)&&u.Fi.unobserveSubtree(i),n(t)},this.Cn=new Ko(this.N,i,this.Fi,this.Bi,this.Hi.bind(this)),Ws(this.N.wdx,!this.Ui.onConsentChange,"mutwatcher#onConsentChange"),this.Ui.onConsentChange=function(){return u.updateConsent()}}return t.prototype.start=function(t){void 0===t&&(t=this.vt.document),this.Ci=t,this.yn=!1;var i=!0;if($s)try{this.xn()}catch(t){"Error setting up IE workarounds for mutation watcher: ".concat(t),i=!1}if(i){var n=ah(this.N.wdx,this.N.window);this.Rn=new n(this.Kn.bind(this))}},t.prototype.Kn=function(t){for(var i=0,n=t;i<n.length;i++){var r=n[i];this.An.push(r)}},t.prototype.resizer=function(){return this.Fi},t.prototype.stop=function(){if(this.Rn){var t=this.N.wdx.mutationDisconnect;t?t(this.Rn):this.Rn.disconnect()}var i=Ce(this.Ci);i&&Oe(this.N.wdx,i,this.Hi.bind(this)),this.An=[],$s&&this.jn(),this.Ui.onConsentChange=null,this.Mn&&(this.Mn.disable(),this.Mn=null)},t.prototype.processMutations=function(t){var i=this;if(!this.Ci)return[];this.recordingIsDetached()&&qs(this.N.wdx,"Recording is detached",{inFrame:this.N.recording.inFrame,top:this.N.window.top===this.N.window});var n=[],r=!1,e=null,u=function(s){return s||r?(r||(e=i.Pn(t,n,i.Ci),r=!0),e):null};if(this.On(t,n,u),this.In&&(n.push({Kind:Y.FAIL_THROTTLED,Args:[Pt.SetPropertyHooks],When:t}),this.In=!1),this.An.length>0||this.Sn.length>0){var o=this.Nn(t,n,u),h=o[0],a=o[1];for(var c in a)n.push({Kind:Y.MUT_ATTR,Args:a[c],When:t});for(var c in h)n.push({Kind:Y.MUT_TEXT,Args:h[c],When:t})}var f=this.bn;this.bn=[];for(var v=0;v<f.length;v++){var l=f[v].shadowRoot;l&&0!=Ke(f[v])&&0==Ke(l)&&(this.Ln(l),this.Un(null,t,n,f[v],l))}if(n.push.apply(n,this.Fi.collect(t)),this.An=[],this._n.length>0&&(n.push({Kind:Y.DEFERRED_RESOLVED,Args:s([],this._n,!0),When:t}),this._n=[]),this.kn.length>0){for(var d=0,p=this.kn;d<p.length;d++){var w=p[d];this._n.push(Ke(w)),b(this.N.wdx,w)&&this.An.push(eh(this.N.wdx,w))}this.kn=[]}return n},t.prototype.recordingIsDetached=function(){return!!this.Ci&&this.Ci!=this.vt.document},t.prototype.On=function(t,i,n){if(!this.yn&&this.Ci){window;var r=n(!0);this.Fn(r,t,i,null,this.Ci,null),this.Fi.nodeChanged(this.Ci),this.Rn&&this.Ln(this.Ci),this.Bn(),this.yn=!0,window}},t.prototype.Pn=function(t,i,n){if(!n)return null;var r=Q(this.N.wdx),e=this.Ui.allWatchedElements(n),s=Q(this.N.wdx)-r;return i.push({Kind:Y.TIMING,Args:[[it.Internal,rt.Serialization,st.ApplyRules,t,s]],When:t}),e},t.prototype.Bn=function(){var t=this;this.Mn=Su(this.N,Element.prototype,"attachShadow",!0),this.Mn&&this.Mn.before(function(i){i.that.shadowRoot||t.bn.push(i.that)})},t.prototype.Ln=function(t){var i;try{var n={childList:!0,attributes:!0,characterData:!0,subtree:!0,attributeOldValue:!0,characterDataOldValue:!0},r=this.N.wdx.mutationObserve;r?r(this.Rn,t,n):null===(i=this.Rn)||void 0===i||i.observe(t,n)}catch(t){}},t.prototype.Nn=function(t,i,n){for(var r,e,s=this,u={},o={},h={},a={},c=function(n){try{if(!Ce(n))return;s.Hn(t,i,Ce(n));var r=Ce(m(s.N.wdx,n));if(r){var e=Re(r.id);Sh(s.N.wdx,e)&&(a[r.id]=e)}}catch(t){Bs.send(s.N.wdx,"mutationwatcher#refreshElem",{err:t})}},f=function(n){var s,f;try{var l=v.An[n],d=Ke(l.target);if(!d)return"continue";switch(h[d]=l.target,l.type){case"childList":if("style"===me(l.target)&&!Ue(l.target)){c(l.target);break}if(l.removedNodes.length>0)for(var p=0;p<l.removedNodes.length;++p)(I=Ce(l.removedNodes[p]))&&I.id&&v.Hn(t,i,I);l.addedNodes.length>0&&(a[d]=l.target);break;case"characterData":if((I=Ce(l.target))&&!Ee(l.target,I)){var w=l.target.textContent;if(l.oldValue===w)break;var g=m(v.N.wdx,l.target);if(g&&"style"===me(g)&&!Ue(g))c(g);else{var y=[v.N.wdx,lo.Update,I,l.target,void 0,null!=w?w:void 0];yo(v.N.wdx,v.En,v.Tn,y,function(t){var i;u[d]=[d,null!==(i=t[wo.Text])&&void 0!==i?i:""]})}}break;case"attributes":var b=l.target,S=me(b);if(l.attributeNamespace==Ti){"style"==S&&l.attributeName==Ci&&v.mn(b);break}if("link"===S&&"rel"===l.attributeName&&Be.test(null!==(r=l.oldValue)&&void 0!==r?r:"")){c(b);break}var k=function(t,i){return void 0===i&&(i=Ce(t)),null==i?void 0:i.watchKind}(b),_=v.Ui.isWatched(b);if(Vo(_)>Vo(k)){c(b);break}if(Lo.needsToObserve(k,_)){v.Fi.observe(b),(null==_?void 0:_.has(re.Watch))&&(null===(e=v.Bi)||void 0===e||e.observe(b));var A=Ce(b);A&&(A.watchKind=Lo.combineKindsPreservePrivacy(k,_))}var I,E=(void 0===(f=l.attributeNamespace)&&(f=""),(null===f?"":{"http://www.w3.org/1999/xlink":"xlink:","http://www.w3.org/XML/1998/namespace":"xml:","http://www.w3.org/2000/xmlns/":"xmlns:"}[f]||"")+(l.attributeName||"")),T=l.target.getAttribute(E);(I=Ce(l.target))&&l.oldValue!=T&&(y=[v.N.wdx,lo.Update,I,l.target,(s={},s[E]=T||"",s),void 0],yo(v.N.wdx,v.En,v.Tn,y,function(t){var i,n=null!==(i=t[wo.Attrs])&&void 0!==i?i:{};for(var r in n){var e=n[r];b.hasAttribute(r)||(e=null),o["".concat(d," ").concat(r)]=[d,r,e]}}))}}catch(t){}},v=this,l=0;l<this.An.length;++l)f(l);for(var d=0,p=this.Sn;d<p.length;d++){var w=p[d];c(w)}for(var g in this.Sn=[],a){var y=Ce(b=a[g]);y&&y.id&&this.Dn(n,t,i,b,y)}for(var g in h){var b=h[g];this.Fi.nodeChanged(b)}return[u,o]},t.prototype.Wn=function(t){return this.yn&&t&&this.Ci?(this.zn(this.Ci),[]):[]},t.prototype.zn=function(t){var i=this,n=m(this.N.wdx,t);Ke(t)||!n?d(this.N.wdx,t,function(t){i.zn(t)}):this.An.push(rh(this.N.wdx,n,[t]))},t.prototype.Dn=function(t,i,n,r,e){var s=this,u=[],o=e.child;for(d(this.N.wdx,r,function(t){if(o)for(var i=Ce(t);o;){if(!Ke(t)){var n=Re(o.id);Sh(s.N.wdx,n)&&u.push({insert:[r,t,n]});break}if(i&&o.id==i.id){o=o.next;break}u.push({remove:o}),o=o.next}else u.push({insert:[r,t,null]})});o;o=o.next)u.push({remove:o});var h=!1,a=t(h);if(null===a&&this.N.recording.flags().UseRuleOptimization){try{for(var c=0,f=0;f<u.length&&!h;f++)(l=u[f]).insert&&(c+=bh(this.N.wdx,l.insert[1]))>=1e3&&(h=!0)}catch(t){qs(this.N.wdx,"ruleOpt",{err:t})}h&&(a=t(h))}var v=!1;for(f=0;f<u.length;f++){var l;(l=u[f]).insert?this.Fn(a,i,n,l.insert[0],l.insert[1],l.insert[2]):l.remove&&(v=!0,this.Hn(i,n,l.remove))}Ws(this.N.wdx,!v,"mutwatcher#_diff")},t.prototype.Un=function(t,i,n,r,e){var s=Ke(r),u=Q(this.N.wdx),o=this.qn(t,r,e,null),h=Q(this.N.wdx)-u;o.length>0&&n.push({Kind:Y.RESOLVABLE_EVENT,Args:[Y.MUT_SHADOW,function(){return[s,mh(o)]}],When:i},{Kind:Y.TIMING,Args:[[it.Internal,rt.Serialization,st.NodeEncoding,i,h]],When:i})},t.prototype.Fn=function(t,i,n,r,e,s){var u=Ke(r)||-1,o=Ke(s)||-1,h=-1===u&&-1===o,a=Q(this.N.wdx);this.N.window;var c=this.qn(t,r,e,s);this.N.window;var f=Q(this.N.wdx)-a;c.length>0&&(n.push({Kind:Y.RESOLVABLE_EVENT,Args:[Y.MUT_INSERT,function(){return[u,o,mh(c)]}],When:i},{Kind:Y.TIMING,Args:[[it.Internal,rt.Serialization,h?st.DomSnapshot:st.NodeEncoding,i,f]],When:i}),this.gn())},t.prototype.qn=function(t,i,n,r){var e=this;return i&&Ee(i)?[]:this.Cn.tokenizeNode(t,i,r,n,this.En,this.Tn,function(t,i){switch(i){case se.Immediate:e.refreshElement(t);break;case se.Deferred:e.kn.push(t)}})},t.prototype.Hn=function(t,i,n){var r=n.id;if(Pe(this.N.wdx,n,this.Hi.bind(this)),i.length>0){var e=i[i.length-1];if(e.Kind==Y.MUT_REMOVE)return void e.Args.push(r)}i.push({Kind:Y.MUT_REMOVE,Args:[r],When:t})},t.prototype.xn=function(){var i=this;if(Gs){var r=Object.getOwnPropertyDescriptor(Node.prototype,"textContent"),e=r&&r.set;if(!r||!e)throw new Error("Missing textContent setter -- not safe to record mutations.");Object.defineProperty(Element.prototype,"textContent",n(n({},r),{set:function(t){try{for(var i=void 0;i=y(this.N.wdx,this);)this.removeChild(i);if(null===t||""==t)return;var n=(this.ownerDocument||document).createTextNode(t);this.appendChild(n)}catch(i){e&&e.call(this,t)}}}))}this.Vn=new zu(this.N.wdx,this.N.window,t.ThrottleMax,t.ThrottleInterval,function(){return new Wu(i.N.wdx,i.N.window,function(){i.In=!0,i.jn()}).start()});var s=this.Vn.guard(function(t){var i=t.cssText;t.cssText=i});this.Vn.open(),this.$n=Su(this.N,CSSStyleDeclaration.prototype,"setProperty"),this.$n&&this.$n.afterSync(function(t){s(t.that)}),this.Gn=Su(this.N,CSSStyleDeclaration.prototype,"removeProperty"),this.Gn&&this.Gn.afterSync(function(t){s(t.that)})},t.prototype.jn=function(){this.Vn&&this.Vn.close(),this.$n&&this.$n.disable(),this.Gn&&this.Gn.disable()},t.prototype.updateConsent=function(){var t=this;this.Ci&&d(this.N.wdx,this.Ci,function(i){return t.refreshElement(i)})},t.prototype.refreshElement=function(t){Ke(t)&&this.Sn.push(t)},t.prototype.acceptSanitizer=function(t){this.En.push(t)},t.prototype.acceptVisitor=function(t){this.Tn.push(t)},t.prototype.visit=function(t){},t.prototype.preVisit=function(t,i){if(t.type===c&&!Ee(i,t)){var n=i;n.shadowRoot&&this.Ln(n.shadowRoot)}},t.ThrottleMax=1024,t.ThrottleInterval=1e4,t}();function bh(t,i,n){void 0===n&&(n=!1);var r=1;if(!i||i.nodeType!==c)return r;var e=i;if(r+=t.elQuerySelectorAll(e,"*").length,n){var s=e.shadowRoot;s&&11===s.nodeType&&(r+=t.docFragQuerySelectorAll(s,"*").length)}return r}function Sh(t,i){return Ws(t,!!i,"MutationWatcher#nodeNotFound")}var kh,_h=function(t,i,r){try{if(-1!==PerformanceObserver.supportedEntryTypes.indexOf(t)){var e=new PerformanceObserver(function(t){mr.resolve().then(function(){i(t.getEntries())})}),s=n({type:t,buffered:!0},r);return e.observe(s),e}}catch(t){}},Ah=0,Ih=1/0,Eh=0;function Th(t,i){for(var n=0,r=i;n<r.length;n++){var e=r[n];e.interactionId&&(Ih=t.mathMin(Ih,e.interactionId),Eh=t.mathMax(Eh,e.interactionId),Ah=Eh?(Eh-Ih)/7+1:0)}}function Ch(t){"interactionCount"in performance||kh||(kh=_h("event",Th.bind(null,t),{type:"event",buffered:!0,durationThreshold:0}))}var xh="first-input",Rh="largest-contentful-paint",Kh="layout-shift",jh="longtask",Mh="event",Ph="mark",Oh="measure",Nh="navigation",Lh="paint",Uh="resource",Fh="long-animation-frame",Bh=function(){function t(t,i,n,r){this.N=t,this.Ft=i,this.et=n,this.Qn=r,this.Xn=!1,this.Yn=!1,this.Jn=ui.DefaultOrgSettings.MaxPerfMarksPerPage,this.Zn=0,this.tr=0,this.ir=!1,this.nr=[],this.rr=!1}return t.prototype.initialize=function(t){var i=t.resourceUploader,n=t.recTimings,r=t.recImgs,e=t.maxPerfMarksPerPage;this.er=i,this.Yn=n,this.Xn=r,this.Jn=e||ui.DefaultOrgSettings.MaxPerfMarksPerPage},t.prototype.start=function(){var t=this;this.Zn=0;var i=this.sr();this.ur(i);var n=window.performance;n&&(Ch(this.N.wdx),this.nr.length>0&&n.addEventListener&&n.removeEventListener&&this.et.add(n,"resourcetimingbufferfull",!0,function(){t.Ft.enqueue({Kind:Y.RESOURCE_TIMING_BUFFER_FULL,Args:[]})}),this.hr(),this.ar())},t.prototype.onLoad=function(){if(!this.ir){this.ir=!0;var t=window.performance;t&&t.timing&&this.cr(Et.Timing,t.timing,Ct.Timing)}},t.prototype.tick=function(){this.hr()},t.prototype.stop=function(){this.et.clearAll(),this.er=void 0;var t=[];if(this.nr.length>0){for(var i=0,n=this.nr;i<n.length;i++){var r=n[i];t.push.apply(t,Hh(r))}this.nr=[]}else window.performance&&"function"==typeof window.performance.getEntries&&(t=window.performance.getEntries());t.length>300&&(t=t.slice(0,300),this.Ft.enqueue({Kind:Y.RESOURCE_TIMING_BUFFER_FULL,Args:[]})),this.hr(),this.Li(t),this.N.taskQueue.flush()},t.prototype.Li=function(t){for(var i=this,n=function(t){r.N.taskQueue.enqueue(function(){return i.vr(t)})},r=this,e=0,s=t;e<s.length;e++)n(s[e])},t.prototype.sr=function(){var t=this;if(!window.PerformanceObserver||!window.performance)return[];"function"==typeof window.performance.getEntries&&this.Li(performance.getEntries());var i=[];return[Mh,xh,Rh,Kh,jh,Ph,Oh,Nh,Lh,Uh,Fh].forEach(function(n){var r={buffered:!0};n===Mh&&(r.durationThreshold=40);var e=_h(n,t.Li.bind(t),r);e&&t.nr.push(e),i.push([n,!!e])}),i},t.prototype.hr=function(){var t=window.performance;if((this.N.recording.inWebView||!this.N.recording.inFrame)&&t){var i=t.memory;if(i){var n=i.usedJSHeapSize-this.tr;(0===this.tr||this.N.wdx.mathAbs(n/this.tr)>.2)&&(this.cr(Et.Memory,i,Ct.Memory),this.tr=i.usedJSHeapSize)}}},t.prototype.ar=function(){var t,i={timeOrigin:(t=this.N.wdx,G(t).timeOrigin)};this.cr(Et.TimeOrigin,i,Ct.TimeOrigin)},t.prototype.vr=function(t){switch(t.entryType){case Mh:this.lr(),this.cr(Et.EventTiming,t,Ct.EventTiming);break;case xh:this.cr(Et.FirstInput,t,Ct.FirstInput);break;case Rh:this.cr(Et.LargestContentfulPaint,t,Ct.LargestContentfulPaint);break;case Kh:this.cr(Et.LayoutShift,t,Ct.LayoutShift);break;case jh:this.cr(Et.LongTask,t,Ct.Measure);break;case Fh:this.dr(t);break;case Ph:this.cr(Et.Mark,t,Ct.Measure);break;case Oh:this.cr(Et.Measure,t,Ct.Measure);break;case Nh:this.cr(Et.Navigation,t,Ct.Navigation,{name:Nh});break;case Lh:this.cr(Et.Paint,t,Ct.Measure);break;case Uh:this.pr(t)}},t.prototype.dr=function(t){var i=this;if(t.scripts)for(var n=0,r=t.scripts;n<r.length;n++){var e=r[n],s={sourceURL:"script"};"event-listener"===e.invokerType?s.invoker=function(t){return n=i.N.wdx,t.replace(/\[src=(.*)?\]/gi,function(t,i){return"[src=".concat($e(n,i,{source:"perfEntry",type:"other"}),"]")});var n}:"classic-script"!==e.invokerType&&"module-script"!==e.invokerType||(s.invoker="script"),this.cr(Et.ScriptTiming,e,Ct.ScriptTiming,s)}this.cr(Et.LongAnimationFrame,t,Ct.LongAnimationFrame)},t.prototype.pr=function(t){if(this.Yn){var i=t.initiatorType;(this.Xn||"img"!==i&&"image"!==i)&&this.cr(Et.Resource,t,Ct.Resource,{name:i})}},t.prototype.cr=function(t,i,n,r){if(void 0===r&&(r={}),!this.atLimit(t)){for(var e=[t],s=0,u=n;s<u.length;s++){var o=u[s],h=i[o];if(void 0===h&&(h=-1),o in r){var a=r[o];if(_(a))h=a(h);else{var c=$e(this.N.wdx,h,{source:"perfEntry",type:a}),f=h!==c;h=this.wr(t,i,c,f)}}"target"!==o&&"element"!==o||(h=Ke(h)),e.push(h)}this.Ft.enqueue({Kind:Y.PERF_ENTRY,Args:e})}},t.prototype.wr=function(t,i,n,r){if(t!==Et.Resource)return n;switch(i.initiatorType){case"":case"xmlhttprequest":case"fetch":case"script":case"beacon":return n;case"css":if(this.er&&!r){var e=qr("",n);this.er.uploadIfNeeded(this.N.window,e,"css")}return this.Qn.obfuscateUrl(n,"css");default:return this.Qn.obfuscateUrl(n)}},t.prototype.atLimit=function(t){switch(t){case Et.Mark:case Et.Measure:if(this.Zn>=this.Jn)return!0;this.Zn++}return!1},t.prototype.ur=function(t){for(var i=window.performance,n=[Rt.Performance,!!(null==i?void 0:i.timing),Rt.PerformanceEntries,"function"==typeof(null==i?void 0:i.getEntries),Rt.PerformanceMemory,!!(null==i?void 0:i.memory),Rt.PerformanceObserver,!!window.PerformanceObserver,Rt.PerformanceTimeOrigin,!!(null==i?void 0:i.timeOrigin)],r=0,e=t;r<e.length;r++){var s=e[r],u=s[0],o=s[1];switch(u){case Kh:n.push(Rt.LayoutShift,o);break;case xh:n.push(Rt.FirstInput,o);break;case Rh:n.push(Rt.LargestContentfulPaint,o);break;case jh:n.push(Rt.LongTask,o)}}this.Ft.enqueue({Kind:Y.REC_FEAT_SUPPORTED,Args:n})},t.prototype.lr=function(){var t=this;this.rr||(this.rr=!0,this.N.taskQueue.enqueue(function(){var i=kh?Ah:performance.interactionCount||0;t.Ft.enqueue({Kind:Y.PERF_ENTRY,Args:[Et.EventTimingCount,i]}),t.rr=!1}))},t}();function Hh(t){var i=[];return t.takeRecords&&(i=t.takeRecords()),t.disconnect(),i}function Dh(t){var i=0,n={id:i++,edges:{}};return t.split("\n").forEach(function(t){var r=t.trim();if(""!=r){if(0==r.indexOf("/")||r.lastIndexOf("/")==r.length-1)throw new Error("Leading and trailing slashes are not supported");var e=n,s=r.split("/");s.forEach(function(t,n){var r=t.trim();if(""===r)throw new Error("Empty elements are not allowed");if("**"!=r&&"*"!=r&&-1!=r.indexOf("*"))throw new Error("Embedded wildcards are not supported");var u=null;r in e.edges&&(u=e.edges[r]),u||(u={id:i++,edges:{}},e.edges[r]=u),n==s.length-1&&(u.term=!0),e=u})}}),n}var Wh=Dh("**"),zh="_fs_trimmed_values";function qh(t,i,n,r){if(!va(r)){try{for(var e=[],s=0,u=r;s<u.length;s++){var o=u[s];if(!o)return;var h=new Vh(!0===o?Wh:o);e.push(h)}var a=function(t,i){for(var n={},r=function(i){n[i]=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];for(var e=0,s=t;e<s.length;e++){var u=s[e];u[i].apply(u,n)}}},e=0,s=["push","pop"];e<s.length;e++)r(s[e]);return n}(e),c=0,f=[1],v=!1;return t.jsonStringify(i,function(i,r){var s=r,u=typeof s,o=s&&"object"===u;if(""===i&&1===f.length)return f[0]--,o&&f.push(t.objectKeys(s).length),s;if(a.push(i),e.some(function(t){return t.isRedacted(!o)})&&(s=ui.BlockedFieldValue,o=!1),c+=i.length+2,o)c+=2;else if(function(t,i){return null!=t&&"symbol"!==i&&"function"!==i}(s,u))try{c+=s.toString().length}catch(t){}else c+=4;for(c>=n&&(v?s=void 0:(s=zh,v=!0)),f[f.length-1]--,o&&s&&s!==ui.BlockedFieldValue?f.push(t.objectKeys(s).length):a.pop();f[f.length-1]<=0;)f.pop(),a.pop();for(var h=0,l=e;h<l.length;h++){var d=l[h].depth();if(void 0!==d&&f.length>0&&d!==f.length-1)throw new Error("Property matcher depth out of sync")}return s})}catch(r){Bs.send(t,"allowlistedJson",{err:r,maxLength:n,ctor:i.constructor.name})}return"[error serializing ".concat(i.constructor.name,"]")}}var Vh=function(){function t(t){this.gr=1;var i=[t];t.edges["**"]&&i.push(t.edges["**"]),this.mr=[i]}return t.prototype.yr=function(){if(this.mr.length<=0)return[];var t=this.mr.length-1,i=this.mr[t];return"number"==typeof i?this.mr[t-1]:i},t.prototype.depth=function(){return this.gr},t.prototype.isRedacted=function(t){var i=this.yr();return 0===i.length||t&&!i.some(function(t){return t.term})},t.prototype.push=function(t){var i;this.gr++;var n=this.yr(),r=[];function e(i){i.edges["**"]&&(r.push(i.edges["**"],$h(i)),e(i.edges["**"])),i.edges["*"]&&r.push(i.edges["*"]),i.edges[t]&&r.push(i.edges[t])}for(var s=0,u=n;s<u.length;s++){var o=u[s];if(null===(i=o.edges["**"])||void 0===i?void 0:i.term){r=[$h(o),o.edges["**"]];break}e(o)}var h=!1;if(r.length!==n.length)h=!0;else for(var a=0;a<r.length;a++)if(r[a].id!==n[a].id){h=!0;break}h?this.mr.push(r):("number"!=typeof this.mr[this.mr.length-1]&&this.mr.push(0),this.mr[this.mr.length-1]++)},t.prototype.pop=function(){this.gr>0&&this.gr--;var t=this.mr[this.mr.length-1];"number"==typeof t&&t>1?this.mr[this.mr.length-1]--:this.mr.pop()},t}();function $h(t){var i=t.edges["**"];if(!i)throw new Error("Node must have double-wildcard edge.");return P(t.edges,1)?{id:-i.id,edges:{"**":i}}:t}var Gh=("TextDecoder"in window),Qh=("Request"in window),Xh=!ro&&!tu,Yh="ReadableStream"in window&&"function"==typeof ReadableStream.prototype.tee,Jh=function(){function t(t,i){this.N=t,this.br=i,this.Sr=null,this.kr=ui.DefaultOrgSettings.MaxAjaxPayloadLength,this._r=new wu}return t.prototype.setMaxAjaxPayloadLength=function(t){this.kr=t||ui.DefaultOrgSettings.MaxAjaxPayloadLength},t.prototype.disable=function(){this.Sr&&(this.Sr.disable(),this.Sr=null)},t.prototype.enable=function(t){var i,n=this,s=Ui(t,this.N.options.namespace),u=null===(i=null==s?void 0:s._w)||void 0===i?void 0:i.fetch;(u||t.fetch)&&(this.Sr=Su(this.N,u?s._w:t,"fetch"),this.Sr&&(this.Sr.before(function(t){n.Ar(t)}),this.Sr.afterSync(function(t){var i=t.result;t.result=r(n,void 0,void 0,function(){return e(this,function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,this.Ir(i,t.args[0],t.args[1])];case 1:case 2:return n.sent(),[3,3];case 3:return[2,i]}})})})))},t.prototype.Ar=function(t){if(Yh&&Gh&&Xh)try{var i=t.args[0],n=t.args[1];if(Qh&&!x(i)&&"url"in i&&i instanceof Request&&Xh&&i.body instanceof ReadableStream&&Zh(i.headers)){var r=i.clone(),e=ia(this.N.wdx,this.N.window,r.body,this.kr);return void this._r.set(i,e)}if(n){var s=ka(this.N.wdx,n.headers);if(n.body&&n.body instanceof ReadableStream&&Zh(s)&&Xh){var u=n.body.tee(),o=u[0],h=u[1];n.body=h,e=ia(this.N.wdx,this.N.window,o,this.kr),this._r.set(n,e)}}}catch(t){}},t.prototype.Ir=function(t,i,n){return r(this,void 0,mr,function(){var s,u,o,h,a,c;return e(this,function(f){switch(f.label){case 0:return s="GET",u="",a=!1,"string"!=typeof i?[3,1]:(u=i,[3,5]);case 1:return"url"in i?(u=i.url,s=i.method,o=i.body,h=i.headers,a=!!i.signal,this._r.has(i)?[4,this.Er(i)]:[3,3]):[3,4];case 2:o=f.sent(),f.label=3;case 3:return[3,5];case 4:u="".concat(i),f.label=5;case 5:return u?n?(s=n.method||s,h=ka(this.N.wdx,n.headers),this._r.has(n)?[4,this.Er(n)]:[3,7]):[3,9]:[2];case 6:return o=f.sent(),[3,8];case 7:o=n.body||o,f.label=8;case 8:a=!!n.signal||a,f.label=9;case 9:return c=function(t){return r(this,void 0,mr,function(){var i,n,r,s,u;return e(this,function(e){switch(e.label){case 0:return e.trys.push([0,6,,7]),[4,t];case 1:switch(i=e.sent(),u=i.ok?At.OK:At.ERROR,i.type){case"opaque":case"opaqueredirect":u=At.OPAQUE;break;case"error":u=At.ERROR}if(!ta(((n=i.headers).get("content-type")||ui.TextPlain).split(";")[0]))return[2,ra(u,i.status,{headers:n,body:null})];r=null,e.label=2;case 2:return e.trys.push([2,4,,5]),[4,i.clone().text()];case 3:return r=e.sent(),[3,5];case 4:return e.sent(),u=At.ABORTED,[3,5];case 5:return[2,ra(u,i.status,{headers:n,body:r})];case 6:return s=e.sent(),[2,ra(u=(o=s)&&"AbortError"===o.name?At.ABORTED:At.ERROR,0,{headers:{forEach:function(){}},body:void 0})];case 7:return[2]}var o})})}(t),a&&u.search(/\/(?:graph|graphql|gql)/i)>-1?[4,mr.race([c,br(this.N.wdx,this.N.window,5e3)])]:[3,11];case 10:f.sent(),f.label=11;case 11:return this.br.startRequest(s,u,{body:function(){return o},headers:h},c),[2]}})})},t.prototype.Er=function(t){return r(this,void 0,mr,function(){var i;return e(this,function(n){switch(n.label){case 0:return[4,mr.race([this._r.get(t),br(this.N.wdx,this.N.window,5e3).then(function(){return null})])];case 1:return i=n.sent(),this._r["delete"](t),[2,i]}})})},t}();function Zh(t){var i=ui.TextPlain;return null==t||t.forEach(function(t,n){"content-type"===n.toLowerCase()&&(i=t)}),ta(i)}function ta(t){switch(t){case"application/json":case"application/vnd.api+json":case ui.TextPlain:return!0}return!1}function ia(t,i,n,s){return r(this,void 0,mr,function(){var r,u,o,h,a;return e(this,function(e){switch(e.label){case 0:if(!Gh)return[2,""];e.label=1;case 1:return e.trys.push([1,3,,4]),r=new TextDecoder,u=n.getReader(),o=_r(u,s).then(function(t){return t.map(function(t){return r.decode(t)}).join("")}),h=br(t,i,2e3).then(function(){return"_fs_timeout"}),[4,mr.race([o,h])];case 2:return"_fs_timeout"===(a=e.sent())?(u.cancel()["catch"](function(t){}),[2,""]):[2,a];case 3:return e.sent(),[2,""];case 4:return[2]}})})}var na=function(){function t(t,i){this.N=t,this.br=i,this.Tr=new WeakMap}return t.prototype.disable=function(){this.Cr&&(this.Cr.disable(),this.Cr=null),this.Rr&&(this.Rr.disable(),this.Rr=null),this.Kr&&(this.Kr.disable(),this.Kr=null)},t.prototype.jr=function(t){var i=this.Tr.get(t);if(i)return i;var n={};return this.Tr.set(t,n),n},t.prototype.enable=function(t){var i,n,s,u,o=this,h=Ui(t,this.N.options.namespace),a=(null===(i=null==h?void 0:h._w)||void 0===i?void 0:i.XMLHttpRequest)||t.XMLHttpRequest;if(a){var c=a.prototype;this.Cr=null===(n=Su(this.N,c,"open"))||void 0===n?void 0:n.before(function(t){var i=o.jr(t.that);i.method=t.args[0],i.url=t.args[1]}),this.Kr=null===(s=Su(this.N,c,"setRequestHeader"))||void 0===s?void 0:s.before(function(t){var i=t.that,n=t.args[0],r=t.args[1],e=o.jr(i);e.headers||(e.headers=[]),e.headers.push([n,r])}),this.Rr=null===(u=Su(this.N,c,"send"))||void 0===u?void 0:u.before(function(t){var i=t.that,n=t.args[0],s=o.jr(i),u=s.url,h=s.method,a=s.headers;void 0!==u&&void 0!==h&&(o.Tr["delete"](i),o.br.startRequest(h,u,{headers:ka(o.N.wdx,a),body:n},function(t){return r(this,void 0,mr,function(){var i,n;return e(this,function(r){switch(r.label){case 0:return[4,new mr(function(i){t.addEventListener("load",function(){return i(At.OK)}),t.addEventListener("abort",function(){return i(At.ABORTED)}),t.addEventListener("readystatechange",function(){t.readyState===XMLHttpRequest.DONE&&0!==t.status&&(t.status<400?i(At.OK):i(At.ERROR))}),t.addEventListener("error",function(){t.readyState===t.UNSENT?i(At.ABORTED):i(At.ERROR)})})];case 1:return i=r.sent(),n=function(t){if(t)return{forEach:function(i){for(var n,r=/([^:]*):\s+(.*)(?:\r\n|$)/g;n=r.exec(t);)i(n[2],n[1])}}}(t.getAllResponseHeaders()),[2,ra(i,t.status,{headers:n,body:function(){return"text"===t.responseType?t.responseText:t.response}})]}})})}(i)))})}},t.prototype.setMaxAjaxPayloadLength=function(t){},t}();function ra(t,i,n){return{state:t,status:i,data:n}}var ea,sa,ua,oa,ha,aa=/^data:/i,ca=function(){function t(t,i){this.N=t,this.Ft=i,this.di=!1,this.Mr=new fa(t,i),this.Pr=new na(t,this.Mr),this.Or=new Jh(t,this.Mr)}return t.prototype.isEnabled=function(){return this.di},t.prototype.start=function(t){t.AjaxWatcher&&(this.di||(this.di=!0,this.Ft.enqueue({Kind:Y.REC_FEAT_SUPPORTED,Args:[Rt.Ajax,!0]}),this.Pr.enable(this.N.window),this.Or.enable(this.N.window)))},t.prototype.stop=function(){this.di&&(this.di=!1,this.Pr.disable(),this.Or.disable())},t.prototype.tick=function(){this.Mr.tick()},t.prototype.setWatches=function(t){this.Mr.setWatches(t)},t.prototype.initialize=function(t){this.Mr.initialize(t),this.Or.setMaxAjaxPayloadLength(t.maxAjaxPayloadLength)},t}(),fa=function(){function t(t,i){this.N=t,this.Ft=i,this.Nr=[],this.Lr={},this.Ur={},this.Fr=[],this.kr=0;var n=ui.DefaultOrgSettings;this.initialize({requests:n.HttpRequestHeadersAllowlist,responses:n.HttpResponseHeadersAllowlist,maxAjaxPayloadLength:n.MaxAjaxPayloadLength})}return t.prototype.Br=function(t){for(var i=!1,n=!1,r=[],e=[],s=0,u=this.Nr;s<u.length;s++){var o=u[s],h=o.urlPattern,a=o.reqFields,c=o.rspFields;if(i&&n)break;h.test(t)&&(!1===a&&(r=[!1],i=!0),i||r.push(a),!1===c&&(e=[!1],n=!0),n||e.push(c))}return[r,e]},t.prototype.Hr=function(t){this.Fr.push(t)},t.prototype.setWatches=function(t){this.Nr=t.map(function(t){return{urlPattern:new RegExp(t.URLRegex),reqFields:ya(t.RecordReq,t.ReqAllowlist),rspFields:ya(t.RecordRsp,t.RspAllowlist)}})},t.prototype.initialize=function(t){var i=this,n=t.requests,r=t.responses,e=t.maxAjaxPayloadLength;this.Lr={},this.Ur={},null==n||n.forEach(function(t){return i.Lr[t]=!0}),null==r||r.forEach(function(t){return i.Ur[t]=!0}),this.kr=e||ui.DefaultOrgSettings.MaxAjaxPayloadLength},t.prototype.addHeaderAllowlist=function(t,i){var n=this;null==t||t.forEach(function(t){return n.Lr[t]=!0}),null==i||i.forEach(function(t){return n.Ur[t]=!0})},t.prototype.tick=function(){for(var t=0;t<this.Fr.length;t++)this.Ft.enqueue({Kind:Y.AJAX_REQUEST,Args:this.Fr[t]});this.Fr=[]},t.prototype.startRequest=function(t,i,n,s){var u,o;return r(this,void 0,mr,function(){var r,h,a,c,f,v,l,d,p,w,g,m,y;return e(this,function(e){switch(e.label){case 0:return aa.test(i)?[2]:(r=Q(this.N.wdx),h=function(t,i){return zr.resolveToDocument(t,i)}(this.N.window,i),a=this.Br(h),c=a[0],f=a[1],[4,this.Dr(c,n)]);case 1:return v=e.sent(),[4,s["catch"](function(){return ra(At.ERROR,0,{headers:{forEach:function(){}},body:void 0})})];case 2:return l=e.sent(),d=l.state,p=l.status,w=l.data,g=Q(this.N.wdx)-r,[4,this.Wr(f,w)];case 3:return m=e.sent(),y=[t,$e(this.N.wdx,h,{source:"event",type:Y.AJAX_REQUEST}),g,p,v.headers,m.headers,r,v.size,m.size,null!==(u=v.text)&&void 0!==u?u:null,null!==(o=m.text)&&void 0!==o?o:null,v.legibility,m.legibility,d],this.Hr(y),[2]}})})},t.prototype.Dr=function(t,i){return ba(this.N.wdx,this.Lr,t,this.kr,i)},t.prototype.Wr=function(t,i){return ba(this.N.wdx,this.Ur,t,this.kr,i)},t}();function va(t){return 0===t.length||t.indexOf(!1)>-1}function la(t,i,n,r){return[i.length,ma(t,i,n,r)]}function da(t,i,n,r){var e=void 0;return va(n)||(e=qh(t,i,r,n)),[ga(t,i),e]}function pa(t,i){var n=t.byteLength,r=void 0;return va(i)||(r="[ArrayBuffer]"),[n,r]}function wa(t,i,n,s){return r(this,void 0,mr,function(){var r,u,o,h,a,c,f;return e(this,function(e){switch(e.label){case 0:if(u=(r=i).size,o=r.type,va(n))return[2,[u,void 0]];if(h=function(i){Bs.send(t,"ajaxwatcher#_httpBodyBlob",{err:i,blobType:o,blobSize:u},"warning")},!ta(o))return[3,4];e.label=1;case 1:return e.trys.push([1,3,,4]),[4,r.text()["catch"](function(t){h(t)})];case 2:return(a=e.sent())&&(c=ma(t,a,n,s))?[2,[u,c]]:[3,4];case 3:return f=e.sent(),h(f),[3,4];case 4:return[2,[u,"[Blob]"]]}})})}function ga(t,i){try{return t.jsonStringify(i).length}catch(t){}return 0}function ma(t,i,n,r){if(!va(n))try{return qh(t,t.jsonParse(i),r,n)}catch(t){return n.length>0&&n.every(function(t){return!0===t})?i.slice(0,r):void 0}}function ya(t,i){switch(t){default:case Bt.Elide:return!1;case Bt.Record:return!0;case Bt.Allowlist:try{return Dh(i)}catch(t){return"error parsing field allowlist (".concat(i,": ").concat(t),!1}}}function ba(t,i,n,s,u){var o;return r(this,void 0,mr,function(){var r,h,a,c,f,v,l;return e(this,function(e){switch(e.label){case 0:return r="",null===(o=u.headers)||void 0===o||o.forEach(function(t,n){var e=n.toLowerCase(),s=i[e];r+="".concat(e).concat(s?": ".concat(t):"").concat("\r\n")}),"function"!=typeof(h=null==u?void 0:u.body)?[3,2]:[4,h()];case 1:return a=e.sent(),[3,3];case 2:a=h,e.label=3;case 3:return[4,Sa(t,n,a,s)];case 4:return c=e.sent(),f=c[0],v=c[1],l=0!==f||v?kt.NotEmpty:kt.Unknown,[2,{headers:r,text:v,size:f,legibility:l}]}})})}function Sa(t,i,n,s){return void 0===s&&(s=ui.DefaultOrgSettings.MaxAjaxPayloadLength),r(this,void 0,mr,function(){var r;return e(this,function(e){if(null==n)return[2,[0,void 0]];switch(typeof n){default:return[2,[-1,va(i)?void 0:"[unknown]"]];case"string":return[2,la(t,n,i,s)];case"object":switch(r=n.constructor){case Object:default:return[2,da(t,n,i,s)];case Blob:return[2,wa(t,n,i,s)];case ArrayBuffer:return[2,pa(n,i)];case Document:case FormData:case URLSearchParams:case ReadableStream:return[2,[-1,va(i)?void 0:"".concat(r.name)]]}}return[2]})})}function ka(t,i){return i?S(t,i)?{forEach:function(t){for(var n=0,r=i;n<r.length;n++){var e=r[n],s=e[0];t(e[1],s)}}}:"function"==typeof i.forEach?i:{forEach:function(t){for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&t(i[n],n)}}:i}(sa=ea||(ea={}))[sa.CSS_INHERIT=0]="CSS_INHERIT",sa[sa.CSS_PRIMITIVE_VALUE=1]="CSS_PRIMITIVE_VALUE",sa[sa.CSS_VALUE_LIST=2]="CSS_VALUE_LIST",sa[sa.CSS_CUSTOM=3]="CSS_CUSTOM",(oa=ua||(ua={}))[oa.CSS_UNKNOWN=0]="CSS_UNKNOWN",oa[oa.CSS_NUMBER=1]="CSS_NUMBER",oa[oa.CSS_PERCENTAGE=2]="CSS_PERCENTAGE",oa[oa.CSS_EMS=3]="CSS_EMS",oa[oa.CSS_EXS=4]="CSS_EXS",oa[oa.CSS_PX=5]="CSS_PX",oa[oa.CSS_CM=6]="CSS_CM",oa[oa.CSS_MM=7]="CSS_MM",oa[oa.CSS_IN=8]="CSS_IN",oa[oa.CSS_PT=9]="CSS_PT",oa[oa.CSS_PC=10]="CSS_PC",oa[oa.CSS_DEG=11]="CSS_DEG",oa[oa.CSS_RAD=12]="CSS_RAD",oa[oa.CSS_GRAD=13]="CSS_GRAD",oa[oa.CSS_MS=14]="CSS_MS",oa[oa.CSS_S=15]="CSS_S",oa[oa.CSS_HZ=16]="CSS_HZ",oa[oa.CSS_KHZ=17]="CSS_KHZ",oa[oa.CSS_DIMENSION=18]="CSS_DIMENSION",oa[oa.CSS_STRING=19]="CSS_STRING",oa[oa.CSS_URI=20]="CSS_URI",oa[oa.CSS_IDENT=21]="CSS_IDENT",oa[oa.CSS_ATTR=22]="CSS_ATTR",oa[oa.CSS_COUNTER=23]="CSS_COUNTER",oa[oa.CSS_RECT=24]="CSS_RECT",oa[oa.CSS_RGBCOLOR=25]="CSS_RGBCOLOR",oa[oa.CSS_VW=26]="CSS_VW",oa[oa.CSS_VH=27]="CSS_VH",oa[oa.CSS_VMIN=28]="CSS_VMIN",oa[oa.CSS_VMAX=29]="CSS_VMAX";var _a="EventQueue not defined",Aa="_fs_stylesheet_hooked",Ia=["CSSMediaRule","CSSSupportsRule"],Ea=function(){function t(t,i,n,r){void 0===r&&(r=Wu);var e=this;this.N=t,this.Ft=i,this.zr=n,this.gi=[],this.zt=[],this.qr=1,this.Vr=!1,this.$r=new Map,this.Gr=new wu,this.Qr=new wu,this.Xr=0,this.Yr=new wu(void 0,tu),this.Jr=new zu(this.N.wdx,this.N.window,1e4,1e4,function(){return setTimeout(function(){e.Ft.enqueue({Kind:Y.FAIL_THROTTLED,Args:[Pt.StyleSheetHooks]}),e.stop()})}),this.addInsert=this.Jr.guard(this.addInsert),this.addDelete=this.Jr.guard(this.addDelete),this.Zr=new r(this.N.wdx,this.N.window,function(){e.te()},50),this.ie=N(function(t,i){var n,r;if(!(null===(n=t.parentRule)||void 0===n?void 0:n.parentStyleSheet)||!e.Vr)return!0;var s=t.parentRule;return e.$r.has(s)||e.$r.set(s,new Set),null===(r=e.$r.get(s))||void 0===r||r.add(i),e.Zr.isRunning()||e.Zr.start(),!0})}return t.prototype.start=function(){var t=this;this.Jr.open();var i=this.N.window;if(i.CSSStyleSheet&&i.StyleSheet){var n=i.CSSStyleSheet.prototype;this.Sr(n),this.ne(i),this.ee(),this.zt.push(F(i.StyleSheet,"disabled",function(i,n){return t.onDisableSheet(i,n)}),F(i.Document,"adoptedStyleSheets",function(i){return t.onSetAdoptedStyleSheets(i)}),F(i.ShadowRoot,"adoptedStyleSheets",function(i){return t.onSetAdoptedStyleSheets(i)})),this.Vr=!0}},t.prototype.ne=function(t){var i,n,r=this,e=function(t){var i=Su(r.N,t,"insertRule");i&&(i.afterSync(function(t){r.se(t.that,t.args[0],t.args[1])}),r.gi.push(i)),(i=Su(r.N,t,"deleteRule"))&&(i.afterSync(function(t){r.ue(t.that,t.args[0])}),r.gi.push(i))};if("function"==typeof(null===(i=t.CSSGroupingRule)||void 0===i?void 0:i.prototype.insertRule))e(t.CSSGroupingRule.prototype);else for(var s=0,u=Ia;s<u.length;s++){var o=null===(n=t[u[s]])||void 0===n?void 0:n.prototype;o&&e(o)}},t.prototype.ee=function(){var t=this;if(!this.Vr){var i=Su(this.N,CSSStyleDeclaration.prototype,"setProperty");i&&(i.afterSync(function(i){t.ie(i.that,i.args[0])}),this.gi.push(i));var r=CSSStyleRule.prototype,e=Object.getOwnPropertyDescriptor(r,"style");this.oe||(this.oe=null==e?void 0:e.get);var s=this;this.he=function(){var t,i=null===(t=null==e?void 0:e.get)||void 0===t?void 0:t.apply(this);return N(function(){i=s.ae(i)})(),i};try{Object.defineProperty(r,"style",n(n({},e),{get:s.he}))}catch(t){}}},t.prototype.ae=function(t){var i=this.Gr.get(t);if(i)return i;var n=function(t,i,n){if("function"!=typeof t.Proxy)return i;var r=new t.Proxy(i,{get:function(t,i){var n=t[i];return"function"!=typeof n?n:function(){for(var i=[],e=0;e<arguments.length;e++)i[e]=arguments[e];var s=this;return s===r&&(s=t),n.apply(s,i)}},set:function(t,i,r){var e=t[i];return t[i]=r,"function"==typeof e||"symbol"==typeof i||n(t,i),!0}});return r}(this.N.window,t,h(this.ie));return this.Gr.set(t,n),n},t.prototype.te=function(){var t=this,i=this.$r;this.$r=new Map,i.forEach(function(i,n){var r=n.parentStyleSheet;if(r){var e=t.ce(r,n);if(void 0!==e){var s={};i.forEach(function(t){s[t]=n.style[t]}),t.fe(r,function(t){t.enqueue({Kind:Y.CSSRULE_UPDATE,Args:[e,s]})})}}})},t.prototype.se=function(t,i,n){var r=Oa(this.N.wdx,t);this.addInsert(t.parentStyleSheet,i,n,r)},t.prototype.ue=function(t,i){var n=Oa(this.N.wdx,t);this.addDelete(t.parentStyleSheet,i,n)},t.prototype.Sr=function(t){var i,n=this;(i=Su(this.N,t,"insertRule"))&&(i.afterSync(function(t){n.addInsert(t.that,t.args[0],t.args[1])}),this.gi.push(i)),(i=Su(this.N,t,"addRule"))&&(i.afterSync(function(t){n.addInsert(t.that,"".concat(t.args[0]," {").concat(t.args[1],"}"),t.args[2])}),this.gi.push(i)),(i=Su(this.N,t,"deleteRule"))&&(i.afterSync(function(t){n.addDelete(t.that,t.args[0])}),this.gi.push(i)),(i=Su(this.N,t,"removeRule"))&&(i.afterSync(function(t){n.addDelete(t.that,t.args[0])}),this.gi.push(i)),(i=Su(this.N,t,"replaceSync"))&&(i.afterSync(function(t){n.snapshotConstructedStylesheet(t.that,!0)}),this.gi.push(i)),(i=Su(this.N,t,"replace"))&&(i.afterSync(function(t){return r(n,void 0,mr,function(){return e(this,function(i){switch(i.label){case 0:return"object"!=typeof t.result||"function"!=typeof t.result.then?[2]:[4,t.result];case 1:return i.sent(),this.snapshotConstructedStylesheet(t.that,!0),[2]}})})}),this.gi.push(i)),Object.defineProperty(t,Aa,{value:!0,configurable:!0})},t.prototype.onSetAdoptedStyleSheets=function(t){if(Ke(t)){var i=t.adoptedStyleSheets;if(i){for(var n=[],r=0,e=i;r<e.length;r++){var s=e[r],u=this.snapshotConstructedStylesheet(s);n.push(u),s.disabled&&this.onDisableSheet(s,!0)}this.Ft.enqueue({Kind:Y.ADOPTED_STYLESHEETS,Args:[Ke(t),n]})}}},t.prototype.snapshotEl=function(t){var i=Ke(t);if(i&&Ra(this.N.wdx,t)){var n=function(t){return t?t.sheet:void 0}(t);n&&(this.ve([jt.Node,i],n),this.zr&&La(t))}},t.prototype.processWayfinderLog=function(t){var i=this;if(this.zr)try{var n=t.sheet;if(!n)return;var r=Ka(this.Yr,n,jt.Node);if(!r)return;var e=new Map;La(t).forEach(function(t){var s,u,o;try{var h=function(t){var i=t.indexOf(":");return i<=0?{}:JSON.parse(t.substring(i+1))}(t);switch(h.kind){case Y.CSSRULE_INSERT:i.addInsert(n,h.cssText,h.index,h.path);break;case Y.CSSRULE_DELETE:i.addDelete(n,h.index,h.path);break;case Y.CSSRULE_UPDATE:var a=[dt.Index,r,h.index];(null!==(o=null===(u=h.path)||void 0===u?void 0:u.length)&&void 0!==o?o:0)>0&&a.push(h.path);var c=ds(i.N.wdx,a);e.has(c)?e.get(c).rules[h.key]=h.value:e.set(c,{ruleId:a,rules:(s={},s[h.key]=h.value,s)})}}catch(t){}}),this.fe(n,function(t){e.forEach(function(i){t.enqueue({Kind:Y.CSSRULE_UPDATE,Args:[i.ruleId,i.rules]})})})}catch(t){}},t.prototype.snapshotConstructedStylesheet=function(t,i){void 0===i&&(i=!1);var n=ja(this.Yr,t);return i||void 0===n?(void 0===n&&(n=this.qr++,function(t,i,n){t.set(i,n)}(this.Yr,t,n)),this.ve([jt.Sheet,n],t),n):n},t.prototype.ve=function(t,i){this.Ft.enqueue({Kind:Y.RESET_CSS_SHEET,Args:[t]});var n=function(t){try{return t?t.cssRules||t.rules:void 0}catch(t){return}}(i);if(n){for(var r=[],e=0;e<n.length;e++)try{var s=this.N.wdx,u=Ma(Ca(s,n[e]));r.push(qe(s,u))}catch(t){r.push("html {}")}this.Ft.enqueue({Kind:Y.CSSRULE_INSERT,Args:[t,r,0]})}},t.prototype.addInsert=function(t,i,n,r){var e=Ka(this.Yr,t,jt.Node);if(e&&"string"==typeof i){var s=[e,[qe(this.N.wdx,Ma(i))]];void 0!==n&&(s[2]=n),void 0!==r&&(s[3]=r),this.fe(t,function(t){return t.enqueue({Kind:Y.CSSRULE_INSERT,Args:s})})}},t.prototype.addDelete=function(t,i,n){var r=Ka(this.Yr,t,jt.Node);r&&this.fe(t,function(t){return t.enqueue({Kind:Y.CSSRULE_DELETE,Args:n?[r,i,n]:[r,i]})})},t.prototype.onDisableSheet=function(t,i){var n=Ka(this.Yr,t,jt.Node);n&&this.fe(t,function(t){return t.enqueue({Kind:Y.DISABLE_STYLESHEET,Args:[n,!!i]})})},t.prototype.fe=function(t,i){if(t.ownerNode)return n=this.N,r=t.ownerNode,e=i,void((s=Ui(function(t){var i=t.ownerDocument;return i&&i.defaultView}(r)||n.window,n.options.namespace))&&_(s._withEventQueue)&&s._withEventQueue(n.recording.pageSignature(),function(t){var i=t;e({enqueue:function(t){Ws(n.wdx,null!=i,_a)&&i.enqueue(t)},enqueueFirst:function(t){Ws(n.wdx,null!=i,_a)&&i.enqueueFirst(t)}}),i=null}));var n,r,e,s;i(this.Ft)},t.prototype.ce=function(t,i){var n;if(this.Qr.has(i))return[dt.Cached,this.Qr.get(i)];var r,e=void 0;window.CSSGroupingRule&&i.parentRule&&i.parentRule instanceof CSSGroupingRule?(e=Oa(this.N.wdx,i.parentRule),r=i.parentRule.cssRules):r=t.cssRules;var s=Array.prototype.indexOf.call(r,i);if(-1!==s){var u=Ka(this.Yr,t,jt.Node);if(u){var o=this.Xr++;return this.Qr.set(i,o),(null!==(n=null==e?void 0:e.length)&&void 0!==n?n:0)>0?[dt.Index,u,s,e]:[dt.Index,u,s]}}},t.prototype.stop=function(){this.Vr=!1,this.Jr.close();for(var t=0,i=this.gi;t<i.length;t++){var r=i[t];r.disable(),Object.defineProperty(r.getTarget(),Aa,{value:!1,configurable:!0})}this.gi=[];for(var e=0,s=this.zt;e<s.length;e++)(0,s[e])();if(this.zt=[],this.oe){this.Zr.stop();try{var u=CSSStyleRule.prototype,o=Object.getOwnPropertyDescriptor(u,"style");if((null==o?void 0:o.get)!==this.he)return;Object.defineProperty(u,"style",n(n({},o),{get:this.oe}))}catch(t){}}},t.prototype.ensureHook=function(t){this.le(t)||t[Aa]||this.Sr(t)},t.prototype.removeHook=function(t){if(!this.le(t))for(var i=0;i<this.gi.length;i++){var n=this.gi[i],r=n.getTarget();if(t===r){n.disable(),this.gi.splice(i,1),Object.defineProperty(r,Aa,{value:!1,configurable:!0});break}}},t.prototype.le=function(t){return Object.getPrototypeOf(t)===this.N.window.CSSStyleSheet},t}(),Ta=document.createElement("div");function Ca(t,i){return""!=Ta.style.cssText?i.cssText:xa(t,i)}function xa(t,i,n){if(void 0===n&&(n=0),!Ws(t,n<=20,"No deep recursion for CSS rules"))return"html { /* Depth limit exceeded! */ }";var r=function(t){switch(t.type){case CSSRule.PAGE_RULE:var i=t.selectorText||"";return i&&R(i,"@page")?i:"@page ".concat(i);case CSSRule.KEYFRAME_RULE:return t.keyText;case CSSRule.STYLE_RULE:return t.selectorText;case CSSRule.MEDIA_RULE:return"@media ".concat(t.media.mediaText);case CSSRule.KEYFRAMES_RULE:return"@keyframes ".concat(t.name);case CSSRule.SUPPORTS_RULE:return"@supports ".concat(t.conditionText);default:return null}}(i);if(null==r)return i.cssText;var e=function(t,i,n){var r,e,s,u,o=i,h=o.style;if(h){for(var a="",c=0;c<h.length;c++){var f=h[c],v=void 0!==(u=function(t,i){var n=t;if("function"==typeof n.getPropertyCSSValue){var r=n.getPropertyCSSValue(i);if(null!=r){var e;switch(r.cssValueType){case ea.CSS_PRIMITIVE_VALUE:e=r;break;case ea.CSS_VALUE_LIST:if(1!==r.length)return;var s=r.item(0);if(null==s)return;if(s.cssValueType!==ea.CSS_PRIMITIVE_VALUE)return;e=s;break;default:return}if(e.primitiveType===ua.CSS_STRING){var u=Wr();ha||(ha=u.createElement("div"));var o=e.cssText;try{ha.style.cssText="".concat(i,": \"").concat(o,"\";");var h=ha.style.getPropertyCSSValue(i);if(null==h)return;if(o!==h.cssText)return}catch(t){return}finally{ha.style.cssText=""}return"\"".concat(o,"\"")}}}}(e=h,s=f))?u:e.getPropertyValue(s);("initial"===v||("\""===(r=v)[0]||"'"===r[0])&&r[r.length-1]===r[0])&&(a+="".concat(f,": ").concat(v),"important"===h.getPropertyPriority(f)&&(a+=" !important"),a+="; ")}return[h.cssText,a].filter(Boolean).join("\n")}var l=o.cssRules;if(!l)return null;var d="";for(c=0;c<l.length;c++)d+=xa(t,l[c],n+1);return d}(t,i,n);return null==e?i.cssText:"".concat(r," { ").concat(e,"} ")}function Ra(t,i){var n;if(i instanceof HTMLLinkElement){try{return function(t,i){var n,r;return null!==(r=null===(n=t.classList)||void 0===n?void 0:n.contains("fs-css-in-js"))&&void 0!==r&&r}(i)}catch(i){Bs.send(t,"shouldSnapshot",{err:i})}return!1}return(null!==(n=i.textContent)&&void 0!==n?n:"").length<Fe}function Ka(t,i,n){var r=function(t,i){var n=ja(t,i);if(n)return[jt.Sheet,n];var r=Ke(i.ownerNode);return r?[jt.Node,r]:void 0}(t,i);if(r){var e=r[0],s=r[1];return e===n?s:r}}function ja(t,i){return t.get(i)}function Ma(t){return t.length<=Fe?t:("CSSRule too large, inserting dummy instead: ".concat(t.length),":root { --fs-dropped-large-rule: 0 }")}function Pa(t,i){for(var n=0;n<t.length;n++)if(t[n]===i)return n;return-1}function Oa(t,i){for(var n=[],r=i,e="stylesheetwatcher#getPath";r.parentRule||r.parentStyleSheet;){var s;if(!(null==(s=r.parentRule?r.parentRule:r.parentStyleSheet)?void 0:s.cssRules))return Bs.send(t,e,{pathLength:n.length},"warning"),n;var u=Pa(s.cssRules,r);if(u>-1){if(n.unshift(u),s instanceof CSSStyleSheet)break;r=s}else Bs.send(t,e,{index:u},"warning")}return n}Ta.style.width="initial";var Na="__wayfinder_cursor";function La(t){var i,n=null!==(i=t.getAttributeNS(Ti,Ci))&&void 0!==i?i:"";if(!n.length)return[];var r=function(t){return Na in t?t[Na]:""}(t),e=n.split("\n").filter(function(t){return t>r});return e.length>0&&(t[Na]=e[e.length-1]),e}var Ua=function(){function t(t,i,n){this.N=t,this.de=i,this.et=n}return t.prototype.start=function(){var t=this,i=this.N.window.document;this.et.addCustom(i,this.pe(),!0,function(i){t.onFullscreenChange(i)}),this.et.addCustom(i,this.we(),!0,function(i){t.onFullscreenError(i)})},t.prototype.stop=function(){var t;null===(t=this.et)||void 0===t||t.clearAll()},t.prototype.onFullscreenChange=function(t){var i=this.ge();if(i){var n=Ke(i);this.me,this.de.enqueue({Kind:Y.FULLSCREEN,Args:[n,!0]}),this.me=n}else this.me,this.de.enqueue({Kind:Y.FULLSCREEN,Args:[this.me,!1]}),this.me=void 0},t.prototype.onFullscreenError=function(t){this.de.enqueue({Kind:Y.FULLSCREEN_ERROR,Args:[]})},t.prototype.ge=function(){var t=this.N.window.document;return t[uu(t,"fullscreenElement")]},t.prototype.pe=function(){return uu(this.N.window.document,"onfullscreenchange").slice(2)},t.prototype.we=function(){return uu(this.N.window.document,"onfullscreenerror").slice(2)},t}(),Fa=function(){function t(t,i){this.Ft=i,this._t=null,this.ye={};var n=t.window;"customElements"in n&&null!=n.customElements&&"get"in n.customElements&&"whenDefined"in n.customElements&&(this._t=n.customElements)}return t.prototype.start=function(){},t.prototype.stop=function(){},t.prototype.onCustomNodeVisited=function(t){return r(this,void 0,mr,function(){var i;return e(this,function(n){return this._t?(i=t.nodeName.toLowerCase(),Object.prototype.hasOwnProperty.call(this.ye,i)||this.be(i),[2]):[2]})})},t.prototype.be=function(t){return r(this,void 0,mr,function(){var i;return e(this,function(n){switch(n.label){case 0:if(!this._t)return[2];n.label=1;case 1:return n.trys.push([1,3,,4]),i=!!this._t.get(t),this.ye[t]=i,[4,this._t.whenDefined(t)];case 2:return n.sent(),this.Ft.enqueue({Kind:Y.CUSTOM_ELEMENT_DEFINED,Args:[t]}),[3,4];case 3:return n.sent(),[3,4];case 4:return[2]}})})},t}(),Ba=function(){function t(t,i){this.N=t,this.Se=!1,this.gi=[],this.Si=i,this.yi=t.window,this.Se=Ha(this.yi)}return t.prototype.start=function(){this.Si.enqueue({Kind:Y.REC_FEAT_SUPPORTED,Args:[Rt.HTMLDialogElement,this.Se]}),this.Se&&(this.Ai("show"),this.Ai("showModal"),this.Ai("close"))},t.prototype.stop=function(){this.gi.forEach(function(t){return t.disable()}),this.gi=[]},t.prototype.Ai=function(t){var i=this,n=Su(this.N,this.yi.HTMLDialogElement.prototype,t);null==n||n.afterSync(function(n){var r=Ke(n.that),e="close"!==t,s="showModal"===t;i.Si.enqueue({Kind:Y.DIALOG,Args:[r,e,s]})}),n&&this.gi.push(n)},t}(),Ha=function(t){return void 0!==t.HTMLDialogElement},Da=function(t,i){try{return t.elMatches(i,"dialog:modal")}catch(t){return!0}},Wa=function(){function t(){}return t.prototype.now=function(){return Date.now()},t}(),za=new(function(){function t(t,i,n,r){void 0===n&&(n=i),void 0===r&&(r=new Wa),this.ke=t,this._e=i,this.Ae=r,this.Ie=r.now(),this.Ee=n}return t.prototype.hasCapacityFor=function(t){var i=this.Ae.now(),n=(i-this.Ie)*this.ke;return this.Ee=Math.min(this._e,this.Ee+n),this.Ie=i,this.Ee>=t?(this.Ee-=t,[!0,0]):[!1,(t-this.Ee)/this.ke]},t}())(2,2e5),qa=new Set(["measureText","getImageData","getError","getTransform","isContextLost","isEnabled","isFramebuffer","isProgram","isRenderbuffer","isShader","isTexture"]),Va=new Set(["fillText"]),$a=function(){function t(t,i,n,r){this.N=t,this.Ft=i,this.er=n,this.Qn=r,this.Te=pi.CaptureCanvasOps,this.Ce=[],this.xe=[],this.Re=new WeakMap,this.Ke=new WeakMap,this.je=new Set,this.Me=0,this.Pe=new WeakMap,this.Oe=!1,this.Ne=new WeakMap,this.Le=new Set,this.Ue=new WeakMap,this.Fe=new WeakMap,this.Be=1,this.He=new WeakMap,this.De=1,this.We=new WeakMap,this.ze=0,this.qe=!1}return t.prototype.start=function(t){var i,n=this;if(t.CanvasWatcherMode&&(this.Ft.enqueue({Kind:Y.REC_FEAT_SUPPORTED,Args:[Rt.CanvasWatcherEnabled,!0,Rt.CanvasScreenShotMode,t.CanvasWatcherMode===pi.ScreenshotCanvas]}),this.Oe=!0,this.Te=null!==(i=t.CanvasWatcherMode)&&void 0!==i?i:pi.CaptureCanvasOps,this.Sr("2d",CanvasRenderingContext2D),this.Sr("webgl",WebGLRenderingContext),this.Sr("webgl2",WebGL2RenderingContext),this.Te===pi.ScreenshotCanvas)){if(!HTMLCanvasElement.prototype.toDataURL)return;this.Me=setInterval(function(){return n.screenshotConnectedCanvases()},1e3)}},t.prototype.Ve=function(t,i){return"object"!=typeof i?[void 0,0]:(this.He.has(i)||this.He.set(i,[t,this.De++]),this.He.get(i))},t.prototype.Sr=function(t,i){var n=this;if(i)for(var r=i.prototype,e=function(e){if(qa.has(e))return"continue";var u=Object.getOwnPropertyDescriptor(r,e);if("function"==typeof(null==u?void 0:u.value)){var o=Su(s.N,r,e);o&&(o.afterSync(function(i){return n.$e(t,e,i.that,i.args,i.result)}),s.Ce.push(o))}else"function"==typeof(null==u?void 0:u.set)&&s.xe.push(F(i,e,s.Ge(t,e)))},s=this,u=0,o=Object.keys(r);u<o.length;u++)e(o[u])},t.prototype.Qe=function(t,i){var n;this.Le.add(t),this.Ue.has(t)||this.Ue.set(t,Q(this.N.wdx)+5e3),this.Ne.has(t)||this.Ne.set(t,[]),null===(n=this.Ne.get(t))||void 0===n||n.push.apply(n,i)},t.prototype.Xe=function(t){var i,n=null!==(i=this.Ne.get(t))&&void 0!==i?i:[];return this.Le["delete"](t),this.Ue["delete"](t),n},t.prototype.Ye=function(t){var i=t instanceof HTMLCanvasElement?Ke(t):0;if(i)return i;if(this.Fe.has(t))return{id:this.Fe.get(t)};var n={id:this.Be};return this.Je(t,n),this.Fe.set(t,this.Be),++this.Be,n},t.prototype.Je=function(t,i){this.Ft.enqueue({Kind:Y.CANVAS_DETACHED_DIMENSION,Args:[i,t.width,t.height]})},t.prototype.flush=function(t,i){var n;if(this.Oe){if(this.We.has(t))return this.We.get(t);this.Te===pi.ScreenshotCanvas&&t instanceof HTMLCanvasElement&&(this.je.add(t),this.Ke.set(t,!0));var r=this.Ye(t);this.We.set(t,r);var e=this.Xe(t);if(e.length>0){var s=i;if(!s){var u=t instanceof HTMLCanvasElement?Ce(t):void 0,o=t instanceof HTMLCanvasElement&&D(this.N.wdx,t);s=null!==(n=null==u?void 0:u.mask)&&void 0!==n?n:o}this.Ze(t,r,e,s)}return this.We["delete"](t),r}},t.prototype.ts=function(t,i,n,r,e,s,u){var o,h;switch(typeof r){case"string":return e?ne(r):r;case"number":case"boolean":case"bigint":return r;case"undefined":return{undef:!0};case"object":if(!r)return r;try{u.set(r,!0)}catch(t){}var a=null===(o=Object.getPrototypeOf(r))||void 0===o?void 0:o.constructor,c=(null==a?void 0:a.name)||function(t){var i;if(t){var n=t.toString(),r=Qa.exec(n);return r||(r=Xa.exec(n)),null===(i=null==r?void 0:r[1])||void 0===i?void 0:i.trim()}}(a),f={ctor:c};if(((h=r)instanceof Node||I(h)&&"nodeType"in h)&&(d=Ke(r)))return f.id=d,f;switch(c){case"Array":return this.ze+=r.length,this.ns(t,i,n,r,e,s,u);case"CanvasGradient":return f;case"HTMLImageElement":var v=$e(this.N.wdx,r.src,{source:"dom",type:"canvas"});return this.Qn.record(v),f.src=v,f;case"HTMLCanvasElement":var l=r,d=this.flush(l,e);return f.srcId=d,f}if(function(t){var i;return!!(null===(i=Object.prototype.toString.call(t))||void 0===i?void 0:i.match(Ga))}(r))return this.He.has(r)?this.rs(r,f,e):(f.typedArray="[".concat(r.toString(),"]"),this.ze+=r.length,f);if("object"==typeof r&&this.He.has(r))return this.rs(r,f,e);if(r instanceof WebGLBuffer||r instanceof WebGLTexture){var p=void 0;switch(s){case"bindTexture":p=this.es(t,"createTexture",i,n,r);break;case"bindBuffer":p=this.es(t,"createBuffer",i,n,r)}if(void 0!==p)return this.rs(r,f,e)}var w=r;for(var g in f.obj={},w){try{switch(typeof w[g]){case"function":continue;case"object":if(w[g]&&u.has(w[g]))continue}}catch(t){continue}++this.ze,f.obj[g]=this.ts(t,i,n,w[g],e,s,u)}return f;default:return null}},t.prototype.rs=function(t,i,n){var r=this.He.get(t),e=r[0],s=r[1];return this.flush(e,n),i.ref=s,delete i.ctor,i},t.prototype.es=function(t,i,n,r,e){var s=this.Ve(n,e),u=(s[0],s[1]);return this.ss(r,[[t,li.Function,i,[],u]]),u},t.prototype.ns=function(t,i,n,r,e,s,u){var o=this;return void 0===u&&(u=new WeakMap),this.ze+=r.length+1,r.map(function(r){return o.ts(t,i,n,r,e,s,u)})},t.prototype.Ze=function(t,i,n,r){var e=this;if(void 0===r&&(r=!1),!this.qe){var s=n.map(function(n){var s=n[0],u=n[1],o=n[2],h=n[3],a=n[4];return[s,u,o,e.ns(s,t,i,h,r&&Va.has(o),o),a]});if(!this.Re.has(t)&&(this.Re.set(t,!0),n.some(function(t){return"2d"===t[0]}))){var u=function(t){var i=t.getContext("2d");if(!i)return[];var n=[];if((i instanceof CanvasRenderingContext2D||window.OffscreenCanvasRenderingContext2D&&i instanceof OffscreenCanvasRenderingContext2D)&&"function"==typeof i.getTransform){var r=i.getTransform();if(!r.isIdentity){var e=r.a,s=r.b,u=r.c,o=r.d,h=r.e,a=r.f;n.push(["2d",li.Function,"transform",[e,s,u,o,h,a],-1])}}return n}(t);if(u.length>0)return u.push.apply(u,s),void this.ss(i,u)}this.ss(i,s)}},t.prototype.ss=function(t,i){if(!this.qe){var n=za.hasCapacityFor(this.ze),r=n[0];n[1],this.ze=0,r?this.Ft.enqueue({Kind:Y.CANVAS,Args:[t,i]}):this.qe=!0}},t.prototype.us=function(t,i){t instanceof HTMLCanvasElement&&(this.Te===pi.ScreenshotCanvas?(this.Ke.set(t,!0),this.je.add(t)):this.Qe(t,i))},t.prototype.$e=function(t,i,n,r,e){for(var s=[],u=0;u<r.length;u++)s.push(r[u]);var o=this.Ve(n.canvas,e),h=(o[0],o[1]);this.us(n.canvas,[[t,li.Function,i,s,h]])},t.prototype.Ge=function(t,i){var n=this;return function(r,e){n.us(r.canvas,[[t,li.Set,i,[e],0]])}},t.prototype.stop=function(){this.Oe=!1,this.Ce.forEach(function(t){return t.disable()}),this.xe.forEach(function(t){return t()}),this.Ce=[],this.xe=[],this.Me&&(clearInterval(this.Me),this.Me=0)},t.prototype.screenshotConnectedCanvases=function(){var t=this,i=[],n=[];return this.je.forEach(function(r){var e=Ke(r);D(t.N.wdx,r)&&e?t.Ke.has(r)&&n.push(Cr(t.N.wdx,t.N.window).then(function(){return t.os(r,e)})):i.push(r)}),i.forEach(function(i){return t.je["delete"](i)}),this.Ke=new WeakMap,mr.all(n)},t.prototype.os=function(t,i){return r(this,void 0,mr,function(){var n,r,s,u,o;return e(this,function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),n=t.toDataURL(),r=this.Pe.get(t),[4,this.er.uploadDataUrlIfNeeded(n,function(t){var i;return t.url!==r&&!!(null===(i=t.contentType)||void 0===i?void 0:i.match(/^image/i))})];case 1:return(s=e.sent())?(u=s.url,this.Pe.set(t,u),s.url===r?[2]:(this.ss(i,[["2d",li.Function,"drawImage",[{ctor:"HTMLImageElement",src:u},0,0],0]]),[3,3])):[2];case 2:return o=e.sent(),qs(this.N.wdx,"Canvas screenshot",{err:o}),[3,3];case 3:return[2]}})})},t.prototype.hs=function(){var t=this,i=[];this.Le.forEach(function(n){n instanceof HTMLCanvasElement&&(Ke(n)||!D(t.N.wdx,n))||(t.Ne["delete"](n),i.push(n))});for(var n=0,r=i;n<r.length;n++){var e=r[n];this.Xe(e)}},t.prototype.tick=function(){var t=this;if(this.Oe)try{if(this.hs(),this.Te===pi.ScreenshotCanvas)return;this.Le.forEach(function(i){var n;i instanceof HTMLCanvasElement&&t.cs(i);var r=null!==(n=t.Ue.get(i))&&void 0!==n?n:Number.POSITIVE_INFINITY;Q(t.N.wdx)>r&&t.Xe(i)})}catch(t){Bs.send(this.N.wdx,"canvaswatcher#tick",{err:t,mode:this.Te})}},t.prototype.cs=function(t){var i=Ke(t);if(i){var n=Ce(t),r=this.Ne.get(t);r&&0!==r.length&&(this.Ze(t,i,r,!!(null==n?void 0:n.mask)),this.Xe(t))}},t}(),Ga=/\[object ((I|Ui)nt(8|16|32)|Float(32|64)|Uint8Clamped|Big(I|Ui)nt64)Array\]/,Qa=/^\[object ([^\]]+?)(?:Constructor)?\]/,Xa=/^function ([^(]+)/,Ya=/^\s*at .*(\S+:\d+|native|(<anonymous>))/m,Ja=/^(eval@)?(\[native code\])?$/;function Za(t,i,n,r,e){return[i||"",t(n||""),parseInt(r||"-1",10),parseInt(e||"-1",10)]}function tc(t){if(!t||-1===t.indexOf(":"))return["","",""];var i=/(.+?)(?::(\d+))?(?::(\d+))?$/.exec(t.replace(/[()]/g,""));return i?[i[1]||"",i[2]||"",i[3]||""]:["","",""]}var ic=["_fs_debug","assert","debug","error","info","log","trace","warn"],nc=ic.filter(function(t){return!/debug/.test(t)});function rc(t,i,n){return es(t,x(i)?i.slice(0,n):Uu(t)(i,n))}function ec(t){if(I(t)){try{if(_(t.toString))return t.toString()}catch(t){}return t.message}}function sc(t,i,n){var r=n?"".concat(n," "):"";return s(["".concat(r).concat(rc(t,ec(i.error)||i.message,1e3)),rc(t,i.filename,100),I(i.lineno)?-1:i.lineno],function(t,i){if(!I(i)||!x(i.stack))return[];var n=i;return n.stack.match(Ya)?function(t,i){return i.split("\n").filter(function(t){return!!t.match(Ya)}).map(function(i){var n=i;n.indexOf("(eval ")>-1&&(n=n.replace(/eval code/g,"eval").replace(/(\(eval at [^()]*)|(\),.*$)/g,""));var r=n.replace(/^\s+/,"").replace(/\(eval code/g,"(").replace(/\(native code\)/,"").split(/\s+/).slice(1),e=tc(r.pop()),s=r.join(" "),u=["eval","<anonymous>"].indexOf(e[0])>-1?"":e[0];return Za(t,s,u,e[1],e[2])})}(t,n.stack):function(t,i){return i.split("\n").filter(function(t){return!t.match(Ja)}).map(function(i){var n=i;if(n.indexOf(" > eval")>-1&&(n=n.replace(/ line (\d+)(?: > eval line \d+)* > eval:\d+:\d+/g,":$1")),-1===n.indexOf("@")&&-1===n.indexOf(":"))return[n,"",-1,-1];var r=n.split("@"),e=tc(r.pop()),s=r.join("@");return Za(t,s,e[0],e[1],e[2])})}(t,n.stack)}(function(i){return es(t,i)},i.error),!0)}var uc=function(){function t(t,i,n){this.N=t,this.Ft=i,this.et=n,this.di=!1,this.vs=!1,this.ls=0,this.gi=[],this.ds=ui.DefaultOrgSettings.MaxConsoleLogPerPage,this.vt=t.window}return t.prototype.initializeMaxLogsPerPage=function(t){this.ds=t||ui.DefaultOrgSettings.MaxConsoleLogPerPage},t.prototype.ps=function(){return"\"[received more than ".concat(this.ds," messages]\"")},t.prototype.start=function(t){var i=this;if(t.ConsoleWatcher&&(this.et.add(this.vt,"error",!0,function(t){return i.ws(t)}),this.et.add(this.vt,"unhandledrejection",!0,function(t){var n,r="";t.reason instanceof Error?n=t.reason:r=Uu(i.N.wdx)(t.reason,1e3),i.ws({error:n,message:r,filename:"",lineno:0},"Uncaught (in promise)")},!0),!this.di))if(this.di=!0,this.Ft.enqueue({Kind:Y.REC_FEAT_SUPPORTED,Args:[Rt.Console,!0]}),this.vt.console)for(var n=function(t){var n=Su(r.N,r.vt.console,t);if(!n)return"continue";"assert"===t?n.before(function(n){var r=n.args;r[0]||i.gs(t,Array.prototype.slice.apply(r,[1]))}):n.before(function(n){var r=n.args;return i.gs(t,r)}),r.gi.push(n)},r=this,e=0,s=nc;e<s.length;e++)n(s[e]);else this.gs("log",["NOTE: Log messages cannot be captured on IE9"])},t.prototype.isEnabled=function(){return this.di},t.prototype.stop=function(){var t,i;if(null===(t=this.et)||void 0===t||t.clearAll(),this.di)for(this.di=!1;i=this.gi.pop();)i.disable()},t.prototype.logEvent=function(t,i,n){if(void 0===n&&(n="console"),!this.ys())return null;for(var r=-1===ic.indexOf(t)?[n,"log",rc(this.N.wdx,t,1e3)]:[n,t],e=0;e<i.length;++e)r.push(rc(this.N.wdx,i[e],1e3));return{Kind:Y.LOG,Args:r}},t.prototype.gs=function(t,i){if(0!==i.length){var n=this.logEvent(t,i);n&&this.Ft.enqueue(n)}},t.prototype.ws=function(t,i){void 0===i&&(i="Uncaught"),(void 0!==t.error||void 0!==t.message||t.filename||t.lineno)&&this.ys()&&this.Ft.enqueue({Kind:Y.ERROR,Args:sc(this.N.wdx,t,i)})},t.prototype.ys=function(){return!this.vs&&(this.ls==this.ds?(this.Ft.enqueue({Kind:Y.LOG,Args:["console","warn",this.ps()]}),this.vs=!0,!1):(this.ls++,!0))},t}();function oc(t){if(t.composed&&t.target){var i=t.target;if(i.nodeType==c&&i.shadowRoot){var n=t.composedPath();if(n.length>0)return n[0]}}return t.target}function hc(t){var i=oc(t);return!!Ke(i)&&!Ee(i)}var ac=function(){function t(t,i,n,r){this.N=t,this.Ft=i,this.bs=n,this.et=r}return t.prototype.start=function(t){var i=this;this.et.add(this.N.window,"click",!0,function(t){hc(t)&&i.Ss(t)}),this.et.add(this.N.window,"dblclick",!0,function(t){hc(t)&&i.ks(t)})},t.prototype.stop=function(t){this.et.clearAll()},t.prototype.Ss=function(t){var i=cc(this.N,t);if(_(i)){var n=Ce(oc(t));this.bs.onClick(n),this.Ft.enqueue({Kind:Y.RESOLVABLE_EVENT,Args:[Y.CLICK,i]})}},t.prototype.ks=function(t){var i=Ke(oc(t));i&&this.Ft.enqueue({Kind:Y.DBL_CLICK,Args:[i]})},t}();function cc(t,i){var n=oc(i),r=Ke(n);if(r){var e=[r,i.clientX,i.clientY],u=t.measurer.enqueue(function(){return{done:!0,result:fc(n)}},!0);return function(){return s(s([],e,!0),function(t,i){if(_(i)){var n=i();if(n&&n[0])return n[0]}return fc(t)}(n,u),!0)}}}function fc(t){var i=0,n=0,r=0,e=0;if(t&&t.getBoundingClientRect){var s=t.getBoundingClientRect();i=s.left,n=s.top,r=s.width,e=s.height}return[i,n,r,e]}var vc,lc,dc,pc,wc=function(){function t(t,i,n,r){this.N=t,this.Ft=i,this.bs=n,this.et=r,this.gi=[],i.registerInterceptor(this)}return t.prototype.start=function(t){var i,n=this;this.et.add(this.N.window,"click",!0,function(t){hc(t)&&n._s(t)});var r=Su(this.N,null===(i=this.N.window.Event)||void 0===i?void 0:i.prototype,"preventDefault",!0);r&&(this.gi.push(r),r.afterSync(function(t){return n.As(t.that)}))},t.prototype.stop=function(t){this.et.clearAll();for(var i=0,n=this.gi;i<n.length;i++)n[i].disable()},t.prototype.intercept=function(t){switch(t.Kind){case Y.RESIZE_DOCUMENT_CONTENT:case Y.RESIZE_LAYOUT:case Y.RESIZE_VISUAL:case Y.SCROLL_LAYOUT:case Y.SCROLL_VISUAL_OFFSET:this.Is()}},t.prototype._s=function(t){var i;(null===(i=this.Es)||void 0===i?void 0:i.target)===oc(t)&&this.Is()},t.prototype.As=function(t){var i,n=t.type;switch(n){case"touchend":var r=t,e=(null===(i=r.changedTouches)||void 0===i?void 0:i.length)>0;r.clientX=e?r.changedTouches[0].clientX:0,r.clientY=e?r.changedTouches[0].clientY:0,this.Ts(n,r);break;case"pointerdown":case"pointerup":this.Ts(n,t);break;default:"Unhandled event type: ".concat(n)}},t.prototype.Cs=function(t,i){var n=cc(this.N,i);if(_(n)){var r=Ce(oc(i));this.bs.onClick(r),this.Ft.enqueue({Kind:Y.RESOLVABLE_EVENT,Args:[Y.EVENT_CANCELED,function(){return[t,n()]}]})}},t.prototype.Ts=function(t,i){var n=this;this.Es||(this.Es={target:oc(i),timer:new Wu(this.N.wdx,this.N.window,function(){n.Es=void 0,n.Cs(t,i)}).start(ui.SyntheticClickTimeout)})},t.prototype.Is=function(){this.Es&&(this.Es.timer.stop(),this.Es=void 0)},t}();(lc=vc||(vc={}))[lc.Ajax=0]="Ajax",lc[lc.Animation=1]="Animation",lc[lc.Canvas=2]="Canvas",lc[lc.Console=3]="Console",lc[lc.CustomElement=4]="CustomElement",lc[lc.Dialog=5]="Dialog",lc[lc.Fullscreen=6]="Fullscreen",lc[lc.Input=7]="Input",lc[lc.Perf=8]="Perf",lc[lc.StyleSheet=9]="StyleSheet",(pc=dc||(dc={}))[pc.Scroll=1]="Scroll",pc[pc.Resize=2]="Resize";var gc=/^\s*((stylesheet)\s*)+$/i,mc=/^\s*((img|picture)\s*)+$/i,yc=function(){function t(t,i,n,r,e,s,u,o,h){var a=this;this.N=t,this.Ft=i,this.bs=n,this.et=e,this.xs=s,this.Rs=u,this.er=o,this.Qn=h,this.Ks=[],this.js={},this.Ms=!1,this.gi=[],this.Ps=!1,this.Os={},this.Ns=!1,this.Ls=new wu,this.vt=t.window,this.Xt=this.vt.document,this.Us=this.vt.location,this.Fs=this.vt.history,this.Bs=this.Us.href,this.Hs=[new ca(t,i),new ho(t,i,this.et.createChild()),new $a(t,i,o,this.Qn),new uc(t,this.Ft,this.et.createChild()),new Fa(t,i),new Ba(t,i),new Ua(t,i,this.et.createChild()),new Vu(t,i),new Bh(t,i,this.et.createChild(),this.Qn),new Ea(t,i,!!this.N.options.isWayfinder),new ac(t,i,n,this.et.createChild()),new wc(t,i,n,this.et.createChild())],this.Ds=function(t,i){void 0===i&&(i=.25);var n=t.window.IntersectionObserver;if(n)return new n(function(i){for(var n,r=0,e=i;r<e.length;r++){var s=e[r],u=s.target,o=s.intersectionRatio,h=Ke(u);h&&(null===(n=t.queue())||void 0===n||n.enqueue({Kind:Y.VIEWPORT_INTERSECTION,Args:[h,o]}))}},{threshold:[i,1]})}(t),this.Ws=new yh(t,r,this.Hi.bind(this),this.Ds,this.gn.bind(this),function(t){return a.Hs[vc.StyleSheet].processWayfinderLog(t)}),this.Ws.acceptVisitor(this)}return t.prototype.getResourceUploader=function(){return this.er},t.prototype.start=function(t){this.Ws.start();for(var i=0,n=this.Hs;i<n.length;i++)n[i].start(t);this.zs(t)},t.prototype.zs=function(t){var i=this;t.DisableCopyPasteListener||(this.et.add(this.vt,"copy",!1,this.qs.bind(this)),this.et.add(this.vt,"paste",!1,this.Vs.bind(this))),this.et.add(this.vt,"mousemove",!0,function(t){hc(t)&&i.$s(t)}),this.et.add(this.vt,"mousedown",!0,function(t){hc(t)&&i.Gs(t)}),this.et.add(this.vt,"mouseup",!0,function(t){hc(t)&&i.Qs(t)}),this.et.add(this.vt,"keydown",!0,function(t){i.Hs[vc.Input].onKeyboardChange(_c(t))}),this.et.add(this.vt,"keyup",!0,function(t){i.Hs[vc.Input].onKeyboardChange(_c(t))}),this.et.add(this.vt,"input",!0,function(t){i.Hs[vc.Input].onInputChange(oc(t))}),this.et.add(this.vt,"focus",!0,function(t){i.Xs(t,_c(t))},!0),this.et.add(this.vt,"blur",!0,function(t){i.Ys(t,_c(t))},!0),this.et.add(this.vt,"change",!0,function(t){i.Js(t,_c(t))},!0),this.et.add(this.vt,"touchstart",!0,function(t){hc(t)&&(i.Zs(t,Y.TOUCHSTART),i.tu())}),this.et.add(this.vt,"touchend",!0,function(t){hc(t)&&(i.Zs(t,Y.TOUCHEND),i.tu())}),this.et.add(this.vt,"touchmove",!0,function(t){hc(t)&&(i.Zs(t,Y.TOUCHMOVE),i.tu())}),this.et.add(this.vt,"touchcancel",!0,function(t){hc(t)&&i.Zs(t,Y.TOUCHCANCEL)}),this.et.add(this.vt,"play",!0,function(t){i.iu(t)}),this.et.add(this.vt,"pause",!0,function(t){i.nu(t)}),this.et.add(this.vt,"scroll",!0,function(t){t.bubbles?i.tu():i.ru(oc(t))}),this.et.add(this.vt,"resize",!1,function(){i.eu()}),this.et.add(this.vt,"submit",!1,function(t){i.su(t)}),this.et.add(this.vt,"focus",!1,function(){i.uu()}),this.et.add(this.vt,"blur",!1,function(){i.ou()}),this.et.add(this.vt,"popstate",!1,function(){i.hu()}),this.et.add(this.vt,"selectstart",!0,function(){i.au()}),this.et.add(this.Xt,"selectionchange",!0,function(){i.au()}),this.et.add(this.vt,"visibilitychange",!1,function(t){i.addVisibilityChangeEvent()});var n=this.vt.visualViewport;n?(this.et.add(n,"scroll",!0,function(){return i.tu()}),this.et.add(n,"resize",!0,function(){return i.eu()})):"onwheel"in this.vt?this.et.add(this.vt,"wheel",!0,function(){i.tu()}):"onmousewheel"in this.vt&&this.et.add(this.vt,"mousewheel",!0,function(){i.tu()});var r=Su(this.N,this.Fs,"pushState",!0);r&&(this.gi.push(r),r.afterSync(function(){return i.hu()}));var e=Su(this.N,this.Fs,"replaceState",!0);e&&(this.gi.push(e),e.afterSync(function(){return i.hu()}));for(var s=function(t){var n=Su(u.N,u.vt,t);if(!n)return"continue";u.gi.push(n),n.before(function(){i.Ft.enqueue({Kind:Y.MODAL_OPEN,Args:[t]})}).afterSync(function(){i.Ft.enqueue({Kind:Y.MODAL_CLOSE,Args:[t]})})},u=this,o=0,h=hi;o<h.length;o++)s(h[o]);if("function"==typeof this.vt.document.hasFocus&&this.Ft.enqueue({Kind:this.vt.document.hasFocus()?Y.WINDOW_FOCUS:Y.WINDOW_BLUR,Args:[]}),this.N.wdx.matchMedia)for(var a=function(t,n,r){var e=c.N.wdx.matchMedia(c.vt,r);if(!e)return"continue";var s=function(){e.matches&&i.Ft.enqueue({Kind:Y.MEDIA_QUERY_CHANGE,Args:[t,n]})};c.et.add(e,"change",!0,s),s()},c=this,f=0,v=[["any-pointer","coarse","not screen and (any-pointer: fine)"],["any-pointer","fine","only screen and (any-pointer: fine)"],["any-hover","none","not screen and (any-hover: hover)"],["any-hover","hover","only screen and (any-hover: hover)"],["pointer","none","(pointer: none)"],["pointer","coarse","(pointer: coarse)"],["pointer","fine","(pointer: fine)"],["prefers-color-scheme","no-preference","(prefers-color-scheme: no-preference)"],["prefers-color-scheme","light","(prefers-color-scheme: light)"],["prefers-color-scheme","dark","(prefers-color-scheme: dark)"]];f<v.length;f++){var l=v[f];a(l[0],l[1],l[2])}this.Ps=!0},t.prototype.initResourceUploading=function(){this.er.init(),this.Ms=!0},t.prototype.onDomLoad=function(){this.cu(),this.fu(!0),this.Ws.Wn($s)},t.prototype.onLoad=function(){var t=this,i=!1,n=Bs.wrap(this.N.wdx,function(){i||(i=!0,t.Hs[vc.Perf].onLoad(),t.vu(),t.fu())},"eventwatcher#onLoad");new Wu(this.N.wdx,this.N.window,n,0).start(),xr(this.N.wdx,this.N.window).then(n)},t.prototype.onNavigate=function(){this.hu()},t.prototype.ajaxWatcher=function(){return this.Hs[vc.Ajax]},t.prototype.consoleWatcher=function(){return this.Hs[vc.Console]},t.prototype.perfWatcher=function(){return this.Hs[vc.Perf]},t.prototype.bundleEvents=function(){var t=this;return this.Ft.enqueueSimultaneousEventsIn(function(i){var n,r=Q(t.N.wdx),e=t.Ws.processMutations(i);if(e.length>0){var s=Q(t.N.wdx)-r;e.push({Kind:Y.TIMING,Args:[[it.Internal,rt.Serialization,st.ProcessMut,i,s]],When:i})}for(var u=0,o=t.Hs;u<o.length;u++){var h=o[u];null===(n=h.tick)||void 0===n||n.call(h)}return t.fu(),e})},t.prototype.stop=function(t){var i,n;if(this.Ps){this.Ps=!1,this.Hs[vc.Perf].onLoad(),this.N.measurer.flush(),this.Ft.processEvents(),this.N.taskQueue.flush(),this.Qn.flush(),this.Ws.stop();for(var r=0,e=this.Hs;r<e.length;r++)e[r].stop(t);null===(i=this.Ds)||void 0===i||i.disconnect(),null===(n=this.et)||void 0===n||n.clearAll(),this.Ls=new wu;for(var s=0,u=this.gi;s<u.length;s++)u[s].disable();this.gi=[],this.Ft.shutdown(t)}},t.prototype.recordingIsDetached=function(){return this.Ws.recordingIsDetached()},t.prototype.preVisit=function(t,i){var n=this,r=t.id,e=t.name;switch(e){case"#document":case"#document-fragment":if(this.Os[r]=r,"#document-fragment"===e){var s=this.et.createChild();s.add(i,"scroll",!0,function(t){return n.ru(oc(t))}),s.add(i,"change",!0,function(t){t.composed||n.Js(t,_c(t))},!0),this.Ls.set(i,s)}var u=i;try{if(!u.adoptedStyleSheets||0===u.adoptedStyleSheets.length)break}catch(t){break}this.Hs[vc.StyleSheet].onSetAdoptedStyleSheets(i);break;case"INPUT":case"TEXTAREA":case"SELECT":this.Hs[vc.Input].addInput(i);break;case"LINK":case"STYLE":case"style":var o=i,h=o.sheet;if(!h)break;var a=this.Hs[vc.StyleSheet];a.ensureHook(h),h.disabled&&a.onDisableSheet(h,!0),"STYLE"===e||"style"===e?a.snapshotEl(o):Ra(this.N.wdx,o)&&(a.snapshotEl(o),this.et.add(o,"load",!1,function(){a.snapshotEl(o)}));break;case"BODY":this.fu(),this.au();break;case"FRAME":case"IFRAME":this.xs(i)}},t.prototype.visit=function(t){t[0];var i=t[1],n=t[2],r=t[3],e=t[4];this.lu(i,n,r),this.du(i,n,r,e)},t.prototype.lu=function(t,i,n){var r=this,e=i.name,s=i.type;if(t===lo.New&&10!==s){switch(e){case"VIDEO":case"AUDIO":n.paused||this.Ft.enqueue({Kind:Y.PLAY,Args:[Ke(n)]});break;case"DIALOG":var u=n;u.open&&this.Ft.enqueue({Kind:Y.DIALOG,Args:[Ke(u),!0,Da(this.N.wdx,u)]});break;case"CANVAS":this.Hs[vc.Canvas].flush(n);break;default:e&&"#"!==e[0]&&e.indexOf("-")>-1&&this.Hs[vc.CustomElement].onCustomNodeVisited(n)}"scrollLeft"in n&&"scrollTop"in n&&this.N.measurer.enqueue(function(){var t=n;0==t.scrollLeft&&0==t.scrollTop||r.ru(t)})}},t.prototype.du=function(t,i,n,r){var e=this,s=this.N.recording.flags().DisableImgUrlPrivacy;if(!s||this.Ms){var u=i.tag;if(i.type===c&&r&&!function(t,i){return void 0===i&&(i=Ce(t)),Ee(t,i)||Te(t,i)}(n)){var o="unknown";"link"===u&&gc.test(r.rel)?o="css":mc.test(null!=u?u:"")&&(o="img");var h=function(t,i,n,r){for(var e,s,u=[],o=0,h=kc;o<h.length;o++){var a=h[o];r[a]&&Sc[a][i]&&u.push(r[a])}if("link"===i&&r.href&&(e=r.rel)&&e.indexOf("stylesheet")>-1&&u.push(r.href),("img"===i||"source"===i)&&(s=r.srcset)&&null==s.match(/^\s*$/))for(var c=0,f=function(t,i){void 0===i&&(i=!0);var n="".concat(t.replace(/\s+/g," "),",").match(i?ke:Se);if(!n)return[];for(var r=[],e=0,s=n;e<s.length;e++){var u=s[e];r.push(u.replace(/^[, ]+|[, ]+$/g,""))}return r}(s,!0);c<f.length;c++){var v=f[c];u.push($e(t,v.trim().split(/\s+/)[0],{source:"dom",type:"srcset"}))}var l=n;if(r.style&&l.style){var d=l.style.backgroundImage;if(d&&d.length<=300){var p=void 0;for(ce.lastIndex=0;p=ce.exec(d);){var w=p[1];w&&u.push($e(t,w.trim(),{source:"dom",type:"css"}))}}}return u}(this.N.wdx,u,n,r);if(0===h.length){if(s)return;for(var a=0,f=kc;a<f.length;a++){var v=r[f[a]];v&&h.push(v)}h.length>0&&this.N.taskQueue.enqueue(function(){return e.pu(h,!1,o)})}else this.N.taskQueue.enqueue(function(){return e.pu(h,!0,o)})}}},t.prototype.Hi=function(t){var i,n=t.id,r=Re(n),e=t.name;if(r){switch(e){case"#document":case"#document-fragment":delete this.Os[n];var s=this.Ls.get(r);s&&(this.et.clearChild(s),this.Ls["delete"](r));break;case"IFRAME":this.Rs(r);break;case"LINK":case"STYLE":case"style":var u=r.sheet;u&&this.Hs[vc.StyleSheet].removeHook(u);break;case"INPUT":case"TEXTAREA":case"SELECT":var o=r;this.Hs[vc.Input].removeInput(o)}if("function"==typeof r.getElementsByTagName)for(var h=null!==(i=r.getElementsByTagName("iframe"))&&void 0!==i?i:[],a=0;a<h.length;a++){var c=h[a];this.Rs(c)}}},t.prototype.gn=function(){var t=this;this.Ns||(this.Ns=!0,this.N.measurer.enqueue(function(){for(var i in t.Ns=!1,t.Os){var n=xe(t.Os[i]);if(n){var r=Re(n.id);r&&t.Hs[vc.Animation].snapshot(r)}}}))},t.prototype.pu=function(t,i,n){var r;void 0===i&&(i=!1),window;for(var e=0,s=t;e<s.length;e++){var u=s[e];/^(([a-z\-_]+):|\/\/)/i.test(u)&&(this.Ms&&i&&(void 0===r&&(r=$e(this.N.wdx,Vr(this.vt),{source:"page",type:"base"})),this.er.uploadIfNeeded(this.vt,qr(r,u),n)),this.Qn.record(u))}window},t.prototype.$s=function(t){var i=Ke(oc(t));this.Ft.enqueue({Kind:Y.MOUSEMOVE,Args:i?[t.clientX,t.clientY,i]:[t.clientX,t.clientY]})},t.prototype.Gs=function(t){this.Ft.enqueue({Kind:Y.MOUSEDOWN,Args:[t.clientX,t.clientY]})},t.prototype.Qs=function(t){this.Ft.enqueue({Kind:Y.MOUSEUP,Args:[t.clientX,t.clientY]})},t.prototype.Zs=function(t,i){if(void 0!==t.changedTouches)for(var n=0;n<t.changedTouches.length;++n){var r=t.changedTouches[n];isNaN(parseInt(r.identifier,10))&&(r.identifier=0);var e=[r.identifier,r.clientX,r.clientY];this.Ft.enqueue({Kind:i,Args:e})}},t.prototype.iu=function(t){var i=Ke(oc(t));i&&this.Ft.enqueue({Kind:Y.PLAY,Args:[i]})},t.prototype.nu=function(t){var i=Ke(oc(t));i&&this.Ft.enqueue({Kind:Y.PAUSE,Args:[i]})},t.prototype.uu=function(){this.Ft.enqueue({Kind:Y.WINDOW_FOCUS,Args:[]})},t.prototype.ou=function(){this.Ft.enqueue({Kind:Y.WINDOW_BLUR,Args:[]})},t.prototype.fu=function(t){var i=this;void 0===t&&(t=!1),this.N.measurer.enqueue(function(){return i.wu(t)})},t.prototype.wu=function(t){var i=this;void 0===t&&(t=!1);var n,r=(n=this.vt.document).scrollingElement||n.body||n.documentElement,e=Ke(r);if(e){var s=function(t){i.Ft.enqueue(t)},u=function(t,i){var n,r=t.documentElement.getBoundingClientRect(),e=null!==(n=t.body)&&void 0!==n?n:{scrollHeight:0,scrollWidth:0};return{width:Math.max(r.width,i.scrollWidth,e.scrollWidth),height:Math.max(r.height,i.scrollHeight,e.scrollHeight)}}(this.vt.document,r);Tu(u,this.gu)||(this.gu=u,s({Kind:Y.RESIZE_DOCUMENT_CONTENT,Args:[u.width,u.height]}));var o,h,a,c,f=Mu(this.N.wdx,this.N.window,t?void 0:this.mu),v=function(t,i,n){var r=i.visualViewport;if(r)return r;var e=n;return void 0===e&&(e=Mu(t,i)),new Ou(i,e)}(this.N.wdx,this.N.window,f);f.hasKnownPosition?(Eu(f,this.mu)||s({Kind:Y.SCROLL_LAYOUT,Args:[e,f.pageLeft,f.pageTop]}),o=v,(h=this.yu)&&o.offsetLeft==h.offsetLeft&&o.offsetTop==h.offsetTop||s({Kind:Y.SCROLL_VISUAL_OFFSET,Args:[e,v.offsetLeft,v.offsetTop]})):Eu(v,this.yu)||s({Kind:Y.SCROLL_LAYOUT,Args:[e,v.pageLeft,v.pageTop]}),function(t,i){return i&&t.width==i.width&&t.height==i.height&&t.clientWidth==i.clientWidth&&t.clientHeight==i.clientHeight}(f,this.mu)||s({Kind:Y.RESIZE_LAYOUT,Args:Lu(f)}),Tu(v,this.yu)||s({Kind:Y.RESIZE_VISUAL,Args:[v.width,v.height]}),this.mu=((c=Cu(a=f)).clientWidth=a.clientWidth,c.clientHeight=a.clientHeight,c),this.yu=function(t){var i=Cu(t);return i.offsetLeft=t.offsetLeft,i.offsetTop=t.offsetTop,i}(v)}},t.prototype.bu=function(t,i){var n=this;t in this.js||(this.js[t]=i,new Wu(this.N.wdx,this.N.window,function(){n.N.measurer.enqueue(function(){if(t in n.js){var i=n.js[t];delete n.js[t],i()}})},ui.ScrollSampleInterval).start())},t.prototype.tu=function(){var t=this;this.bu(dc.Scroll,function(){return t.wu(!1)})},t.prototype.eu=function(){var t=this;this.bu(dc.Resize,function(){return t.wu(!0)})},t.prototype.ru=function(t){var i=this,n=Ke(t);n&&this.bu(n,function(){if(Ke(t)===n){var r=t;n&&"number"==typeof r.scrollLeft&&(i.Ft.enqueue({Kind:Y.RESIZE_SCROLLABLE_ELEMENT_CONTENT,Args:[n,r.scrollWidth,r.scrollHeight]}),i.Ft.enqueue({Kind:Y.SCROLL_LAYOUT,Args:[n,r.scrollLeft,r.scrollTop]}),Cr(i.N.wdx,i.N.window).then(function(){i.Ws.resizer().isObserved(n)||i.Ws.resizer().observe(t)}))}})},t.prototype.cu=function(){this.Ft.enqueue({Kind:Y.DOMLOADED,Args:[]})},t.prototype.vu=function(){this.Ft.enqueue({Kind:Y.LOAD,Args:[]})},t.prototype.getNavigateEvent=function(t,i){void 0===i&&(i=Y.NAVIGATE);var n={Kind:i,Args:[$e(this.N.wdx,t,{source:"event",type:Y.NAVIGATE})]};return i===Y.ENTRY_NAVIGATE?(n.Args.push(this.Su()),n):n},t.prototype.hu=function(){var t=this.Us.href;this.Bs!=t&&(this.Bs=t,this.bs.onNavigate(t),this.Ft.enqueue(this.getNavigateEvent(t)))},t.prototype.Su=function(){try{var t=this.vt.performance;if(!t||!t.navigation)return"unknown";switch(t.navigation.type){case PerformanceNavigation.TYPE_NAVIGATE:return"navigate";case PerformanceNavigation.TYPE_RELOAD:return"reload";case PerformanceNavigation.TYPE_BACK_FORWARD:return"back_forward";default:return"unknown"}}catch(t){return"unknown"}},t.prototype.su=function(t){var i=Ke(oc(t));i&&this.Ft.enqueue({Kind:Y.FORM_SUBMIT,Args:[i]})},t.prototype.Xs=function(t,i){var n=Ke(oc(t));n&&this.Ft.enqueue({Kind:Y.FOCUS,Args:[n,i]})},t.prototype.Ys=function(t,i){var n=Ke(oc(t));n&&this.Ft.enqueue({Kind:Y.BLUR,Args:[n,i]})},t.prototype.Js=function(t,i){this.Hs[vc.Input].onChange(oc(t),i)},t.prototype.au=function(){var t=this;this.N.measurer.enqueue(function(){var i;try{i=t.selectionArgs()}catch(t){return}for(var n=!1,r=0;r<4;r++)if(t.Ks[r]!==i[r]){n=!0;break}n&&(t.Ks=i,t.Ft.enqueue({Kind:Y.SELECT,Args:i}))})},t.prototype.selectionArgs=function(){if(!this.vt.getSelection)return[];var t=this.vt.getSelection();if(!t)return[];if("None"==t.type)return[];if("Caret"==t.type){var i=Ke(t.anchorNode);return i?[i,t.anchorOffset]:[]}if(!t.anchorNode||!t.focusNode)return[];var n=bc(this.N.wdx,t.anchorNode,t.anchorOffset),r=n[0],e=n[1],s=bc(this.N.wdx,t.focusNode,t.focusOffset),u=s[0],o=s[1],h=Boolean(r.compareDocumentPosition(u)&Node.DOCUMENT_POSITION_FOLLOWING),a=h?[r,u]:[u,r],c=a[0],f=a[1],v=h?[e,o]:[o,e],l=v[0],d=v[1];for(Ke(c)||(l=0);c&&!Ke(c)&&c!=f;)c=w(this.N.wdx,c)||m(this.N.wdx,c);for(Ke(f)||(d=0);f&&!Ke(f)&&f!=c;)f=g(this.N.wdx,f)||m(this.N.wdx,f);if(c==f&&l==d)return[];var p=Ke(c),y=Ke(f);return c&&f&&p&&y?[p,l,y,d,h]:[]},t.prototype.qs=function(){this.Ft.enqueue({Kind:Y.COPY,Args:[]})},t.prototype.Vs=function(){this.Ft.enqueue({Kind:Y.PASTE,Args:[]})},t.prototype.addVisibilityChangeEvent=function(){this.Ft.enqueue({Kind:Y.VISIBILITY_STATE,Args:[document.visibilityState]}),"hidden"===document.visibilityState&&this.Ft.singSwanSong(bt.Hidden)},t}();function bc(t,i,n){var r=i,e=y(0,r);if(!e)return[r,n];r=e;for(var s=0;s<n-1;s++){var u=w(0,r);if(!u)return[r,0];r=u}return[r,0]}var Sc={src:{img:!0,embed:!0,source:!0},href:{use:!0,image:!0},data:{object:!0}},kc=["src","href","data"];function _c(t){var i;return!!(null!==(i=t[yi])&&void 0!==i&&i||t.isTrusted)}function Ac(t){return t.Kind===Y.MOUSEMOVE||t.Kind===Y.TOUCHMOVE||t.Kind===Y.SCROLL_LAYOUT||t.Kind===Y.SCROLL_VISUAL_OFFSET||t.Kind===Y.RESIZE_VISUAL}var Ic,Ec,Tc=function(){function t(t,i){this.de=t,this.ku=i,this._u=[],this.Au=0}return t.prototype.add=function(t){this._u.length>0&&this._u[this._u.length-1].When===t.When&&this._u.pop(),0===this._u.length?(this.de.push(t),this.Au=t.When):t.When>this.Au&&(this.Au=t.When),this._u.push(t)},t.prototype.finish=function(t,i){void 0===i&&(i=[]);var n=this._u.length;if(n<=1)return!1;for(var r=[],e=this._u[0].When,u=this._u[n-1].When,o=u-e!=0?u-e:1,h=0;h<this.ku.length;++h){var a=this.ku[h],c=this._u[0].Args[a],f=(this._u[1].When-e)/o,v=(this._u[1].Args[a]-c)/f,l=this._u[n-2].Args[a],d=(u-this._u[n-2].When)/o,p=this._u[n-1].Args[a],w=(p-l)/d;r.push(c,p,v,w)}return this._u[0].Kind=t,this._u[0].Args=s(s([this.Au],i,!0),r,!0),!0},t.prototype.evts=function(){return this._u},t}();(Ec=Ic||(Ic={}))[Ec.rageWindowMillis=2e3]="rageWindowMillis",Ec[Ec.defaultRageThreshold=5]="defaultRageThreshold",Ec[Ec.rageThresholdIfPageChanges=8]="rageThresholdIfPageChanges",Ec[Ec.thresholdChangeQuiescenceMillis=2e3]="thresholdChangeQuiescenceMillis";var Cc,xc,Rc,Kc=function(){function t(t,i){var n,r;void 0===i&&(i=X),this.N=t,this.Iu=i,this.Eu=new bo,this.Tu=Ic.defaultRageThreshold,this.Cu=-1,this.xu=new WeakMap;var e=t.recording.pageResponse();if(!e)throw new Error("Attempt to construct EasyBake before rec/page response is set.");for(var s=[".fs-ignore-rage-clicks",".fs-ignore-rage-clicks *"],u=0,o=null!==(r=null===(n=e.BehaviorSignalSettings)||void 0===n?void 0:n.ElementBlocks)&&void 0!==r?r:[];u<o.length;u++){var h=o[u];h.Signals.indexOf(Gt.SignalRageClick)>-1&&(s.push(h.Selector),s.push("".concat(h.Selector," *")))}var a=s.join(", ");Uo(this.N.wdx,a)?this.Ru=[a]:this.Ru=s}return t.prototype.Ku=function(t){var i=this.xu.get(t);if(void 0!==i)return i;for(var n=0,r=this.Ru;n<r.length;n++){var e=r[n];if(this.N.wdx.elMatches(t,e))return this.xu.set(t,!0),!0}return this.xu.set(t,!1),!1},t.prototype.onEvent=function(t){var i=t.Kind===Y.RESOLVABLE_EVENT?t.Args[0]:t.Kind;if(function(t){switch(t){case Y.VALUECHANGE:case Y.SCROLL_LAYOUT:case Y.SCROLL_LAYOUT_CURVE:case Y.SCROLL_VISUAL_OFFSET:case Y.SCROLL_VISUAL_OFFSET_CURVE:case Y.MUT_INSERT:case Y.MUT_REMOVE:case Y.MUT_ATTR:case Y.MUT_SHADOW:case Y.MUT_TEXT:case Y.NAVIGATE:case Y.LOAD:case Y.FOCUS:case Y.BLUR:case Y.SELECT:case Y.FORM_SUBMIT:case Y.PLAY:case Y.PAUSE:case Y.MODAL_OPEN:case Y.MODAL_CLOSE:return!0}return!1}(i)&&this.Tu!==Ic.rageThresholdIfPageChanges)return this.Tu=Ic.rageThresholdIfPageChanges,void(this.Cu=this.Iu(this.N.wdx));if(i===Y.CLICK){var n=this.Iu(this.N.wdx),r=Re(gh(this.N.wdx,[t])[0].Args[0]);if(r&&Ke(r)){var e=r.nodeType===c?r:m(this.N.wdx,r);if(e&&!this.Ku(e)){var s=me(r);if(!s||"textarea"!==s&&"select"!==s&&("input"!==s||"submit"===r.type)){this.Eu.push(n);for(var u=n-Ic.rageWindowMillis;;){var o=this.Eu.first();if(!(void 0!==o&&o<u))break;this.Eu.shift()}if(this.Cu<n-Ic.thresholdChangeQuiescenceMillis&&(this.Tu=Ic.defaultRageThreshold),this.Eu.size()>=this.Tu){var h=this.N.recording.getCurrentSessionURL,a={eventStartTimeStamp:this.Eu.first(),eventEndTimeStamp:n,eventReplayUrlAtStart:h(),eventReplayUrlAtCurrentTime:h(!0)};!function(t,i,n){var r,e="fullstory/rageclick";try{r=new CustomEvent(e,{detail:n,bubbles:!0,cancelable:!0})}catch(t){(r=document.createEvent("customevent")).initCustomEvent(e,!0,!0,n)}t.wdx.setWindowTimeout(t.window,Bs.wrap(t.wdx,function(){i.dispatchEvent(r)},"dispatchRageClickEvent"),0)}(this.N,r,a),this.Tu=Ic.defaultRageThreshold,this.Eu=new bo}}}}}},t}(),jc="HibernationTimerExceeded",Mc=2*ui.PageInactivityTimeout,Pc=function(){function t(t){this.N=t,this.ju=this.N.time.wallTime(),this.Mu=!1}return t.prototype.getLastUserActivityTS=function(){return this.ju},t.prototype.getMsSinceLastUserActivity=function(){return this.N.wdx.mathFloor(this.N.time.wallTime()-this.ju)},t.prototype.resetUserActivity=function(){this.ju=this.N.time.wallTime()},t.prototype.isHibernating=function(){return this.Mu},t.prototype.setHibernating=function(){this.Mu=!0},t}(),Oc=function(){function t(t,i,n,r){void 0===r&&(r=Wu),this.N=t,this.Pu=i,this.Ft=n,this.Ou=!1,this.Nu=!1,this.Lu=!1,this.Uu=!1,this.Fu={},this.Bu=new r(this.N.wdx,this.N.window,this.Hu.bind(this),ui.HeartbeatInterval),this.Du=new r(this.N.wdx,this.N.window,this.Wu.bind(this),ui.PageInactivityTimeout)}return t.prototype.getUserActivityModel=function(){return this.Pu},t.prototype.manualHibernateCheck=function(){this.Pu.isHibernating()||this.Pu.getMsSinceLastUserActivity()>=ui.PageInactivityTimeout+5e3&&this.Wu()},t.prototype.intercept=function(t){var i=function(t){switch(t){case Y.MOUSEDOWN:case Y.MOUSEMOVE:case Y.MOUSEMOVE_CURVE:case Y.MOUSEUP:case Y.TOUCHSTART:case Y.TOUCHEND:case Y.TOUCHMOVE:case Y.TOUCHMOVE_CURVE:case Y.TOUCHCANCEL:case Y.CLICK:case Y.SCROLL_LAYOUT:case Y.SCROLL_LAYOUT_CURVE:case Y.SCROLL_VISUAL_OFFSET:case Y.SCROLL_VISUAL_OFFSET_CURVE:case Y.NAVIGATE:return!0}return!1}(t.Kind===Y.RESOLVABLE_EVENT?t.Args[0]:t.Kind);this.zu(t),!this.Ou&&i&&(this.Pu.isHibernating()?this.qu():this.Vu())},t.prototype.shutdown=function(){this.Bu.stop(),this.Du.stop()},t.prototype.Vu=function(t){var i=this;void 0===t&&(t=!0),this.Lu||(this.Pu.resetUserActivity(),this.Bu.start(),this.Du.start(),t&&(this.Lu=!0,xr(this.N.wdx,this.N.window).then(function(){i.Lu=!1})))},t.prototype.start=function(){var t=this.N.recording.heartbeatInterval();this.$u(t),this.Vu(!1)},t.prototype.Gu=function(){return this.Pu.isHibernating()||this.Ou},t.prototype.$u=function(t){var i=this.Bu.isRunning();this.Bu.start(t),i||this.Bu.stop()},t.prototype.Hu=function(){var t=this.Pu.getMsSinceLastUserActivity();t<=ui.PageInactivityTimeout&&this.Ft.enqueue({Kind:Y.HEARTBEAT,Args:[t]}),this.Bu.start()},t.prototype.Wu=function(){if(!this.Pu.isHibernating()){var t=!1;this.Pu.getMsSinceLastUserActivity()<=Mc?this.Ft.enqueue({Kind:Y.UNLOAD,Args:[bt.Hibernation]}):t=!0;try{this.Ou=!0,this.Pu.setHibernating(),this.shutdown(),this.Ft.onHibernate(t)}finally{this.Ou=!1}}},t.prototype.qu=function(){this.Nu||(this.Nu=!0,this.N.recording.splitPage(bt.Hibernation))},t.prototype.zu=function(t){this.manualHibernateCheck();var i=W(t.PIds,t.FId),n=function(t){var i=t.When;return"number"==typeof i?i:0}(t),r=this.Fu[i]||0,e=this.Gu(),s=this.Qu(n,r);s&&(this.Uu=!0),!e&&s&&this.Wu(),this.Uu?function(t,i,n){var r=i;i.Kind===Y.RESOLVABLE_EVENT&&(r=gh(t,[i])[0]);var e=ws(t,r,jc,jc);i.When=n,i.Kind=e.Kind,i.Args=e.Args}(this.N.wdx,t,r):this.Fu[i]=n},t.prototype.Qu=function(t,i){if(t-i<=Mc)return!1;for(var n in this.Fu){var r=this.Fu[n];if(this.N.wdx.mathAbs(t-r)<=Mc)return!1}return!0},t}(),Nc=function(){function t(){}return t.prototype.encode=function(t){return t},t}(),Lc=function(){function t(){this.dict={idx:-1,map:{}},this.nodeCount=1,this.startIdx=0}return t.prototype.encode=function(i){if(0==i.length)return[];var n,r,e=i[0],s=Object.prototype.hasOwnProperty.call(this.dict.map,e)?this.dict.map[e]:void 0,u=[],o=1;function h(){s?o>1?u.push([s.idx,o]):u.push(s.idx):u.push(e)}for(n=1;n<i.length;n++)if(r=i[n],s&&Object.prototype.hasOwnProperty.call(s.map,r))o++,e=r,s=s.map[r];else{h();var a=this.startIdx+n-o;null==s&&this.nodeCount<t.MAX_NODES&&(s={idx:a,map:{}},this.dict.map[e]=s,this.nodeCount++),s&&this.nodeCount<t.MAX_NODES&&(s.map[r]={idx:a,map:{}},this.nodeCount++),o=1,e=r,s=Object.prototype.hasOwnProperty.call(this.dict.map,r)?this.dict.map[r]:void 0}return h(),this.startIdx+=i.length,u},t.MAX_NODES=1e4,t}(),Uc=function(){function t(t){this.Xu=t,this.Yu=0}return t.newBudget=function(i){return void 0===i&&(i=256e3),new t(i)},t.prototype.add=function(t){this.Yu+=t},t.prototype.isOver=function(){return this.Yu>this.Xu},t.prototype.reset=function(){this.Yu=0},t}(),Fc=((Cc={})[Y.AJAX_REQUEST]=!0,Cc[Y.PERF_ENTRY]=!0,Cc[Y.RESOURCE_TIMING_BUFFER_FULL]=!0,Cc),Bc=function(){function t(t,i){void 0===i&&(i=Uc.newBudget(t.options.networkBudget)),this.N=t,this.Ju=i,this.Ft=new bo,this.Zu=1,this.io={},this.no=!1,this.ro=[],this.eo=!0,this.init()}return t.prototype.init=function(){if(1===this.Zu){var t,i=!!this.N.recording.flags().UseCompression&&"CompressionStream"in(t=this.N.window)&&"TextEncoderStream"in t;this.eo=!!this.N.recording.flags().UseLZEncoding||!i}},t.prototype.size=function(){return this.Ft.size()},t.prototype.add=function(t){var i=!0;switch(t.Kind){case Y.SET_FRAME_BASE:case Y.MUT_INSERT:case Y.MUT_SHADOW:case Y.RESOLVABLE_EVENT:i=!1}Fc[t.Kind]||(this.no=!0),this.Ft.push([t,i?gs(this.N.wdx,t):void 0])},t.prototype.nextBundle=function(i){if(0!==this.Ft.size())return i.flush||t.forceFlush||this.no?this.so(i):void 0},t.prototype.addEndMarkerEvent=function(t){this.ro.push(t)},t.prototype.persistEndMarkerEventsTo=function(t){for(var i=0,n=this.ro;i<n.length;i++){var r=n[i];t.addEndMarkerEvent(r)}},t.prototype.so=function(t){var i=this.uo(t);if(i){0===this.Ft.size()&&(this.no=!1);var n=i[0],r=i[1];return function(t,i,n){var r="{\"Seq\":".concat(t,",\"When\":").concat(i,",\"Evts\":[").concat(n,"]}");return[t,{type:"string",data:r},r.length]}(this.Zu++,n,r)}},t.prototype.uo=function(t){Ws(this.N.wdx,this.Ft.size()>0,"builder#_serializeEvents"),this.Ju.reset();var i,n,r=this.Ft.first()[0],e=Wc(r),s=this.oo(t),u=s[0],o=s[1];if(this.ho=(i=o,n=this.ho,i?n?Wc(i)>=Wc(n)?i:n:i:n),!this.ao(t))return this.co(u,this.ho),this.ho=void 0,[e,Hc(u)];this.Ft.unshift([r,Hc(u)])},t.prototype.oo=function(t){for(var i,r=[],e=[],u=this.Ft.shift();u;u=this.Ft.shift()){var o=u[0],h=u[1];if(i=o,void 0===h){var a=gh(this.N.wdx,[o])[0];if(this.eo){var c=this.fo(a),f=c[0],v=c[1];h=gs(this.N.wdx,f),e.push.apply(e,v)}else{var l=Q(this.N.wdx);h=gs(this.N.wdx,a),e.push(Q(this.N.wdx)-l)}}if(r.push(h),this.Ju.add(h.length),!t.flush&&this.Ju.isOver())break}return i&&function(t,i,r,e){r.length<=0||i.push(function(t,i,r){var e=r[0],u=r.slice(1);return gs(t,n(n({},zc(i)),{Kind:Y.TIMING,Args:[s([it.Internal,rt.Serialization,st.LzEncoding,Wc(i),e],u.map(function(t){return[st.LzEncoding,t]}),!0)]}))}(t,e,r))}(this.N.wdx,r,e,i),[r,i]},t.prototype.ao=function(t){return!(!this.N.recording.flags().UseMinNetworkBudget||t.bypassMinBudget||t.flush||this.Ju.isOver())},t.prototype.co=function(t,i){Ws(this.N.wdx,!!i,"builder#_finalizeQueue"),i&&function(t,i,r,e){Ws(t,r.length>0,"builder#addMarkers");for(var s=0,u=r;s<u.length;s++){var o=u[s];i.push(gs(t,n(n({},zc(e)),o)))}}(this.N.wdx,t,this.ro,i)},t.prototype.fo=function(t){var i=[];switch(t.Kind){case Y.SET_FRAME_BASE:var n=W(t.PIds,t.FId);delete this.io[n];break;case Y.MUT_INSERT:var r=Q(this.N.wdx);t.Args[2]=this.vo(t.PIds,t.FId,t.Args[2]),i.push(Q(this.N.wdx)-r);break;case Y.MUT_SHADOW:r=Q(this.N.wdx),t.Args[1]=this.vo(t.PIds,t.FId,t.Args[1]),i.push(Q(this.N.wdx)-r)}return[t,i]},t.prototype.vo=function(t,i,n){void 0===t&&(t=[]),void 0===i&&(i=0);var r=W(t,i);return this.io[r]||(this.io[r]=this.eo?new Lc:new Nc),this.io[r].encode(n)},t.forceFlush=!1,t}();function Hc(t){return t.join(",")}function Dc(t,i,n){var r=new Bc(t,n);return i&&i.persistEndMarkerEventsTo(r),r}function Wc(t){var i=t.When;return"number"==typeof i?i:0}function zc(t){return{When:Wc(t),FId:t.FId,PIds:t.PIds}}function qc(t,i,n,s,u,o){return void 0===s&&(s=Wu),void 0===o&&(o=5e3),r(this,void 0,mr,function(){var h;return e(this,function(a){return h=0,[2,function a(){return r(this,void 0,mr,function(){var r,c;return e(this,function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),r=xc.NoRetry,[4,n()];case 1:return r===e.sent()?[2]:[3,3];case 2:return c=e.sent(),"retryWithBackoff caught error: ".concat(c),[3,3];case 3:return"retryWithBackoff retrying ".concat(h+1),[4,$c(t,i,Vc(t,h++,o),s,u)];case 4:return e.sent(),[2,a()]}})})}()]})})}function Vc(t,i,n){void 0===n&&(n=5e3);var r=t.mathMin(ui.BackoffMax,t.mathPow(2,i)*n);return r+.25*t.mathRandom()*r}function $c(t,i,n,r,e){return new mr(function(s){var u=new r(t,i,function(){return s()},n).start();null==e||e(u)})}(Rc=xc||(xc={}))[Rc.NoRetry=0]="NoRetry",Rc[Rc.Retry=1]="Retry";var Gc=function(){function t(t,i,n){void 0===n&&(n=Wu),this.N=t,this.lo=i,this["do"]=n,this.po=!1,this.wo=0,this.mo=!1,this.yo=16e6,this.bo=1e4,this.So=100,this.ko=1e3,this._o=Dc(this.N,void 0,Uc.newBudget(this.bo))}return t.prototype.addEndMarkerEvent=function(t){this._o.addEndMarkerEvent(t)},t.prototype.intercept=function(t){if(this.mo)switch(t.Kind){case Y.SYS_CUSTOM:case Y.SYS_SETVAR:this._o.add(t),this.Ao()}},t.prototype.startPipeline=function(t){this.N.recording.flags().EnableRecEvents&&(this.Io=t.isNewSession,this.Eo=t.orgId,this.To=t.pageId,this.Co=t.serverPageStart,this.xo=t.sessionId,this.Ro=t.userId,this.mo="boolean"==typeof this.Io&&x(this.Eo)&&x(this.Ro)&&x(this.xo)&&x(this.To),Ws(this.N.wdx,this.mo,"fastlane#options"),this.Ao())},t.prototype.stopPipeline=function(){var t;this.mo=!1,null===(t=this.jt)||void 0===t||t.stop(),this._o=Dc(this.N,this._o,Uc.newBudget(this.bo))},t.prototype.Ao=function(){var t;this.mo&&!(null===(t=this.jt)||void 0===t?void 0:t.isRunning())&&(this.jt||(this.jt=new this["do"](this.N.wdx,this.N.window,this.Ko.bind(this),this.So)),this.jt.start())},t.prototype.Ko=function(){return r(this,void 0,mr,function(){var t,i,n=this;return e(this,function(s){switch(s.label){case 0:if(!this.mo||this.po)return[2];if(!(t=this._o.nextBundle({bypassMinBudget:!0})))return[2];i={bundle:t,deltaT:null,isNewSession:this.Io,lastUserActivity:0,orgId:this.Eo,pageId:this.To,serverBundleTime:this.wo,serverPageStart:this.Co,sessionId:this.xo,userId:this.Ro,version:this.N.recording.bundleApiVersion()},s.label=1;case 1:return s.trys.push([1,3,4,5]),this.po=!0,[4,qc(this.N.wdx,this.N.window,function(){return r(n,void 0,mr,function(){var t,n,r;return e(this,function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),[4,this.lo.event(i)];case 1:return t=e.sent(),n=t[0],r=t[1],n>=this.yo&&this.stopPipeline(),this.wo=r.BundleTime,[3,3];case 2:return On(e.sent())?[3,3]:[2,xc.Retry];case 3:return[2,xc.NoRetry]}})})},this["do"],function(t){return n.jo=t},this.ko)];case 2:return s.sent(),[3,5];case 3:return s.sent(),this.stopPipeline(),[3,5];case 4:return this.po=!1,this._o.size()>0&&this.Ao(),[7];case 5:return[2]}})})},t}(),Qc=function(){function t(t,i,n,r,e,s,u){void 0===r&&(r=function(){return[]}),void 0===e&&(e=Du),void 0===s&&(s=Wu);var o=this;this.N=t,this.Mo=i,this.Po=r,this.Oo=e,this.No=u,this.Lo=0,this.Uo=[],this.Fo=!1,this.Bo=!1,this.Ho=0,this.Do=-1,this.Wo=!1,this.Si=[],this.zo=[],this.qo=new Xc,this.Vo=new s(t.wdx,t.window,function(){window,o.processEvents(!0),window},ui.CurveSamplingInterval),this.$o=new this.Oo(t.wdx,t.window,ui.MutationProcessingInterval),n&&(this.Go=new Oc(this.N,n,this,s),this.zo.push(this.Go)),this.zo.push(this.qo),u&&this.zo.push(u)}return t.prototype.startPipeline=function(t){var i,n,s;return r(this,void 0,mr,function(){var r=this;return e(this,function(e){switch(e.label){case 0:return this.Bo||this.Fo?[2]:(this.Fo=!0,t.frameId&&(this.Lo=t.frameId),t.parentIds&&(this.Uo=t.parentIds),t.fixWhenValues&&0===(null!==(i=t.frameId)&&void 0!==i?i:0)&&this.qo.startPipeline(),null===(n=this.No)||void 0===n||n.startPipeline(t),[4,Cr(this.N.wdx,this.N.window)]);case 1:return e.sent(),this.processEvents(),[4,Cr(this.N.wdx,this.N.window)];case 2:return e.sent(),window,this.$o.start(function(){window,r.processEvents(),window}),null===(s=this.Go)||void 0===s||s.start(),this.Mo.startPipeline(t),window,[2]}})})},t.prototype.enableEasyBake=function(){this.Qo=new Kc(this.N)},t.prototype.enqueueSimultaneousEventsIn=function(t){if(0===this.Ho){var i=this.N.time.now();this.Do=i>this.Do?i:this.Do}try{return this.Ho++,t(this.Do)}finally{this.Ho--,this.Ho<0&&(this.Ho=0)}},t.prototype.enqueue=function(t){var i=this.Ho>0?this.Do:this.N.time.now();this.Xo(i,t),Hu.checkForBrokenSchedulers(this.N.wdx,this.N.window)},t.prototype.Xo=function(t,i){var n;if(!this.Bo){var r=t;r<this.Do&&(r=this.Do),this.Do=r;var e,s=i;s.When=r,this.Si.push(s);try{null===(n=this.Qo)||void 0===n||n.onEvent(s)}catch(t){Bs.send(this.N.wdx,"easyBake",{err:t,kind:s.Kind})}!this.Vo.isRunning()&&(e=s,this.N.recording.flags().EnableRecEvents||Ac(e))&&this.Vo.start(this.N.recording.flags().EnableRecEvents?this.N.wdx.mathMax(50,100):ui.CurveSamplingInterval)}},t.prototype.enqueueFirst=function(t){if(this.Si.length>0){var i=t;i.When=this.Si[0].When,this.Si.unshift(i)}else this.enqueue(t)},t.prototype.addUnload=function(t){this.Wo||(this.Wo=!0,this.enqueue({Kind:Y.UNLOAD,Args:[t]}),this.singSwanSong(t))},t.prototype.shutdown=function(t){this.addUnload(t),this.Yo(),this.Bo=!0,this.Jo()},t.prototype.Yo=function(){var t,i;this.processEvents(),null===(i=(t=this.Mo).send)||void 0===i||i.call(t)},t.prototype.singSwanSong=function(t){var i,n;this.Bo||(window,this.Yo(),t===bt.Hidden&&this.Wo||null===(n=(i=this.Mo).send)||void 0===n||n.call(i,{mode:"sing",reason:t}),window)},t.prototype.rebaseIframe=function(t,i){for(var n=Math.max(0,i),r=this.N.time.startTime(),e=function(i){var e=r+i-t;return e>=n?e:n},s=0,u=this.Si;s<u.length;s++){var o=u[s];o.When=e(o.When)}-1===this.Do?this.Do=n:this.Do=e(this.Do)},t.prototype.processEvents=function(t){if(this.Fo){var i=this.Si;this.Si=[];var n=function(t){if(0==t.length)return t;for(var i,n=[],r=new Tc(n,[0,1]),e={},s={},u={},o=0,h=t;o<h.length;o++){var a=h[o];if(Ac(a))switch(a.Kind){case Y.MOUSEMOVE:r.add(a);break;case Y.TOUCHMOVE:(v=a.Args[0])in e||(e[v]=new Tc(n,[1,2])),e[v].add(a);break;case Y.SCROLL_LAYOUT:(v=a.Args[0])in s||(s[v]=new Tc(n,[1,2])),s[v].add(a);break;case Y.SCROLL_VISUAL_OFFSET:(v=a.Args[0])in u||(u[v]=new Tc(n,[1,2])),u[v].add(a);break;case Y.RESIZE_VISUAL:i||(i=new Tc(n,[0,1])),i.add(a);break;default:jr()}else n.push(a)}if(r.finish(Y.MOUSEMOVE_CURVE)){var c=r.evts();if(c.length>0){var f=c[c.length-1].Args[2];f&&(c[0].Args[9]=f)}}for(var v in s)s[l=parseInt(v,10)].finish(Y.SCROLL_LAYOUT_CURVE,[l]);for(var v in u)u[l=parseInt(v,10)].finish(Y.SCROLL_VISUAL_OFFSET_CURVE,[l]);for(var v in e){var l;e[l=parseInt(v,10)].finish(Y.TOUCHMOVE_CURVE,[l])}return i&&i.finish(Y.RESIZE_VISUAL_CURVE),n}(i);t||(n=n.concat(this.Po())),this.Zo(n),this.sendEvents(this.N.recording.pageSignature(),n)}},t.prototype.registerInterceptor=function(t){this.zo.push(t)},t.prototype.sendEvents=function(t,i){if(0!=i.length){if(this.zo.length>0)for(var n=0,r=i;n<r.length;n++)for(var e=r[n],s=0,u=this.zo;s<u.length;s++)u[s].intercept(e);try{this.Mo.enqueueEvents(t,i)}catch(t){"transport.enqueueEvents: caught exception ".concat(t)}}},t.prototype.onHibernate=function(t){var i,n,r;t||this.Yo(),null===(n=(i=this.Mo).send)||void 0===n||n.call(i,{mode:"sing"}),this.Mo.stopPipeline(),null===(r=this.No)||void 0===r||r.stopPipeline()},t.prototype.Zo=function(t){if(this.Lo)for(var i=this.Uo,n=i&&i.length>0,r=0;r<t.length;++r){var e=t[r];e.FId||(e.FId=this.Lo),n&&!e.PIds&&(e.PIds=i)}},t.prototype.Jo=function(){var t,i;this.Fo&&(this.Vo.stop(),this.$o.stop(),this.Si=[],null===(t=this.Go)||void 0===t||t.shutdown(),this.Mo.stopPipeline(),this.qo.stopPipeline(),null===(i=this.No)||void 0===i||i.stopPipeline())},t.prototype.manualHibernateCheck=function(){var t;null===(t=this.Go)||void 0===t||t.manualHibernateCheck()},t}(),Xc=function(){function t(){this.di=!1,this.th={}}return t.prototype.startPipeline=function(){this.di=!0},t.prototype.stopPipeline=function(){this.di=!1,this.th={}},t.prototype.intercept=function(t){var i;if(this.di){var n=W(t.PIds,t.FId),r=null!==(i=this.th[n])&&void 0!==i?i:-1;t.When>r?this.th[n]=t.When:t.When<r&&(t.When=r)}},t}(),Yc=function(){function t(t){void 0===t&&(t=4),this.hashCount=t,this.idx=0,this.hashMask=t-1,this.reset()}return t.prototype.reset=function(){this.idx=0,this.hash=[];for(var t=0;t<this.hashCount;++t)this.hash.push(2166136261)},t.prototype.write=function(t){for(var i=this.hashMask,n=this.idx,r=0;r<t.length;r++)this.hash[n]=this.hash[n]^t[r],this.hash[n]+=(this.hash[n]<<1)+(this.hash[n]<<4)+(this.hash[n]<<7)+(this.hash[n]<<8)+(this.hash[n]<<24),n=n+1&i;this.idx=n},t.prototype.writeAscii=function(t){for(var i=this.hashMask,n=this.idx,r=0;r<t.length;r++)this.hash[n]=this.hash[n]^t.charCodeAt(r),this.hash[n]+=(this.hash[n]<<1)+(this.hash[n]<<4)+(this.hash[n]<<7)+(this.hash[n]<<8)+(this.hash[n]<<24),n=n+1&i;this.idx=n},t.prototype.sum=function(){var t;return t=this.sumAsHex().replace(/\r|\n/g,"").replace(/([\da-fA-F]{2}) ?/g,"0x$1 ").replace(/ +$/,"").split(" ").map(Number),nf(String.fromCharCode.apply(window,t))},t.prototype.sumAsHex=function(){for(var t="",i=0;i<this.hashCount;++i)t+="00000000".concat((this.hash[i]>>>0).toString(16)).slice(-8);return t},t}();function Jc(t){var i=new Yc(1);return i.writeAscii(t),i.sumAsHex()}function Zc(t){var i=new Uint8Array(t);return nf(String.fromCharCode.apply(null,i))}var tf="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function nf(t){var i;return(null!==(i=window.btoa)&&void 0!==i?i:rf)(t).replace(/\+/g,"-").replace(/\//g,"_")}function rf(t){for(var i=String(t),n=[],r=0,e=0,s=0,u=tf;i.charAt(0|s)||(u="=",s%1);n.push(u.charAt(63&r>>8-s%1*8))){if((e=i.charCodeAt(s+=3/4))>255)throw new Error("'btoa' failed: The string to be encoded contains characters outside of the Latin1 range.");r=r<<8|e}return n.join("")}var ef=1e4,sf=25,uf=100;function of(t,i,n,s){return void 0===s&&(s=new Yc),r(this,void 0,mr,function(){var r,u,o,h;return e(this,function(e){switch(e.label){case 0:r=t.now(),u=n.byteLength,o=0,e.label=1;case 1:return o<u?t.now()-r>sf?[4,i(uf)]:[3,3]:[3,5];case 2:e.sent(),r=t.now(),e.label=3;case 3:h=new Uint8Array(n,o,Math.min(u-o,ef)),s.write(h),e.label=4;case 4:return o+=ef,[3,1];case 5:return[2,{hash:s.sum(),hasher:s}]}})})}function hf(t,i,n){var s,u;return r(this,void 0,mr,function(){return e(this,function(r){switch(r.label){case 0:return(null===(u=null===(s=t.crypto)||void 0===s?void 0:s.subtle)||void 0===u?void 0:u.digest)?[4,t.crypto.subtle.digest({name:"sha-1"},n)]:[3,2];case 1:return[2,{hash:Zc(r.sent()),algorithm:"sha1"}];case 2:return[4,of(i,af,n)];case 3:return[2,{hash:r.sent().hash,algorithm:"fsnv"}]}})})}function af(t){return new mr(function(i){return setTimeout(function(){try{i()}catch(t){}},t)})}function cf(t){var i,n;return!!(null===(n=null===(i=t.Capacitor)||void 0===i?void 0:i.isNativePlatform)||void 0===n?void 0:n.call(i))}var ff=6e6,vf=/^\/?_capacitor_file_/,lf=function(){function t(t,i,n,r,e){void 0===r&&(r=window.FormData),void 0===e&&(e=Wu),this.N=t,this.Ft=i,this.lo=n,this.ih=r,this.nh=e,this.Oe={},this.rh={},this.eh=!1,this.sh=[]}return t.prototype.init=function(){var t=this;this.ih&&this.uh()["catch"](function(i){Bs.send(t.N.wdx,"resourceuploader#init",{err:i})})},t.prototype.uh=function(){return r(this,void 0,mr,function(){var t,i,n,r,s,u,o,h,a,c,f,v,l,d,p,w,g,m,y,b,S,k,_,A,I,E;return e(this,function(e){switch(e.label){case 0:t=this.N.options.orgId,e.label=1;case 1:return[4,this.oh()];case 2:for(i=e.sent(),n={fsnv:{},sha1:{}},r={},s=0,u=i;s<u.length;s++)b=u[s],n[b.hashAlgorithm][b.hash]=!0,(l=r[b.hash])?l.push(b):r[b.hash]=[b];o={fsnv:Object.keys(n.fsnv),sha1:Object.keys(n.sha1)},h=void 0,e.label=3;case 3:return e.trys.push([3,5,,6]),[4,this.lo.queryResources({OrgId:t,HashesByAlgorithm:o})];case 4:return h=e.sent(),[3,6];case 5:return a=e.sent(),"/rec/queryResources failed with status ".concat(a),[3,1];case 6:c=0,f=h,e.label=7;case 7:if(!(c<f.length))return[3,18];if(v=f[c],!(null==(l=r[v.QueryHash])?void 0:l.length))return qs(this.N.wdx,"No resource found for hash"),[3,17];if(d=v.Found&&v.CanonicalHash,!v.Found||!v.CanonicalHash)return[3,8];for(p=0,w=l;p<w.length;p++)(b=w[p]).blob=b.stringData=null,"fsnv"===(g=v.CanonicalHash.Algorithm)?this.Ft.enqueue({Kind:Y.SYS_RESOURCEHASH,Args:["url",b.url,v.CanonicalHash.Hash]}):qs(this.N.wdx,"Unrecognized hash",{hashAlgorithm:g,reportFound:d});return[3,17];case 8:m=0,y=l,e.label=9;case 9:if(!(m<y.length))return[3,17];if(b=y[m],S=b.url,k=b.contentType,(_={}).orgId=t,_.baseUrl=S,"fsnv"===v.QueryAlgorithm)_.fsnvHash=v.QueryHash;else{if("sha1"!==v.QueryAlgorithm)return qs(this.N.wdx,"Unrecognized hash",{hashAlgorithm:v.QueryAlgorithm,reportFound:d}),[3,16];_.sha1Hash=v.QueryHash}return b.blob?[4,kr(this.N.wdx,b.blob)]:[3,11];case 10:return(A=e.sent())?(_.contents={data:A,contentType:k,filename:"blob"},[3,12]):[3,17];case 11:b.stringData&&(_.contents={data:b.stringData,contentType:k,filename:"blob"}),e.label=12;case 12:if(!_.contents)return qs(this.N.wdx,"Missing resource contents"),[3,16];b.blob=b.stringData=null,e.label=13;case 13:return e.trys.push([13,15,,16]),[4,this.lo.uploadResource({type:"FormData",data:_})];case 14:return I=e.sent(),"fsnv"!=(E=JSON.parse(I)).Algorithm&&qs(this.N.wdx,"Unrecognized hash",{hashAlgorithm:E.Algorithm,from:"response"}),this.Ft.enqueue({Kind:Y.SYS_RESOURCEHASH,Args:["url",S,E.Hash]}),[3,16];case 15:return e.sent(),[3,16];case 16:return m++,[3,9];case 17:return c++,[3,7];case 18:return[3,1];case 19:return[2]}})})},t.prototype.oh=function(){var t=this,i=yr(),n=i.resolve,r=i.promise,e=function(){t.hh=null,t.eh=!1;var i=t.sh;t.sh=[],n(i)};return this.eh?e():this.hh=e,r},t.prototype.uploadIfNeeded=function(t,i,n){return void 0===n&&(n="unknown"),r(this,void 0,mr,function(){var r,s;return e(this,function(e){switch(e.label){case 0:return this.ih?(r=i.href,this.Oe[r]?[2]:"css"===n&&this.N.options.forceLocalResources||function(t,i){if(cf(t)){var n=function(t){var i,n=null===(i=t.Capacitor)||void 0===i?void 0:i.getServerUrl;return _(n)?qr(Vr(t),n()):void 0}(t);if(n)return"blob:"===i.protocol||n.protocol===i.protocol&&n.hostname===i.hostname&&vf.test(i.pathname)}switch(i.protocol){case"blob:":return!0;case"http:":case"https:":switch(i.hostname){case"localhost":case"127.0.0.1":case"[::1]":return t.location.protocol===i.protocol&&t.location.host===i.host;case"::1":var r=i.port?"[::1]:".concat(i.port):"[::1]";return t.location.protocol===i.protocol&&t.location.host===r;default:return!1}default:return!1}}(t,i)?(this.Oe[r]=!0,[4,this.ah(r)]):[2]):[2];case 1:return(s=e.sent())?(this.fh(s),[2]):[2]}})})},t.prototype.fh=function(t){var i=this,n=0==this.sh.length;this.sh.push(t),n&&new this.nh(this.N.wdx,this.N.window,function(){i.eh=!0,i.hh&&i.hh()},50).start()},t.prototype.ah=function(t){return r(this,void 0,mr,function(){var i,n,r,s,u;return e(this,function(e){switch(e.label){case 0:return this.rh[t]?[2,this.rh[t]]:[4,df(this.N.wdx,t)];case 1:return(i=e.sent())&&i.buffer.byteLength?[4,hf(this.N.window,this.N.time,i.buffer)]:[2,null];case 2:return n=e.sent(),r=n.hash,s=n.algorithm,u={hash:r,hashAlgorithm:s,url:t,blob:i.blob,contentType:i.contentType},this.rh[u.url]=u,[2,u]}})})},t.prototype.uploadDataUrlIfNeeded=function(t,i){return void 0===i&&(i=function(){return!0}),r(this,void 0,mr,function(){var n,r,s,u,o,h;return e(this,function(e){switch(e.label){case 0:return n=function(t,i){var n,r="Could not parse data url",e=i.indexOf(",");if(-1!==e)try{var s=i.substring(0,e).match(pf),u=void 0,o=void 0;s&&(u=s[1],o=null===(n=s[2])||void 0===n?void 0:n.substring(1));for(var h=atob(i.substring(e+1)),a=new ArrayBuffer(h.length),c=new Uint8Array(a),f=0;f<h.length;++f)c[f]=h.charCodeAt(f);return{blob:new Blob([a],{type:u}),buffer:a,contentType:u,charset:o}}catch(n){return void Bs.send(t,r,{input:i.substring(0,100),err:n},"warning")}else Bs.send(t,r,{input:i.substring(0,100)},"warning")}(this.N.wdx,t),n?[4,hf(this.N.window,this.N.time,n.buffer)]:[2,void 0];case 1:return r=e.sent(),s=r.hash,u=r.algorithm,o="https://".concat("data-url.fs.invalid","/").concat(u,"/").concat(s),h={hash:s,hashAlgorithm:u,url:o,blob:n.blob,contentType:n.blob.type||"application/octet-stream"},i(h)?(this.rh[h.url]=h,this.fh(h),[2,h]):[2,h]}})})},t}();function df(t,i){var n=yr(),r=n.resolve,e=n.promise,s=new XMLHttpRequest;return"string"!=typeof s.responseType?(r(null),e):(s.open("GET",i,!0),s.responseType="blob",s.onerror=function(){r(null)},s.onload=function(){if(200!=s.status&&0!==s.status)return"Error loading blob resource ".concat($e(t,i,{source:"log",type:"debug"})),void r(null);var n=s.response,e=n.size||n.length;if(e>ff){var u=$e(t,i,{source:"log",type:"fsbugs"});return qs(t,"Size of blob resource exceeds limit",{url:u,max:ff,blobSize:e}),void r(null)}kr(t,n).then(function(t){r(t?{buffer:t,blob:n,contentType:n.type}:null)})},s.send(),e)}var pf=/^data:([^;,]*)(;?charset=[^;]+)?(?:;base64)?$/i,wf=function(){function t(t,i,n){void 0===n&&(n=new gf(t)),this.N=t,this.de=i,this.lh=n}return t.prototype.initialize=function(t){var i;if(t){this.dh(t);var n=null===(i=this.N.window.location)||void 0===i?void 0:i.href;this.onNavigate(n)}},t.prototype.onNavigate=function(t){return!!this.lh.matches(t)&&(this.de.enqueue({Kind:Y.KEEP_URL,Args:[$e(this.N.wdx,t,{source:"page",type:"base"})]}),!0)},t.prototype.onClick=function(t){var i;return!!(null===(i=null==t?void 0:t.watchKind)||void 0===i?void 0:i.has(re.Keep))&&(this.de.enqueue({Kind:Y.KEEP_ELEMENT,Args:[t.id]}),!0)},t.prototype.urlMatches=function(t){return this.lh.matches(t)},t.prototype.dh=function(t){this.lh.setRules(t)},t}(),gf=function(){function t(t){this.N=t,this.ph=null}return t.prototype.setRules=function(t){var i=t.map(function(t){return t.Regex}).filter(mf.bind(null,this.N.wdx));i.length>0&&(this.ph=function(t,i){try{return new RegExp("(".concat(i.join(")|("),")"),"i")}catch(n){return Bs.send(t,"keep#joinRegexes",{exprs:i,err:n}),null}}(this.N.wdx,i))},t.prototype.matches=function(t){return!!this.ph&&this.ph.test(t)},t}();function mf(t,i){try{return new RegExp(i),!0}catch(n){return Bs.send(t,"keep#isValidRegex",{expr:i,err:n}),!1}}function yf(t,i,n){if(t&&"function"==typeof t[i]){n.get(t)||n.set(t,{});var r=t[i];t[i]=function(){var e=n.get(t);return e&&"function"==typeof e[i]&&e[i].apply(this,arguments),r.apply(this,arguments)}}}gi.RequestFrameId,gi.EvtBundle;var bf=function(t){var i=void 0===t?{}:t,n=i.wnd,r=void 0===n?window:n,e=i.injectedNamespace,s=void 0===e?r._fs_namespace:e;void 0===i.injectedScript&&r._fs_script,function(t,i,n,r,e,s,u,o){var h,a;function c(t){var i,n=[];function r(){i&&(n.forEach(function(t){var n;try{n=t[i[0]]&&t[i[0]](i[1])}catch(i){return void(t[3]&&t[3](i))}n&&n.then?n.then(t[2],t[3]):t[2]&&t[2](n)}),n.length=0)}function e(t){return function(n){i||(i=[t,n],r())}}return t(e(0),e(1)),{then:function(t,i){return c(function(e,s){n.push([t,i,e,s]),r()})}}}n in t&&(t.console&&t.console.log&&t.console.log("FullStory namespace conflict. Please set window[\"_fs_namespace\"]."),1)||(u=t[n]=function(){var t=function(t,n,r,e){function s(s,u){i(t,n,r,s,u,e)}e=e||2;var u,o=/Async$/;return o.test(t)?(t=t.replace(o,""),"function"==typeof Promise?new Promise(s):c(s)):i(t,n,r,u,u,e)};function i(i,n,r,e,s,u){return t._api?t._api(i,n,r,e,s,u):(t.q&&t.q.push([i,n,r,e,s,u]),null)}return t.q=[],t}(),function(){function t(){}function i(t,i,n){u(t,i,n,1)}function n(t,n,r){i("setProperties",{type:t,properties:n},r)}function r(t,i){n("user",t,i)}function e(t,i,n){r({uid:t},n),i&&r(i,n)}u.identify=e,u.setUserVars=r,u.identifyAccount=t,u.clearUserCookie=t,u.setVars=n,u.event=function(t,n,r){i("trackEvent",{name:t,properties:n},r)},u.anonymize=function(){e(!1)},u.shutdown=function(){i("shutdown")},u.restart=function(){i("restart")},u.log=function(t,n){i("log",{level:t,msg:n})},u.consent=function(t){i("setIdentity",{consent:!arguments.length||t})}}(),h="fetch",a="XMLHttpRequest",u._w={},u._w[a]=t[a],u._w[h]=t[h],t[h]&&(t[h]=function(){return u._w[h].apply(this,arguments)}),u._v="2.0.0")}(r,r.document,s)};"".concat(yf,"\n(").concat(function(t,i){var n=t._fs_prehooks||new WeakMap;t._fs_prehooks=n,i.forEach(function(t){var i=t[0];t[1].forEach(function(t){yf(i,t,n)})})},")(win, [[\n  CSSStyleSheet.prototype,\n  ['insertRule', 'removeRule']\n]]);");var Sf=/[^a-zA-Z0-9.\-_]/g;function kf(t,i){return"".concat(t,"_").concat(i)}function _f(t,i,n,r){var e=function(t,i){return i?function(t,i){var n=i.replace(Sf,"");return 0===n.length?t:kf(t,n)}(t,n):t};return[e(t,r),e(i,r),e(t,!r),e(i,!r)]}function Af(t,i,n,r,e,s){var u=i.getValue(n,r),o=u.cookieValue,h=u.localStorageValue;if(!t&&!o&&!h){var a=i.getValue(e,s);o=a.cookieValue,h=a.localStorageValue}return[o,h]}function If(t){return t.options.useNamespace?kf(bi,t.options.namespace):bi}function Ef(t,i,n){if(i&&i.postMessage)try{i.postMessage(function(t,i){var n;return ds(t.wdx,((n={})[If(t)]=i,n))}(t,n),"*")}catch(i){qs(t.wdx,"postMessageTo",{err:i})}}function Tf(t,i){try{var n=U(t.wdx,i),r=If(t);if(r in n)return n[r]}catch(t){}return[gi.Unknown]}function Cf(t,i,n,r){var e=t.options.transport;if(!e)return!1;try{e.send(i,n,r)}catch(t){e.send(i,n)}return!0}function xf(t,i,n){var r=t.options.transport;if(!r||!r.sendToChild)return!1;var e=n[0],s=n.slice(1),u=ds(t.wdx,s);return r.sendToChild(i,e,u),!0}var Rf=new RegExp(/^\s+$/),Kf=/^fb\d{18}$/,jf=function(t,i){var n=i.appHost,r=i.desc,e=i.frame,s=i.namespace,u=i.orgId,o=i.recHost,h=i.recSettingsHost,a=i.scheme,f=i.script,v=i.snippetVersion,l=i.underTest,d=i.useNamespace;"Injecting into Frame ".concat(r);try{if(function(t){return t.id==t.name&&Kf.test(t.id)}(e))return"Blocklisted iframe: ".concat(r),ot.BlocklistedFrame;if(function(t){return!(t.contentDocument&&t.contentWindow&&t.contentWindow.location)||function(t){return!!t.src&&"about:blank"!=t.src&&t.src.indexOf("javascript:")<0}(t)&&t.src!=t.contentWindow.location.href&&"loading"==t.contentDocument.readyState}(e))return"Frame not yet loaded: ".concat(r),ot.PartiallyLoaded;var p=e.contentWindow,g=e.contentDocument;if(!p||!g)return"Missing contentWindow or contentDocument: ".concat(r),ot.MissingWindowOrDocument;if(!g.head)return"Missing contentDocument.head: ".concat(r),ot.MissingDocumentHead;if(!g.body||!b(0,g.body))return ot.MissingBodyOrChildren;for(var m=!1,S=y(0,g.body);S;S=w(0,S)){switch(S.nodeType){case c:if("SCRIPT"===S.tagName)continue;break;case 3:var k=S.textContent;if(null===k||Rf.test(k))continue;break;case 8:continue}m=!0;break}if(!m)return ot.NoNonScriptElement;if(Ui(p,s))return"FS already defined in Frame contentWindow: ".concat(r," Ignoring."),ot.AlreadyDefined;l&&(p._fs_org=u);var _=function(t,i,n){return{send:function(r,e,s){if(void 0!==i.parent){var u=Ui(i.parent,n);void 0!==u&&"function"==typeof u._withRecorder&&u._withRecorder(s,function(n){try{n.onMessageReceived(i,[r,t.jsonParse(e),s])}catch(i){i instanceof SyntaxError&&Bs.send(t,"rec#onMessageReceived",{err:i,msg:r,signature:s})}})}}}}(t,p,s);return function(t,i,n,r){/^2\./.test(r.snippetVersion)&&r.script?bf({wnd:i,injectedNamespace:r.namespace,injectedScript:r.script}):function(t){var i,n,r,e,s,u=void 0===t?{}:t,o=u.wnd,h=void 0===o?window:o,a=u.injectedNamespace,c=void 0===a?h._fs_namespace:a;i=h,h.document,r="user",(n=c)in i?i.console&&i.console.log&&i.console.log("FullStory namespace conflict. Please set window[\"_fs_namespace\"]."):((e=i[n]=function(t,i,n){e.q?e.q.push([t,i,n]):e._api(t,i,n)}).q=[],e.identify=function(t,i,n){e(r,{uid:t},n),i&&e(r,i,n)},e.setUserVars=function(t,i){e(r,t,i)},e.event=function(t,i,n){e("event",{n:t,p:i},n)},e.anonymize=function(){e.identify(!1)},e.shutdown=function(){e("rec",!1)},e.restart=function(){e("rec",!0)},e.log=function(t,i){e("log",[t,i])},e.consent=function(t){e("consent",!arguments.length||t)},e.identifyAccount=function(t,i){(i=i||{}).acctId=t,e("account",i)},e.clearUserCookie=function(){},e.setVars=function(t,i){e("setVars",[t,i])},e._w={},s="XMLHttpRequest",e._w[s]=i[s],s="fetch",e._w[s]=i[s],i[s]&&(i[s]=function(){return e._w[s].apply(this,arguments)}),e._v="1.3.0")}({wnd:i,injectedNamespace:r.namespace});var e=function(t,i){return void 0===i&&(i=Ni(t)),t[i]}(i,r.namespace);Ws(t,!!e,"snippet api"),null==e||e("init",{env:{appHost:r.appHost,orgId:r.orgId,recHost:r.recHost,recSettingsHost:r.recSettingsHost,runInIframe:r.runInIframe,scheme:r.scheme,script:r.script,transport:r.transport,useNamespace:r.useNamespace}});var s=n.createElement("script");s.setAttribute(Si,r.namespace),s.async=!0,s.crossOrigin="anonymous",s.src="".concat(r.scheme,"//").concat(r.script),"testdrive"===r.orgId&&(s.src+="?allowMoo=true"),n.head.appendChild(s)}(t,p,g,{appHost:n,namespace:s,orgId:u,recHost:o,recSettingsHost:h,runInIframe:!0,scheme:a,script:f,snippetVersion:v,transport:_,useNamespace:d}),ot.Successful}catch(t){return ot.Exception}};function Mf(t,i){var n="".concat(Ke(i));i.id&&(n+="#".concat(i.id));var r=$e(t,i.src,{source:"log",type:"debug"});return n+"[src=".concat(r,"]")}var Pf,Of,Nf=function(){function t(t,i){var n;this.N=t,this.Ft=[],this.wh=!1,this.xt=null!==(n=i.interval)&&void 0!==n?n:1e3,this.gh=i.onFlush}return t.prototype.append=function(t){this.schedule(),this.Ft.push(t)},t.prototype.flush=function(){this.wh=!1,this.gh(this.Ft),this.Ft=[]},t.prototype.schedule=function(){this.wh||(this.N.wdx.setWindowTimeout(this.N.window,h(this.flush.bind(this)),this.xt),this.wh=!0)},t}(),Lf="https://fs-obfuscated.invalid",Uf=function(){function t(t,i){this.N=t,this.Ft=i,this.mh=0,this.yh={},this.di=!1}return t.prototype.enable=function(){var t=this;this.di=!0,this.bh=function(t,i){var n;try{if("function"==typeof(null===(n=i.crypto)||void 0===n?void 0:n.getRandomValues)){var r="",e=new Uint32Array(2);i.crypto.getRandomValues(e);for(var s=0;s<e.length;s++)r+=e[s].toString(16);return r}}catch(t){}return t.mathFloor(1e20*(t.mathRandom()+.1)).toString(16)}(this.N.wdx,this.N.window),this.Ft.enqueue({Kind:Y.URL_SALT,Args:[this.bh]}),this.Sh=new Nf(this.N,{interval:500,onFlush:function(i){return t.gh(i)}})},t.prototype.flush=function(){var t;null===(t=this.Sh)||void 0===t||t.flush()},t.prototype.gh=function(t){0!==t.length&&this.Ft.enqueue({Kind:Y.URL_ID,Args:t})},t.prototype.record=function(t){var i;if(this.di&&/^(file|http)/i.test(t)){var n=this.kh(t,!0),r=n[0];n[1]&&(null===(i=this.Sh)||void 0===i||i.append([t,r]))}},t.prototype._h=function(t){return this.kh(t,!1)[0]},t.prototype.kh=function(t,i){var n=t.substring(0,5e3);if(void 0===this.yh[n]){var r=this.mh++;return this.yh[n]={id:r,record:i},[r,i]}var e=!1;return i&&(e=!1===this.yh[n].record,this.yh[n].record=!0),[this.yh[n].id,e]},t.prototype.obfuscateUrl=function(t,i){return this.di?"css"===i?this.Ah(t):this.Ih(t):t},t.prototype.Ah=function(t){Ws(this.N.wdx,void 0!==this.bh,"_hashObfuscatedUrl#salt");var i=Jc(t.substring(0,5e3)+this.bh);return"".concat(Lf,"?hash=").concat(i,"&algorithm=fnv32")},t.prototype.Ih=function(t){var i=this._h(t);return"".concat(Lf,"?url-id=").concat(i)},t}(),Ff=function(){function t(t){this.N=t,this.Eh=!!this.N.options.isWayfinder}return t.prototype.page=function(t){return r(this,void 0,mr,function(){return e(this,function(t){switch(t.label){case 0:return[4,Cr(this.N.wdx,this.N.window)];case 1:return t.sent(),[2,n(n({},Wf(this.Eh)),{UserIntId:"0",SessionIntId:"0",PageIntId:"0",EmbedToken:"",PageStart:Q(this.N.wdx),IsNewSession:!0,Flags:{AjaxWatcher:!0,ConsoleWatcher:!0,DisableImgUrlPrivacy:!0,GetCurrentSession:!0,UseClientSideId:!0}})]}})})},t.prototype.settings=function(t){return r(this,void 0,mr,function(){return e(this,function(t){switch(t.label){case 0:return[4,Cr(this.N.wdx,this.N.window)];case 1:return t.sent(),[2,Wf(this.Eh)]}})})},t.prototype.event=function(t){return this.bundle(t)},t.prototype.bundle=function(t){var i;return r(this,void 0,mr,function(){var n,r,s,u,o,h;return e(this,function(e){switch(e.label){case 0:n=Q(this.N.wdx),r=0,e.label=1;case 1:return e.trys.push([1,8,,9]),[4,Cr(this.N.wdx,this.N.window)];case 2:return e.sent(),(s=t.bundle)[0],u=s[1],o=s[2],r=o,"string"===u.type?[3,3]:[3,5];case 3:return[4,Er(u.data)];case 4:return h=e.sent()[0],r=null!==(i=null==h?void 0:h.byteLength)&&void 0!==i?i:0,[3,5];case 5:return r>2e6?[4,Cr(this.N.wdx,this.N.window)]:[3,7];case 6:e.sent(),e.label=7;case 7:return[3,9];case 8:return e.sent(),[3,9];case 9:return[2,[r,{BundleTime:n}]]}})})},t.prototype.bundleBeacon=function(t){return!0},t.prototype.startBeacon=function(t){return r(this,void 0,mr,function(){return e(this,function(t){return[2,mr.resolve()]})})},t}(),Bf=function(){function t(t){this.N=t}return t.prototype.uploadResource=function(t){return r(this,void 0,mr,function(){var t;return e(this,function(i){switch(i.label){case 0:return[4,Cr(this.N.wdx,this.N.window)];case 1:return i.sent(),t={Algorithm:"fsnv",Hash:""},[2,ds(this.N.wdx,t)]}})})},t.prototype.queryResources=function(t){return r(this,void 0,mr,function(){return e(this,function(t){switch(t.label){case 0:return[4,Cr(this.N.wdx,this.N.window)];case 1:return t.sent(),[2,[]]}})})},t}(),Hf=function(){function t(){this._cookies={}}return t.prototype.setDomain=function(t){},t.prototype.getValue=function(t,i){return{cookieValue:this._cookies[t],localStorageValue:void 0}},t.prototype.setValue=function(t,i,n,r){this.setCookie(t,i,n)},t.prototype.setCookie=function(t,i,n){this._cookies[t]=i},t.prototype.clearCookie=function(t,i){delete this._cookies[t]},Object.defineProperty(t.prototype,"cookies",{get:function(){return this._cookies},enumerable:!1,configurable:!0}),t}();function Df(){try{return document.domain}catch(t){}return""}function Wf(t){return{AjaxWatches:[],CookieDomain:Df(),ElementBlocks:t?[]:s(s([],[{Selector:"input",Consent:!1,Type:Jt.Mask},{Selector:"textarea",Consent:!1,Type:Jt.Mask},{Selector:"select",Consent:!1,Type:Jt.Mask},{Selector:"[contenteditable]",Consent:!1,Type:Jt.Mask},{Selector:"input[type=radio]",Consent:!1,Type:Jt.Exclude},{Selector:"input[type=checkbox]",Consent:!1,Type:Jt.Exclude}],!0),Mo,!0),ElementDeferreds:[],ElementKeeps:[],ElementWatches:[],OrgSettings:ui.DefaultOrgSettings,UrlKeeps:[],DwellTime:0}}(Of=Pf||(Pf={}))[Of.NoInfoYet=1]="NoInfoYet",Of[Of.Enabled=2]="Enabled",Of[Of.Disabled=3]="Disabled";var zf,qf,Vf=function(){function t(t,i,n,r,e,s){var u,o=this;this.N=t,this.Th=e,this.Ch=ui.DefaultOrgSettings,this.xh=!1,this.Lo=null,this.Uo=[],this.Rh=ui.DefaultBundleUploadInterval,this.Kh=ui.HeartbeatInterval,this.jh=[],this.Mh=new wu,this.Ph=[],this.et=new Iu(this.N),this.Oh=Pf.NoInfoYet,this.Nh=!1,this.Lh=!1,this.Uh=!1,this.Fh=!1,this.Bh={},this.Ft=new Qc(t,r,n,function(){return o.Hh.bundleEvents()},i,void 0,s);var h,a=new lf(t,this.Ft,(h=t).options.useMockProtocol?new Bf(h):new Ss(h));this.Qn=new Uf(t,this.Ft),this.Ui=new $o(t.wdx),this.bs=new wf(t,this.Ft),this.Hh=new yc(t,this.Ft,this.bs,this.Ui,this.et.createChild(),function(t){return o.xs(t)},function(t){return o.Rs(t)},a,this.Qn),this.P=t.options.scheme,this.Dh=t.options.script,this.Wh=t.options.recHost,this.zh=t.options.recSettingsHost,this.qh=t.options.appHost,this.Eo=t.options.orgId,this.Lh=null!==(u=t.options.skipIframeInjection)&&void 0!==u&&u,this.vt=t.window,this.Xt=this.vt.document}return t.prototype.getPageResponse=function(){return this.Vh},t.prototype.bundleUploadInterval=function(){return this.Rh},t.prototype.heartbeatInterval=function(){return this.Kh},t.prototype.setInitConfig=function(t){this.$h=t},t.prototype.start=function(t,i,n){var r=this;this.Gh=i,this.Qh=n,this.zs();var e=this.vt.Document?this.vt.Document.prototype:this.vt.document;this.Xh=Su(this.N,e,"close"),this.Xh&&this.Xh.afterAsync(function(){r.et.refresh()})},t.prototype.zs=function(){var t=this;"onpageshow"in this.vt&&this.et.add(this.vt,"pageshow",!1,function(i){t.Ft.manualHibernateCheck(),(null==i?void 0:i.persisted)&&t.Ft.enqueue({Kind:Y.BFCACHE_STATE,Args:[vt.Restored]})}),"onpagehide"in this.vt?this.et.add(this.vt,"pagehide",!1,function(i){(null==i?void 0:i.persisted)?(t.Ft.enqueue({Kind:Y.BFCACHE_STATE,Args:[vt.Entering]}),t.Ft.singSwanSong(bt.Unload)):t.Yh()}):this.et.add(this.vt,"unload",!1,function(){t.Yh()}),this.et.add(this.vt,"message",!1,function(i){var n=i.data;if("string"==typeof n){var r=i.source;t.onMessageReceived(r,Tf(t.N,n))}})},t.prototype.tellAllFramesTo=function(t){for(var i=0,n=this.jh;i<n.length;i++){var r=n[i];r.contentWindow&&Ef(this.N,r.contentWindow,t)}},t.prototype.queue=function(){return this.Ft},t.prototype.eventWatcher=function(){return this.Hh},t.prototype.console=function(){return this.Hh.consoleWatcher()},t.prototype.orgSettings=function(){return this.Ch},t.prototype.onDomLoad=function(){this.Hh.onDomLoad()},t.prototype.onLoad=function(){this.Hh.onLoad()},t.prototype.shutdown=function(t){var i;this.Fh=!0,this.tellAllFramesTo([gi.ShutdownFrame]),this.jh=[],this.Hh.stop(t),null===(i=this.et)||void 0===i||i.clearAll(),this.Mh=new wu,this.Xh&&this.Xh.disable()},t.prototype.getCurrentSessionURL=function(t){var i=this.Oh;if(i==Pf.NoInfoYet)return null;if(i==Pf.Disabled)return"".concat(this.P,"//").concat(this.qh,"/opt/upgrade");var n=this.getCurrentSession();return n?(t&&(n+=":".concat(this.N.time.wallTime())),"".concat(this.P,"//").concat(this.qh,"/ui/").concat(this.N.options.orgId,"/").concat(this.xh?"client-":"","session/").concat(encodeURIComponent(n))):null},t.prototype.getCurrentSession=function(){return this.getIsSessionReady()?"".concat(this.Ro,":").concat(this.xo):null},t.prototype.getPageArgs=function(){return this.Ro&&this.xo&&this.To?{userId:this.Ro,sessionId:this.xo,pageId:this.To,orgId:this.Eo}:null},t.prototype.getIsSessionReady=function(){var t=this.Oh;return!(t==Pf.NoInfoYet||t==Pf.Disabled||!this.Ro||!this.xo)},t.prototype.setConsent=function(t){var i,n=this;null===(i=this.N.recording.identity)||void 0===i||i.getConsentStore().setConsentState(t);var r=function(){n.Ui.setConsent(t),n.Ft.processEvents()},e=function(){n.Ft.enqueue({Kind:Y.SYS_SETCONSENT,Args:[t,mt.Document]})};switch(t){case wt.GrantConsent:e(),r();break;case wt.RevokeConsent:r(),e()}this.tellAllFramesTo([gi.SetConsent,t])},t.prototype.pageSignature=function(){return"".concat(this.Ro,":").concat(this.xo,":").concat(this.To)},t.prototype.getBundleApiVersion=function(){return this.xh?"v2":"v1"},t.prototype.Jh=function(t){void 0===t&&(t=!1);var i=this.N.options.ready;if(i)try{t?i(!0):i()}catch(t){"exception in _fs_ready(): ".concat(t)}},t.prototype.Yh=function(){this.Ft.addUnload(bt.Unload),Hu.stopAll(this.N.wdx)},t.prototype.Zh=function(t,i){var n,r,e,s,u=t.Flags,o=u.AjaxWatcher,h=u.ClientSideRageClick,a=u.DisableImgUrlPrivacy,c=u.GetCurrentSession,f=u.ResourceUploading,v=u.UseClientSideId;this.Vh=t,this.Ro=t.UserIntId,this.xo=t.SessionIntId,this.To=t.PageIntId,this.Co=t.PageStart,this.Oh=c?Pf.Enabled:Pf.Disabled,this.Ch=t.OrgSettings,Je(null!==(n=this.Ch.UrlPrivacyConfig)&&void 0!==n?n:ui.DefaultOrgSettings.UrlPrivacyConfig,this.Ch.MaxUrlLength);var l=null!==(r=this.Ch.AttributeBlocklist)&&void 0!==r?r:[];(null===(s=null===(e=this.$h)||void 0===e?void 0:e.privacy)||void 0===s?void 0:s.attributeBlocklist)&&("adding ".concat(this.$h.privacy.attributeBlocklist.length," client defined attribute block rules."),l.push.apply(l,this.$h.privacy.attributeBlocklist.map(fs))),hs(this.N.wdx,l),a||this.Qn.enable(),this.Hh.consoleWatcher().initializeMaxLogsPerPage(this.Ch.MaxConsoleLogPerPage),this.Hh.ajaxWatcher().initialize({requests:this.Ch.HttpRequestHeadersAllowlist,responses:this.Ch.HttpResponseHeadersAllowlist,maxAjaxPayloadLength:this.Ch.MaxAjaxPayloadLength}),this.Hh.perfWatcher().initialize({resourceUploader:this.Hh.getResourceUploader(),recTimings:!!this.Ch.RecordPerformanceResourceTiming,recImgs:!!this.Ch.RecordPerformanceResourceImg,maxPerfMarksPerPage:this.Ch.MaxPerfMarksPerPage}),this.Ui.initialize({canvasWatcherMode:t.Flags.CanvasWatcherMode,blocks:t.ElementBlocks,deferreds:t.ElementDeferreds,keeps:t.ElementKeeps,watches:t.ElementWatches,noDefaultExclusions:this.N.options.isWayfinder}),this.bs.initialize(t.UrlKeeps),this.Ui.initializeConsent(i),"number"==typeof t.BundleUploadInterval&&(this.Rh=t.BundleUploadInterval),"number"==typeof t.HeartbeatInterval&&(this.Kh=t.HeartbeatInterval),f&&this.ta(),o&&t.AjaxWatches&&this.Hh.ajaxWatcher().setWatches(t.AjaxWatches),h&&this.Ft.enableEasyBake(),v&&(this.xh=!0),this.Hh.start(t.Flags)},t.prototype.ia=function(){var t;this.Gh&&this.Gh({sessionUrl:null!==(t=this.getCurrentSessionURL())&&void 0!==t?t:"",settings:this.Ch})},t.prototype.na=function(){this.Qh&&this.Qh()},t.prototype.ta=function(){this.Nh=!0,this.Hh.initResourceUploading()},t.prototype.ra=function(){if(this.Ph.length>0){for(var t=0;t<this.Ph.length;t++)this.Ph[t]();this.Ph=[]}},t.prototype.ea=function(t){var i=this;this.N.measurer.enqueue(function(){var n=Mf(i.N.wdx,t),r=i.Th(i.N.wdx,{appHost:i.qh,desc:n,frame:t,namespace:i.N.options.namespace,orgId:i.Eo,recHost:i.Wh,recSettingsHost:i.zh,scheme:i.P,script:i.Dh,snippetVersion:i.N.options.snippetVersion,underTest:!!i.N.recording.flags().DisableInertBundles,useNamespace:i.N.options.useNamespace});r!==ot.MissingDocumentHead&&r!==ot.MissingBodyOrChildren&&r!==ot.NoNonScriptElement||!t.contentDocument||new MutationObserver(function(n,r){i.ea(t),r.disconnect()}).observe(t.contentDocument,{childList:!0,subtree:!0}),i.Ft.enqueue({Kind:Y.FRAME_STATUS,Args:[Ke(t),n,r]})})},t.prototype.sa=function(){var t,i,n,r,e,s=this;if(this.$h){var u=null!==(n=null===(i=null===(t=this.$h)||void 0===t?void 0:t.privacy)||void 0===i?void 0:i.attributeBlocklist)&&void 0!==n?n:[],o=null!==(e=null===(r=this.$h)||void 0===r?void 0:r.env)&&void 0!==e?e:{};this.Ft.enqueue({Kind:Y.INIT_API,Args:["privacy",["attributeBlocklist",u.map(function(t){return[t.target,t.tag,t.name,t.action,t.type]})],"env",Object.keys(o).map(function(t){var i=o[t];return[t,x(i)?i:Uu(s.N.wdx)(i)]})]})}},t.prototype.xs=function(t){var i=Ke(t);if(i){this.jh.push(t);var n=!1;if(t.contentWindow)try{n=!!Ui(t.contentWindow,this.N.options.namespace)}catch(t){n=!0}var r=function(t){var i=t.src,n="".concat(location.protocol,"//").concat(location.host);return!i||"about:blank"==i||R(i,"javascript:")||R(i,n)}(t),e=t.contentWindow&&t.contentWindow.postMessage;r&&!n||!e?r?this.ua(t):"Frame Doesn't need injecting. Probably cross domain ".concat(Mf(this.N.wdx,t)):this.oa(t,i)}else"fsid missing or invalid for iFrame ".concat(Mf(this.N.wdx,t))},t.prototype.oa=function(t,i){var n=[gi.GreetFrame,i];t.contentWindow&&t.contentWindow.postMessage?("Cross-origin iframe ".concat(Mf(this.N.wdx,t)),xf(this.N,t,n)||Ef(this.N,t.contentWindow,n)):"No content window on init of cross-origin iframe ".concat(Mf(this.N.wdx,t))},t.prototype.ua=function(t){var i=this;if(this.Lh)"skipped same-origin iframe injection for ".concat(Mf(this.N.wdx,t)," because _fs_skip_iframe_injection is set to true");else{"Attempting to setup Frame ".concat(Mf(this.N.wdx,t)),this.ea(t);var n=this.et.createChild();n.add(t,"load",!1,Bs.wrap(this.N.wdx,function(){"onload for frame ".concat(Mf(i.N.wdx,t)),i.ea(t)},"iframe#loadListener")),this.Mh.set(t,n)}},t.prototype.Rs=function(t){if(Ke(t)){var i=this.Mh.get(t);i&&(this.et.clearChild(i),this.Mh["delete"](t)),this.jh=this.jh.filter(function(i){return i!==t})}},t.prototype.onMessageReceived=function(t,i){if(!t||t.parent==this.vt)switch(i[0]){case gi.EvtBundle:var n=i[1],r=i[2],e=this.pageSignature();if(e!==r)return Uu(this.N.wdx)({msg:"Page signature mismatch",pageSignature:e,messageSignature:r},1024),void(t&&Ef(this.N,t,[gi.ShutdownFrame]));n.length>0&&this.Ft.sendEvents(e,n);break;case gi.RequestFrameId:if(!t)return;var s=this.ha(t);if(void 0===s);else{var u=Ke(s);"Responding to FID request for frame ".concat(u),this.Bh[u]=!1,this.aa(s,u)}case gi.Unknown:}},t.prototype.onNavigate=function(){this.eventWatcher().onNavigate()},t.prototype.ha=function(t){for(var i=0,n=this.jh;i<n.length;i++){var r=n[i];if(r.contentWindow===t)return r}},t.prototype.aa=function(t,i){var n=this,r=function(){var r,e=[];0!=n.Lo&&null!==n.Lo&&(e=n.Uo?n.Uo.concat(n.Lo):[n.Lo]);var s=n.N.time.startTime(),u=[gi.SetFrameId,i,e,s,n.P,n.Dh,n.qh,n.Eo,n.$h,n.Vh,null!==(r=n.Ui.getConsent())&&void 0!==r?r:wt.RevokeConsent,n.N.time.now()];xf(n.N,t,u)||Ef(n.N,t.contentWindow,u)};null==this.Lo?this.Ph.push(r):r()},t.prototype.ca=function(t){var i,n,r=this;this.Uh||((null===(n=null===(i=this.Vh)||void 0===i?void 0:i.Flags)||void 0===n?void 0:n.FetchIntegrations)?$i(function(){var i;"string"!=typeof Gi(i=r.N.window,"_fs_rec_settings_host")&&(i._fs_rec_settings_host=Di(Rn(i)));var n=r.Xt.createElement("script");t&&(n.addEventListener("load",t),n.addEventListener("error",t)),n.async=!0,n.src="".concat(r.P,"//").concat(r.Wh,"/rec/integrations?OrgId=").concat(r.Eo,"&isInFrame=").concat(r.N.recording.inFrame,"&isNative=").concat(r.N.recording.inWebView),r.Xt.head.appendChild(n),r.Uh=!0}):t&&t())},t.prototype.fa=function(t){var i=this;this.N.measurer.enqueue(function(){i.Ft.enqueue({Kind:Y.DOCUMENT_PROPERTIES,Args:[Ke(t.scrollingElement),t.compatMode]})})},t}();function $f(t,i){var n,r,e,s,u,o,h,a,c=(null!==(r=null===(n=i.ElementBlocks)||void 0===n?void 0:n.length)&&void 0!==r?r:0)>0&&(null!==(u=null===(s=null===(e=i.OrgSettings)||void 0===e?void 0:e.UrlPrivacyConfig)||void 0===s?void 0:s.length)&&void 0!==u?u:0)>0&&(null!==(a=null===(h=null===(o=i.OrgSettings)||void 0===o?void 0:o.AttributeBlocklist)||void 0===h?void 0:h.length)&&void 0!==a?a:0)>0;return c||Bs.send(t,"Invalid page response",{rsp:i}),c}(qf=zf||(zf={})).START="start",qf.SHUTDOWN="shutdown",qf.INTERNAL_BUNDLE="internal/bundle",qf.INTERNAL_ERROR="internal/error",qf.INTERNAL_FS_INIT="internal/fs-init";var Gf=[zf.START,zf.SHUTDOWN,zf.INTERNAL_BUNDLE,zf.INTERNAL_ERROR,zf.INTERNAL_FS_INIT];function Qf(t,i){var n=i.Seq,r=Uu(t)(i);return[n,{data:r,type:"string"},r.length]}function Xf(t,i,n,r){var e;return new mr(function(s){var u=_h(Rh,function(o){for(var h=0,a=o;h<a.length;h++)a[h].entryType===Rh&&(e?(e.stop(),e.start()):(e=new n(t,i,function(){null==u||u.disconnect(),s()},1e3).start(),null==r||r(e)))});!function(t,i,n,r,e,o){var h=new e(t,i,function(){null==u||u.disconnect(),s()},4e3).start();null==o||o(h)}(t,i,0,0,n,r)})}var Yf=function(){function t(t,i,n){this.N=t,this.va=i,this.la=n,this.da={stored:0,inStorage:0,currentSize:0,totalSize:0,key:i,nextSeq:1}}return t.prototype.store=function(t,i){return this.pa(t,i)},t.prototype.retrieve=function(t){var i=void 0===t?{}:t,n=i.validate,r=i.keyMeta,e=function(t,i,n){var r=[];try{for(var e=Zf(i,n)[0],s=0,u=e;s<u.length;s++){var o=u[s],h=localStorage.getItem(o);if(h){localStorage.removeItem(o);var a=Jf(t,h);a&&r.push(a)}}}catch(t){}return r}(this.N.wdx,this.va,r);return n?e.filter(n):e},t.prototype.pa=function(t,i){try{var r=function(t,i){var n=Zf(t,i),r=n[0],e=n[1],s=r.length?iv(r[r.length-1])+1:1;return[s,nv(t,s,i),r.length,e]}(this.va,i),e=r[0],s=r[1],u=r[2],o=r[3],h=ds(this.N.wdx,t),a=h.length;return!this.wa(a+o)&&(this.da.stored++,this.da.inStorage=u+1,this.da.nextSeq=e,this.da.currentSize=a,this.da.totalSize+=a,localStorage.setItem(s,h),!0)}catch(t){return function(t,i){return!!function(t){return t instanceof DOMException&&(22===t.code||1014===t.code||"QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)}(t)&&i.inStorage>2}(t,this.da)&&qs(this.N.wdx,"Quota Exceeded",n(n({},this.da),{message:ec(t),err:t})),!1}},t.prototype.wa=function(t){var i;return(null!==(i=this.la)&&void 0!==i?i:t)<t},t}();function Jf(t,i){try{return U(t,i)}catch(t){return}}function Zf(t,i){var n,r=[],e=0,s="^".concat(t,":\\d+$");i&&(s="^".concat(t,":").concat(i,":\\d+$"));var u=new RegExp(s);try{for(var o=0;o<localStorage.length;o++){var h=null!==(n=localStorage.key(o))&&void 0!==n?n:"";u.test(h)&&(r.push(h),e+=tv(h))}}catch(t){}return[r.sort(function(t,i){return iv(t)-iv(i)}),e]}function tv(t){var i,n;try{return null!==(n=null===(i=localStorage.getItem(t))||void 0===i?void 0:i.length)&&void 0!==n?n:0}catch(t){return 0}}function iv(t){var i;return null!==(i=A(t.slice(t.lastIndexOf(":")+1)))&&void 0!==i?i:0}function nv(t,i,n){return n?"".concat(t,":").concat(n,":").concat(i):"".concat(t,":").concat(i)}function rv(t,i,n,s,u,o){return void 0===u&&(u=Wu),r(this,void 0,mr,function(){var h,a,c,f,v,l,d=this;return e(this,function(p){switch(p.label){case 0:if(!S(t,s)||0===s.length)return[2];a=!1,c=function(t){return r(d,void 0,mr,function(){var i,r,s,u,o,c;return e(this,function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),i=t.bundle,r=i[0],s=i[2],"Sending ".concat(s," trailing bytes from last session as Seq ").concat(r),void 0!==h&&(t.serverBundleTime=h),[4,n.bundle(t)];case 1:return u=e.sent(),o=u[1],h=o.BundleTime,[2,xc.NoRetry];case 2:return On(c=e.sent())?(a=!0,[2,xc.NoRetry]):(cf(window)&&205===(null==c?void 0:c.status)&&(t.disableCompression=!0),[3,3]);case 3:return[2,xc.Retry]}})})},f=0,v=s,p.label=1;case 1:return f<v.length?(l=v[f],!a&&l&&l.bundle?[4,qc(t,i,c.bind(null,l),u,o)]:[2]):[3,4];case 2:p.sent(),p.label=3;case 3:return f++,[3,1];case 4:return[2]}})})}var ev=function(){function t(t,i,n){void 0===n&&(n=Wu);var r=this;this.N=t,this.lo=i,this["do"]=n,this.ga=t.options.multiStorage||new Yf(this.N,"_fs_songs",2e6),this.ma=new this["do"](this.N.wdx,this.N.window,function(){r.sing()},2e3),this.ya()}return t.prototype.ya=function(){return r(this,void 0,mr,function(){var t,i=this;return e(this,function(n){switch(n.label){case 0:return t=function(t){i.ba=t},[4,Xf(this.N.wdx,this.N.window,this["do"],t)];case 1:return n.sent(),this.sing(),[2]}})})},t.prototype.store=function(t,i){if(!this.N.options.useMockProtocol){"Saving ".concat(t.bundles.length," bundles in swan-song.");for(var n=0,r=t.bundles;n<r.length;n++){var e=r[n];if("string"!==e[1].type)return void qs(this.N.wdx,"song#arrayBuilder",{bundleType:e[1].type})}var s={Bundles:t.bundles,IsNewSession:t.isNewSession,LastBundleTime:t.lastBundleTime,OrgId:t.orgId,PageId:t.pageId,PageStartTime:this.N.time.startTime(),RecHost:Ps(this.N),ServerBundleTime:t.serverBundleTime,ServerPageStart:t.serverPageStart,SessionId:t.sessionId,UserId:t.userId,Version:t.version};this.ga.store(s),this.ma.isRunning()||this.ma.start()}},t.prototype.stop=function(){this.ma.stop()},t.prototype.sing=function(){return r(this,void 0,mr,function(){return e(this,function(t){switch(t.label){case 0:return this.N.options.useMockProtocol?[2]:[4,this.Sa(this.ga.retrieve())];case 1:return t.sent(),[4,this.ka(sv(this.N.wdx,"_fs_song"))];case 2:return t.sent(),[4,this.ka(sv(this.N.wdx,"_fs_swan_song"))];case 3:return t.sent(),[2]}})})},t.prototype.Sa=function(t){return r(this,void 0,mr,function(){var i,n;return e(this,function(r){switch(r.label){case 0:if(!t.length)return[2];i=t.length-1,r.label=1;case 1:return i>=0?(n=t[i],[4,this.ka(n)]):[3,4];case 2:r.sent(),r.label=3;case 3:return i--,[3,1];case 4:return[2]}})})},t.prototype.ka=function(t){return r(this,void 0,mr,function(){var i,n,r=this;return e(this,function(e){switch(e.label){case 0:return e.trys.push([0,2,,3]),void 0!==t&&t.Bundles&&t.UserId&&t.SessionId&&t.PageId?("Sending ".concat(t.Bundles.length," bundles as prior page swan song"),i=t.Bundles.reduce(function(i,n,e){var s=e===t.Bundles.length-1;return i.push({bundle:n,isNewSession:t.IsNewSession,orgId:t.OrgId,pageId:t.PageId,recHost:t.RecHost,serverBundleTime:t.ServerBundleTime,serverPageStart:t.ServerPageStart,sessionId:t.SessionId,userId:t.UserId,version:t.Version,deltaT:s?r.N.time.wallTime()-(t.LastBundleTime||0):null}),i},[]),[4,rv(this.N.wdx,this.N.window,this.lo,i,this["do"],function(t){r.jo=t})]):[2];case 1:return e.sent(),[3,3];case 2:return n=e.sent(),"Error recovering swan-song: ".concat(n),[3,3];case 3:return[2]}})})},t}();function sv(t,i){try{if(i in localStorage){var n=localStorage[i];return delete localStorage[i],function(t,i){return i.Bundles=function(t,i){for(var n=[],r=0,e=i.Bundles;r<e.length;r++){var s=e[r];S(t,s)?n.push(s):n.push(Qf(t,s))}return n}(t,i),i}(t,U(t,n))}}catch(t){"Error purging swan-song: ".concat(t)}}var uv=function(){function t(t,i){var n;this.N=t,this.lo=i,this._a=!1,this.Aa=0,this.Ia=!1,this.Ea=!1,this.Ta=t.time.now(),this.ga=new Yf(t,"_fs_bundle"),Ws(this.N.wdx,!!this.N.recording.identity,"dwell#identity"),this.Ca=null===(n=this.N.recording.identity)||void 0===n?void 0:n.getClientStore()}return t.prototype.init=function(t){var i,n,r,e,s;this.xa=t;var u=null!==(i=t.minDwellTime)&&void 0!==i?i:0;if(this.Aa=1e3*this.N.wdx.mathMin(u,10),this.Ta=this.N.time.now(),this.Aa&&this.lo.startBeacon({orgId:(null===(n=this.xa)||void 0===n?void 0:n.orgId)||"",userId:(null===(r=this.xa)||void 0===r?void 0:r.userId)||"",sessionId:(null===(e=this.xa)||void 0===e?void 0:e.sessionId)||""}),t.isNewSession||0===this.Aa)return[];var o=this.retrieve(t,!0);return null===(s=this.Ca)||void 0===s||s.setValue(Ei,t.sessionId,""),o},t.prototype.store=function(t){return this.ga.store(t,t.orgId)?(this._a=!0,!0):(this.Ia=!0,!1)},t.prototype.retrieve=function(t,i){var n=this;void 0===i&&(i=!1);var r=this.ga.retrieve({keyMeta:t.orgId,validate:function(t){return n.Ra(t,i)}});return this._a=!1,r},t.prototype.Ra=function(t,i){void 0===i&&(i=!1);try{if(t&&this.xa&&this.xa.orgId===t.orgId&&this.xa.userId===t.userId&&this.xa.sessionId===t.sessionId&&(i||this.xa.pageId===t.pageId))return!0}catch(t){}return!1},t.prototype.getState=function(){return this.Ka()?this._a?ai.ShouldFlush:ai.Inactive:ai.Pending},t.prototype.Ka=function(){var t,i;if(this.Ea||!(null===(t=this.xa)||void 0===t?void 0:t.isNewSession)||0===this.Aa||this.Ia)return!0;if(this.N.time.now()-this.Ta>this.Aa)return!0;var n=null===(i=this.Ca)||void 0===i?void 0:i.getValue(Ei);return((null==n?void 0:n.cookieValue)||(null==n?void 0:n.localStorageValue))===this.xa.sessionId&&(this.Ea=!0,!0)},t}(),ov=function(){function t(t,i,n,r,e,s,u){void 0===r&&(r=Du),void 0===e&&(e=Wu),void 0===s&&(s=new uv(t,i)),this.N=t,this.lo=i,this.Pu=n,this.Oo=r,this["do"]=e,this.ja=s,this.Ma=u,this.Pa=!1,this.Oa=0,this.Na=0,this.La=0,this.Io=!1,this.wo=0,this.Ua=[],this.Fa=Q(t.wdx),this.P=t.options.scheme,this.Ba=t.time.wallTime(),this.Ha=new ev(t,this.lo,e),this._o=Dc(this.N)}return t.prototype.addEndMarkerEvent=function(t){this._o.addEndMarkerEvent(t)},t.prototype.scheme=function(){return this.P},t.prototype.enqueueEvents=function(t,i){for(var n=0,r=i;n<r.length;n++){var e=r[n];this._o.add(e)}},t.prototype.Da=function(){return this.N.recording.bundleUploadInterval()},t.prototype.initUploadTicker=function(){this.Fa=Q(this.N.wdx),this.Wa=new this.Oo(this.N.wdx,this.N.window,this.Da())},t.prototype.startPipeline=function(t){var i,n=this;this.Eo=t.orgId,this.Ro=t.userId,this.xo=t.sessionId,this.To=t.pageId,this.Co=t.serverPageStart,this.Io=t.isNewSession,this._o.init(),this.za(this.ja.init(t),this["do"]),br(this.N.wdx,this.N.window,this.Da()/2).then(function(){n.N.recording.flags().UseMinNetworkBudget&&n.qa({bypassMinBudget:!0}),n.Va()}),this.Wa||this.initUploadTicker(),null===(i=this.Wa)||void 0===i||i.start(function(){n.N.recording.flags().UseMinNetworkBudget&&Q(n.N.wdx)-n.Fa>=2e4&&(n.Fa=Q(n.N.wdx),n.qa({bypassMinBudget:!0})),n.Va()})},t.prototype.stopPipeline=function(){this.Wa&&this.Wa.stop(),this._o=Dc(this.N,this._o),this.Ua=[],this.Ha.stop(),this.Eo=void 0,this.Ro=void 0,this.xo=void 0,this.To=void 0,this.Co=void 0,this.Io=!1},t.prototype.send=function(t){var i,n;return r(this,void 0,mr,function(){var r,s;return e(this,function(e){switch(e.label){case 0:switch(r=null!==(i=null==t?void 0:t.mode)&&void 0!==i?i:"flush",s=null!==(n=null==t?void 0:t.reason)&&void 0!==n?n:bt.Unknown,r){case"flush":return[3,1];case"sing":return[3,3]}return[3,4];case 1:return this.qa({flush:!0}),[4,this.$a()];case 2:return e.sent(),[3,5];case 3:return this.Ga(s),[3,5];case 4:jr(),e.label=5;case 5:return[2]}})})},t.prototype.Ga=function(t){if(this.qa({flush:!0}),this.Qa(this.Xa))Ws(this.N.wdx,!this.Xa,"_pendingBundle in dwell period");else{var i=[];this.Xa&&i.push(this.Xa);for(var r=0,e=this.Ua;r<e.length;r++){var s=e[r];i.push(s)}var u=this.Ya(i);if(u){this.Ha.store(u,t);for(var o=0,h=i;o<h.length&&(s=h[o],this.lo.bundleBeacon(n(n({},u),{bundle:s,deltaT:null})));o++);}}},t.prototype.Ya=function(t){if(t.length){if(this.Eo&&this.Ro&&this.xo&&this.To&&void 0!==this.Co)return{bundles:t,isNewSession:this.Io,lastBundleTime:this.Ba,orgId:this.Eo,pageId:this.To,serverBundleTime:this.wo,serverPageStart:this.Co,sessionId:this.xo,userId:this.Ro,version:this.N.recording.bundleApiVersion()};"unable to build stored bundle. one or more of orgId:".concat(this.Eo," | userId:").concat(this.Ro," | sessionId:").concat(this.xo," | pageId:").concat(this.To," | serverPageStart:").concat(this.Co," are undefined")}},t.prototype.Ja=function(){var t;if(this.Eo&&this.Ro&&this.xo){for(var i=[],n=0,r=this.ja.retrieve({orgId:this.Eo});n<r.length;n++){var e=r[n];i.push.apply(i,e.bundles)}(t=this.Ua).unshift.apply(t,i)}},t.prototype.Qa=function(t){switch(this.ja.getState()){case ai.Pending:var i=t?s([t],this.Ua,!0):this.Ua,n=this.Ya(i);if(!n)return!1;var r=this.ja.store(n);return r?this.Ua=[]:this.Ja(),r;case ai.ShouldFlush:return this.Ja(),!1;case ai.Inactive:default:return!1}},t.prototype.Va=function(){return r(this,void 0,mr,function(){return e(this,function(t){switch(t.label){case 0:return this.Xa?(this.Pa&&this.Za(),[2]):(this.qa(),[4,this.$a()]);case 1:return t.sent(),[2]}})})},t.prototype.qa=function(t){void 0===t&&(t={});var i=this._o.nextBundle(t);i&&this.Ua.push(i)},t.prototype.$a=function(){return r(this,void 0,mr,function(){return e(this,function(t){switch(t.label){case 0:return this.To&&this.Co&&!this.Xa&&0!=this.Ua.length?this.Qa()?[2]:(this.Xa=this.Ua.shift(),[4,this.Za()]):[2];case 1:return t.sent(),[2]}})})},t.prototype.Za=function(){var t;return r(this,void 0,mr,function(){var i,n,r,s,u;return e(this,function(e){switch(e.label){case 0:if((i=this.N.time.wallTime())<this.Na)return[2];if(!(n=this.Xa))return[2];this.Pa=!1,this.La=this.Ba=i,e.label=1;case 1:return e.trys.push([1,5,,6]),[4,this.tc(n)];case 2:return(r=e.sent())?(this.wo=r.BundleTime,this.Xa=void 0,this.Na=0,this.Oa=0,this.N.time.wallTime()-this.La>this.Da()?[4,this.$a()]:[3,4]):[2];case 3:e.sent(),e.label=4;case 4:return[3,6];case 5:if(s=e.sent(),u="Failed to send bundle",s instanceof Mn){if(Pn(s.status))return 206===s.status||s.status>=500&&Bs.send(this.N.wdx,u,{err:s,status:s.status}),null===(t=this.Ma)||void 0===t||t.call(this),[2]}else Bs.send(this.N.wdx,u,{err:s,status:0});return this.Pa=!0,this.Na=this.La+Vc(this.N.wdx,this.Oa++),[3,6];case 6:return[2]}})})},t.prototype.tc=function(t){var i;return r(this,void 0,mr,function(){var n,r,s,u,o;return e(this,function(e){switch(e.label){case 0:return this.Eo&&this.Ro&&this.xo&&this.To?(window,n=this.Pu.getMsSinceLastUserActivity(),[4,this.lo.bundle({bundle:t,deltaT:null,isNewSession:this.Io,lastUserActivity:n,orgId:this.Eo,pageId:this.To,serverBundleTime:this.wo,serverPageStart:this.Co,sessionId:this.xo,userId:this.Ro,version:this.N.recording.bundleApiVersion()})]):("unable to send bundle. one or more of orgId:".concat(this.Eo," | userId:").concat(this.Ro," | sessionId:").concat(this.xo," | pageId:").concat(this.To," are undefined"),[2]);case 1:return r=e.sent(),s=r[0],u=r[1],null===(i=this.N.recording.observer)||void 0===i||i.addEvent({type:zf.INTERNAL_BUNDLE,data:{clientTime:Q(this.N.wdx),lastActivity:n,orgId:this.Eo,pageId:this.To,pageStart:this.Co,prevBundleTime:this.wo,recHost:Ps(this.N),response:u,seq:t[0],sessionId:this.xo,size:s,userId:this.Ro}}),o=t[0],s>16e6&&o>=16&&("splitting large page: ".concat(s),this.N.recording.splitPage(bt.Size)),window,[2,u]}})})},t.prototype.za=function(t,i){return r(this,void 0,mr,function(){var n,r=this;return e(this,function(e){return 0===t.length?[2]:(n=t.reduce(function(t,i,n){return t.push.apply(t,i.bundles.map(function(t){return{bundle:t,isNewSession:i.isNewSession,orgId:r.Eo,userId:r.Ro,sessionId:r.xo,pageId:i.pageId,version:i.version,serverBundleTime:i.serverBundleTime,serverPageStart:i.serverPageStart,deltaT:null}})),t},[]),[2,rv(this.N.wdx,this.N.window,this.lo,n,i)])})})},t}(),hv="_fs_preview",av=new RegExp("(^\\?|&)".concat(hv,"=(?:true|false)(&|$)")),cv=function(){function t(t,i,n){this.N=t,this.Ca=n,this.ic="fs_preview_".concat(i)}return t.prototype.isPreviewMode=function(){return this.nc()||this.rc()},t.prototype.clear=function(){this.Ca.setValue(this.ic,"",new Date(1970,1,1).toUTCString())},t.prototype.write=function(){var t=this.nc(),i=this.ec();(t||i)&&(t?this.Ca.setValue(this.ic,"true",new Date(Q(this.N.wdx)+432e5).toUTCString()):this.clear(),this.sc())},t.prototype.inject=function(t,i,n){$i(function(){var r="FullStory-preview-script";if(!t.getElementById(r)){var e=t.createElement("script");e.id=r,e.async=!0,e.src="".concat(i,"//").concat(n,"/s/fspreview.js"),t.head.appendChild(e)}})},t.prototype.sc=function(){if(this.N.window.history){var t=location.search.replace(av,function(t,i,n){return n?i:""});this.N.window.history.replaceState({},"",this.N.window.location.href.replace(location.search,t))}},t.prototype.nc=function(){return this.N.window.document.location.search.indexOf("".concat(hv,"=true"))>-1},t.prototype.ec=function(){return this.N.window.document.location.search.indexOf("".concat(hv,"=false"))>-1},t.prototype.rc=function(){var t=this.Ca.getValue(this.ic),i=t.cookieValue,n=t.localStorageValue;return!(!i&&!n)},t}();function fv(t){var i,n,r;return{Kind:Y.CAPTURE_SOURCE,Args:[t.type,t.entrypoint,"dom",null===(n=null===(i=t.source)||void 0===i?void 0:i.integration)||void 0===n?void 0:n.slice(0,1024),!!(null===(r=t.source)||void 0===r?void 0:r.userInitiated)]}}function vv(t){var i=function(t){return"msCrypto"in t?t.msCrypto:t.crypto}(t);if("function"==typeof(null==i?void 0:i.randomUUID))return i.randomUUID();var n=new Uint8Array(16);i.getRandomValues(n),n[6]=15&n[6]|64,n[8]=63&n[8]|128;for(var r=[],e=0;e<n.length;e++)r.push(function(t,i,n){for(var r=t;r.length<2;)r="".concat("0").concat(r);return r}(n[e].toString(16)));return["".concat(r[0]).concat(r[1]).concat(r[2]).concat(r[3]),"".concat(r[4]).concat(r[5]),"".concat(r[6]).concat(r[7]),"".concat(r[8]).concat(r[9]),"".concat(r[10]).concat(r[11]).concat(r[12]).concat(r[13]).concat(r[14]).concat(r[15])].join("-")}var lv=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;function dv(t){return 36===t.length&&lv.test(t)}var pv=void 0,wv=18e5,gv=function(){function t(t,i,n,r){void 0===r&&(r=Wu),this.N=t,this.Pu=i,this.uc=n,this.oc=0,this.hc=new r(this.N.wdx,this.N.window,this.ac.bind(this))}return t.prototype.createUserSessionPage=function(){return e=function(t,i){var n,r,e,s=i.getUserId();if(!s||!dv(s))return[pv,"uid ".concat(s)];var u=i.getSessionId();if(!u||!dv(u))return[pv,"sid ".concat(u)];var o=t.time.wallTime(),h=null!==(n=i.getSessionStartTimeMS())&&void 0!==n?n:0,a=t.wdx.mathAbs(o-h);if(0===h||a>=864e5)return[pv,"exp sid: ".concat(h,"ms ").concat(a,"ms")];var c=null!==(r=i.getLastUserActivityTimeMS())&&void 0!==r?r:h,f=t.wdx.mathMax(c,h),v=t.wdx.mathAbs(o-f);if(v>=wv)return[pv,"exp lua: ".concat(c,"ms ").concat(v,"ms")];var l=null!==(e=i.getPageCount())&&void 0!==e?e:0;return l>=250?[pv,"pages: ".concat(l)]:[u]}(t=this.N,i=this.uc),s=e[0],u=e[1],o=s===pv,h=t.time.wallTime(),a=vv(t.window),c=i.getSessionId(),f=i.getUserId(),v=(null!==(n=i.getPageCount())&&void 0!==n?n:0)+1,l=null!==(r=i.getSessionStartTimeMS())&&void 0!==r?r:h,o&&(c=vv(t.window),f=function(t,i){var n=i.getUserId();return n&&dv(n)?n:vv(t.window)}(t,i),v=1,l=h),{userId:f,sessionId:c,pageId:a,isNewSession:o,lastActivityTime:h,pageCount:v,reason:u,sessionStartTime:l};var t,i,n,r,e,s,u,o,h,a,c,f,v,l},t.prototype.persist=function(t){this.uc.create({userId:t.userId,sessionId:t.sessionId,sessionStartTime:t.sessionStartTime,lastUserActivityTime:t.lastActivityTime,pageCount:t.pageCount})},t.prototype.start=function(){this.hc.start(3e5)},t.prototype.stop=function(){this.hc.stop()},t.prototype.ac=function(){var t=this.Pu.getLastUserActivityTS();t!==this.oc&&(this.oc=t,this.uc.setLastUserActivityTimeMS(t)),this.start()},t}(),mv=function(t){function s(i,n,r,e,s,u,o){void 0===r&&(r=new Pc(i)),void 0===e&&(e=new ov(i,n,r,void 0,void 0,void 0,function(){return a.shutdown(bt.SettingsBlocked)})),void 0===s&&(s=Du),void 0===u&&(u=jf),void 0===o&&(o=new Gc(i,n));var h,a=t.call(this,i,s,r,e,u,o)||this;a.lo=n,a.Mo=e,a.No=o,a.cc=!1,a.Bo=!1,a.fc=!1,a.Xt=a.vt.document,a.Lo=0,a.vc=i.recording.identity,a.lc=new cv(a.N,a.Eo,a.vc.getClientStore()),a.Oh=Pf.NoInfoYet,a.dc=new gv(i,r,a.vc),h=function(t){if(a.Hh.stop(bt.Api),t){var i=a.Xt.getElementById(t);i&&a.wc&&i.setAttribute("_fs_embed_token",a.wc)}},a.vt._fs_shutdown=h;var c=a.lc.isPreviewMode();return a.gc=a.lo.settings({orgId:a.Eo,previewMode:c})["catch"](function(){}),a}return i(s,t),s.prototype.onDomLoad=function(){var i=this;t.prototype.onDomLoad.call(this),this.cc=!0,this.ca(function(){i.Jh(i.Bo)})},s.prototype.mc=function(){var t=this.N.options.replayFlags;if(/[?&]_fs_force_session=true(&|#|$)/.test(location.search)&&(t="".concat(t,",forceSession"),this.vt.history)){var i=location.search.replace(/(^\?|&)_fs_force_session=true(&|$)/,function(t,i,n){return n?i:""});this.vt.history.replaceState({},"",this.vt.location.href.replace(location.search,i))}return t},s.prototype.start=function(i,n,s){var u,o,h,a,c,f,v;return r(this,void 0,mr,function(){var r,l,d,p,w,g,m,y,b,S,k,_,A,I,E,T,C,x,R,K,j,M,P,O,N,U;return e(this,function(e){switch(e.label){case 0:return t.prototype.start.call(this,i,n,s),[4,this.gc];case 1:if(!(r=e.sent()))return this.yc(),[2];Je(null!==(u=r.OrgSettings.UrlPrivacyConfig)&&void 0!==u?u:ui.DefaultOrgSettings.UrlPrivacyConfig,r.OrgSettings.MaxUrlLength),l=this.mc(),e.label=2;case 2:return e.trys.push([2,4,,5]),[4,mr.race([Kr(this.N.wdx,this.N.window),br(this.N.wdx,this.N.window,250)])];case 3:case 4:return e.sent(),[3,5];case 5:d=Ru(this.Xt),p=d[0],w=d[1],F=this.N.window,B=0,D=0,g=null==F.screen?[B,D]:(B=parseInt(String(F.screen.width),10),D=parseInt(String(F.screen.height),10),[B=isNaN(B)?0:B,D=isNaN(D)?0:D]),m=g[0],y=g[1],b="",i||(b=this.vc.getUserId()),S=this.dc.createUserSessionPage(),k=this.vc.getAppId(),_=this.vc.getAppKeyHash(),A=this.lc.isPreviewMode(),I=null!==(a=null===(h=null===(o=this.N)||void 0===o?void 0:o.recording)||void 0===h?void 0:h.preroll)&&void 0!==a?a:-1,E=$e(this.N.wdx,Vr(this.vt),{source:"page",type:"base"}),T=$e(this.N.wdx,this.vt.location.href,{source:"page",type:"url"}),C=""===this.Xt.referrer?"":$e(this.N.wdx,this.Xt.referrer,{source:"page",type:"referrer"}),x=L(this.Xt),R=null!==(c=this.N.options.tabId)&&void 0!==c?c:function(t){var i,n="_fs_tab_id";try{var r=t.sessionStorage.getItem(n);if(r)return r;var e=Math.floor(1e17*Math.random()).toString(16);return t.sessionStorage.setItem(n,e),null!==(i=t.sessionStorage.getItem(n))&&void 0!==i?i:void 0}catch(t){return}}(this.vt),K={OrgId:this.Eo,UserId:b,SessionId:S.sessionId,PageId:S.pageId,Url:T,Base:E,Width:p,Height:w,ScreenWidth:m,ScreenHeight:y,SnippetVersion:this.N.options.snippetVersion,Referrer:C,Preroll:I,Doctype:x,CompiledVersion:ys.Ver,CompiledTimestamp:ys.TS,AppId:k,TabId:R,PreviewMode:A||void 0,SessionKeyHash:"",PageKeyHash:""},l&&(K.ReplayFlags=l),e.label=6;case 6:return e.trys.push([6,12,,13]),[4,this.lo.page(K)];case 7:return j=e.sent(),A||!j.PreviewMode?[3,9]:[4,this.lo.settings({orgId:this.Eo,previewMode:!0})["catch"](function(){})];case 8:if(!(r=e.sent()))return this.yc(),[2];Je(null!==(f=r.OrgSettings.UrlPrivacyConfig)&&void 0!==f?f:ui.DefaultOrgSettings.UrlPrivacyConfig,r.OrgSettings.MaxUrlLength),e.label=9;case 9:return[4,this.bc(S,j,r)];case 10:return U=e.sent(),$f(this.N.wdx,U)?this.Fh?[2]:(window,M=this.vc.getConsentStore().getConsentState(),this.Zh(U,M),window,this.Sc(U.CookieDomain,U.UserIntId,U.SessionIntId,U.PageIntId,U.EmbedToken),this.kc(),U.PreviewMode&&this.lc.inject(this.Xt,this.P,this.qh),(P=this.N.options.pageStart)&&P(),this.Ft.enqueueFirst(fv({type:"default"})),this.Ft.enqueueFirst(this.Hh.getNavigateEvent(this.vt.location.href,Y.ENTRY_NAVIGATE)),this.Ft.enqueueFirst({Kind:Y.SYS_REPORTCONSENT,Args:[M,mt.Document]}),this.Ft.enqueueFirst({Kind:Y.SET_FRAME_BASE,Args:[$e(this.N.wdx,Vr(this.vt),{source:"event",type:Y.SET_FRAME_BASE}),x,T,C]}),O={Kind:Y.PAGE_DATA,Args:[T,E,p,w,m,y,this.N.options.snippetVersion,C,x,I,b,U.PageStart,H(this.N.wdx,this.N.window),this.vt.navigator.userAgent,R,!!U.IsNewSession,!!U.PreviewMode,k,_,ys.TS,ys.Ver,"",""]},this.Mo.addEndMarkerEvent(O),null===(v=this.No)||void 0===v||v.addEndMarkerEvent(O),U.Flags.DisableInertBundles&&(Bc.forceFlush=!0),this.Ft.enqueue({Kind:Y.SCRIPT_COMPILED_VERSION,Args:[ys.Ver]}),this.Ft.enqueue({Kind:Y.RESIZE_LAYOUT,Args:[p,w]}),this.Hh.addVisibilityChangeEvent(),this.sa(),[4,this.Ft.startPipeline({isNewSession:!!U.IsNewSession,orgId:this.Eo,pageId:U.PageIntId,serverPageStart:U.PageStart,sessionId:U.SessionIntId,userId:U.UserIntId,minDwellTime:U.Flags.UseDwellTime&&U.DwellTime||0,fixWhenValues:U.Flags.FixWhenValues})]):[2,this.yc()];case 11:return e.sent(),this.fa(this.Xt),this.ia(),[3,13];case 12:return(N=e.sent())instanceof Mn&&(U=N.data)&&U.user_id&&U.cookie_domain&&U.reason_code===Xt.ReasonBlockedTrafficRamping&&b!==U.user_id&&this.Sc(U.cookie_domain,U.user_id,"","",""),this.yc(),[3,13];case 13:return[2]}var F,B,D})})},s.prototype.kc=function(){var t=this;this.fc=!0,this.ca(function(){t.Jh(t.Bo)})},s.prototype.Sc=function(t,i,n,r,e){var s=this.vc;s.setIds(this.vt,t,i,n),this.wc=e,this.lc.write(),"/User,".concat(s.getUserId(),"/Session,").concat(s.getSessionId(),"/Page,").concat(r)},s.prototype.ca=function(i){this.cc&&this.fc&&t.prototype.ca.call(this,i)},s.prototype.yc=function(){this.na(),this.shutdown(bt.SettingsBlocked),this.Bo=!0,this.Jh(this.Bo)},s.prototype.bc=function(t,i,s){var u,o,h;return r(this,void 0,mr,function(){var r,a,c,f;return e(this,function(e){return(r=n(n({},i),s)).Flags.UseClientSideId&&(dv(a=null!==(u=r.UserUUID)&&void 0!==u?u:"")&&(t.userId=a),dv(c=null!==(o=r.SessionUUID)&&void 0!==o?o:"")&&(t.sessionId=c),dv(f=null!==(h=r.PageUUID)&&void 0!==h?h:"")&&(t.pageId=f),this.vc.setCookieDomain(this.vt,r.CookieDomain),this.dc.persist(t),r=n(n({},r),{UserIntId:t.userId,SessionIntId:t.sessionId,PageIntId:t.pageId,IsNewSession:t.isNewSession,PageStart:Q(this.N.wdx)}),t.reason&&this.Ft.enqueue({Kind:Y.SESSION_INFO,Args:[t.reason]})),this.dc.start(),[2,r]})})},s.prototype.onMessageReceived=function(i,n){t.prototype.onMessageReceived.call(this,i,n),(null==i?void 0:i.parent)==this.vt&&n[0]===gi.EndPreviewMode&&this.lc.clear()},s.prototype.onNavigate=function(){this.eventWatcher().onNavigate()},s}(Vf),yv=function(){function t(t,i){void 0===i&&(i=new bv(t)),this.N=t,this._c=i}return t.prototype.enqueueEvents=function(t,i){var n=null!=t?t:void 0;this._c.postMessage(this.N.window.parent,[gi.EvtBundle,gh(this.N.wdx,i),n],n)},t.prototype.startPipeline=function(){},t.prototype.stopPipeline=function(){},t.prototype.addEndMarkerEvent=function(t){},t}(),bv=function(){function t(t){this.N=t}return t.prototype.postMessage=function(t,i,n){switch(i[0]){case gi.EvtBundle:Cf(this.N,i[0],ds(this.N.wdx,i[1]),n)||Ef(this.N,t,i);break;case gi.RequestFrameId:Cf(this.N,i[0],"[]",n)||Ef(this.N,t,i);break;default:"Unknown message type: ".concat(i[0])}},t}(),Sv=function(t){function n(i,n,r,e,s){void 0===n&&(n=new bv(i)),void 0===r&&(r=new yv(i,n)),void 0===e&&(e=Du),void 0===s&&(s=jf);var u=t.call(this,i,e,void 0,r,s,void 0)||this;return u._c=n,u.Xt=u.vt.document,u}return i(n,t),n.prototype.ca=function(){var i,n;this.N.recording.inWebView&&(null===(n=null===(i=this.Vh)||void 0===i?void 0:i.Flags)||void 0===n?void 0:n.FetchChildIntegrations)&&t.prototype.ca.call(this)},n.prototype.start=function(i,n,r){var e=this;t.prototype.start.call(this,i,n,r),this.Ac(),this.et.add(this.vt,"load",!1,function(){e.Hh.recordingIsDetached()&&e.N.recording.splitPage(bt.FsShutdownFrame)}),this.Hh.addVisibilityChangeEvent()},n.prototype.onMessageReceived=function(i,n){var s;return r(this,void 0,mr,function(){var r,u,o,h,a,c,f;return e(this,function(e){switch(e.label){case 0:if(t.prototype.onMessageReceived.call(this,i,n),i!==this.vt.parent&&i!==this.vt)return[2];switch(n[0]){case gi.GreetFrame:return[3,1];case gi.SetFrameId:return[3,2];case gi.SetConsent:return[3,3];case gi.InitFrameMobile:return[3,4]}return[3,7];case 1:return this.Ac(n[1]),[3,7];case 2:try{if(!(r=n[1]))return u=$e(this.N.wdx,location.href,{source:"log",type:"debug"}),"Outer page gave us a bogus frame Id! Iframe: ".concat(u),[2];this.Ic({frameId:r,parentIds:n[2],outerStartTime:n[3],scheme:n[4],script:n[5],appHost:n[6],orgId:n[7],initConfig:n[8],pageRsp:n[9],consented:n[10],minimumWhen:n[11]})}catch(t){"Failed to parse frameId from message: ".concat(ds(this.N.wdx,n))}return[3,7];case 3:return this.setConsent(n[1]),[3,7];case 4:return e.trys.push([4,6,,7]),o=JSON.parse(n[1]),h=o.StartTime,a=void 0,n.length>2&&n[2]&&(c=n[2],Object.prototype.hasOwnProperty.call(c,"ProtocolVersion")&&c.ProtocolVersion>=20180723&&Object.prototype.hasOwnProperty.call(c,"OuterStartTime")&&(h=c.OuterStartTime),Object.prototype.hasOwnProperty.call(c,"MobileScriptPath")&&(a=c.MobileScriptPath)),f=o.Host,[4,this.Ic({frameId:0,parentIds:[],outerStartTime:h,scheme:"https:",script:null!=a?a:jn(f),appHost:Kn(f),orgId:o.OrgId,initConfig:void 0,pageRsp:o.PageResponse,consented:null!==(s=this.Ui.getConsent())&&void 0!==s?s:wt.RevokeConsent})];case 5:return e.sent(),[3,7];case 6:return e.sent(),"Failed to initialize mobile web recording from message: ".concat(ds(this.N.wdx,n)),[3,7];case 7:return[2]}})})},n.prototype.Ac=function(t){this.Lo&&this.Lo===t||0!=this.Lo&&this.vt.parent&&this._c.postMessage(this.vt.parent,[gi.RequestFrameId])},n.prototype.Ic=function(t){var i;return r(this,void 0,mr,function(){var n,r,s,u,o,h=this;return e(this,function(e){switch(e.label){case 0:if(this.Lo)return this.Lo!==t.frameId?("Updating frame id from ".concat(this.Lo," to ").concat(t.frameId),this.N.recording.splitPage(bt.FsShutdownFrame)):"frame Id is already set to ".concat(this.Lo),[2];if(n=$e(this.N.wdx,location.href,{source:"log",type:"debug"}),"FrameId received within frame ".concat(n,":").concat(t.frameId),this.P=t.scheme,this.Dh=t.script,this.qh=t.appHost,this.Eo=t.orgId,this.$h=t.initConfig,this.Lo=t.frameId,this.Uo=t.parentIds,!t.pageRsp||!$f(this.N.wdx,t.pageRsp))return this.shutdown(bt.FsShutdownFrame),[2];if(this.Fh)return[2];r=t.consented,this.Zh(t.pageRsp,r),this.ca(),this.Jh(),this.Ft.enqueueFirst(fv({type:"default"})),s=this.N.recording.inWebView,u=$e(this.N.wdx,this.vt.location.href,{source:"page",type:s?"url":"base"}),o=""===this.Xt.referrer?"":$e(this.N.wdx,this.Xt.referrer,{source:"page",type:s?"referrer":"base"}),this.Ft.enqueueFirst(this.Hh.getNavigateEvent(u,Y.ENTRY_NAVIGATE)),this.Ft.enqueueFirst({Kind:Y.SYS_REPORTCONSENT,Args:[r,mt.Document]}),this.Ft.enqueueFirst({Kind:Y.SET_FRAME_BASE,Args:[$e(this.N.wdx,Vr(this.vt),{source:"event",type:Y.SET_FRAME_BASE}),L(this.vt.document),u,o]}),this.Ft.enqueue({Kind:Y.SCRIPT_COMPILED_VERSION,Args:[ys.Ver]}),e.label=1;case 1:return e.trys.push([1,3,,4]),[4,mr.race([Kr(this.N.wdx,this.vt),br(this.N.wdx,this.vt,250)])];case 2:case 3:return e.sent(),[3,4];case 4:return this.Ft.enqueue({Kind:Y.RESIZE_LAYOUT,Args:Ru(this.vt.document)}),this.sa(),this.Ft.rebaseIframe(t.outerStartTime,null!==(i=t.minimumWhen)&&void 0!==i?i:0),this.N.time.setStartTime(t.outerStartTime),this.Ro&&this.xo&&this.To?(this.Ft.startPipeline({frameId:t.frameId,isNewSession:!!t.pageRsp.IsNewSession,orgId:this.Eo,pageId:t.pageRsp.PageIntId,parentIds:t.parentIds,serverPageStart:t.pageRsp.PageStart,sessionId:t.pageRsp.SessionIntId,userId:t.pageRsp.UserIntId,minDwellTime:0}).then(function(){h.ra(),h.fa(h.vt.document),h.ia()}),[2]):("one or more of userId:".concat(this.Ro," | sessionId:").concat(this.xo," | pageId:").concat(this.To," are undefined"),[2])}})})},n}(Vf),kv=function(){function t(t,i,n){void 0===i&&(i=function(){}),void 0===n&&(n=!1),this.Xt=t,this.Ec=i,this.Tc=n,this._cookies={},this._cookies=o(this.Xt)}return t.prototype.setDomain=function(t){this.Cc=t},t.prototype.getValue=function(t,i){return this.xc(t,i)},t.prototype.setValue=function(t,i,n,r){var e=this;if(null!=this.Cc&&!this.Tc){var s=[];this.Rc(t,r,i,function(){return e.Kc(t,r,i,n)},s),s.length>0&&this.Ec(s)}},t.prototype.setCookie=function(t,i,n){this._setCookie(t,i,n)},Object.defineProperty(t.prototype,"cookies",{get:function(){return this._cookies},enumerable:!1,configurable:!0}),t.prototype.clearCookie=function(t,i){this._cookies[t]&&(this.Xt.cookie=_v(this.Cc,t,"","Thu, 01 Jan 1970 00:00:01 GMT"),delete this._cookies[t]);try{delete localStorage[null!=i?i:t]}catch(t){}},t.prototype._setCookie=function(t,i,n){try{if(this.Xt.cookie=_v(this.Cc,t,i,n),this.Xt.cookie&&this.Xt.cookie.indexOf(i)>-1)return;this.Xt.cookie=_v(this.Cc,t,i,n,"None")}finally{this._cookies=o(this.Xt)}},t.prototype.Kc=function(t,i,n,r){this._setCookie(t,n,r),function(t,i){try{localStorage[t]=i}catch(t){}}(null!=i?i:t,n)},t.prototype.xc=function(t,i){var n,r=this._cookies[t];try{n=localStorage[null!=i?i:t]}catch(t){}return{cookieValue:r,localStorageValue:n}},t.prototype.Rc=function(t,i,n,r,e,s){void 0===s&&(s=3),r();for(var u=!1,o=!1,h=1;h<s;h++){var a=this.xc(t,i),c=a.cookieValue;if(o=a.localStorageValue===n,(u=c===n)&&o)return!0;if(u)break;r()}return u||e.push([t,"cookie",n]),o||e.push([null!=i?i:t,"localStorage",n]),!1},t}();function _v(t,i,n,r,e){void 0===e&&(e="Strict");var s="".concat(i,"=").concat(n),u=function(t){if(t)return".".concat(encodeURIComponent(t))}(t);return u&&(s+="; domain=".concat(u)),r&&(s+="; Expires=".concat(r)),s+="; path=/; SameSite=".concat(e),"https:"===location.protocol&&(s+="; Secure"),s}function Av(t,i,n,r){var e=n(t),s=n(i);return e?s?r(e,s):e:s}var Iv=1,Ev=function(){function t(t,i,n,r,e){var s;this.N=t,this.uc=i,this.Eo=n,this.jc=r,this.Mc=e,s=_f("fs_cid","_fs_cid",this.Eo,this.jc),this.Pc=s[0],this.Oc=s[1],this.Nc=s[2],this.Lc=s[3];var u=Af(this.Mc,this.uc,this.Pc,this.Oc,this.Nc,this.Lc),o=u[0],h=u[1],a=o||h;this.Uc=function(t){var i={consent:wt.RevokeConsent};if(!t)return i;var n=t.split(".");return n.length<1?i:(n[0],"1"===n[1]?{consent:wt.GrantConsent}:i)}(a)}return t.prototype.getConsentState=function(){return this.Uc.consent},t.prototype.setConsentState=function(t){var i,n;this.Mc||this.uc.clearCookie(this.Nc,this.Lc),this.Uc.consent=t,t!==wt.RevokeConsent?this.uc.setValue(this.Pc,(n=this.Uc.consent,[Iv,n===wt.GrantConsent?1:0].join(".")),(i=this.N.wdx,new Date(1e3*ct(i)).toUTCString()),this.Oc):this.uc.clearCookie(this.Pc,this.Oc)},t}(),Tv=1,Cv=function(){function t(t,i,n,r,e){var s;this.N=t,this.Eo=n,this.jc=r,this.Mc=e,this.uc=i,s=_f("fs_lua","_fs_lua",this.Eo,this.jc),this.Pc=s[0],this.Oc=s[1],this.Nc=s[2],this.Lc=s[3];var u=Af(this.Mc,this.uc,this.Pc,this.Oc,this.Nc,this.Lc),o=u[0],h=u[1];this.Uc=function(t,i){var n;return null!==(n=Av(t,i,xv,function(t,i){return(i.lastUserActivityTime||0)>(t.lastUserActivityTime||0)?i:t}))&&void 0!==n?n:{lastUserActivityTime:void 0}}(o,h)}return t.prototype.getLastUserActivityTimeMS=function(){return this.Uc.lastUserActivityTime},t.prototype.setLastUserActivityTimeMS=function(t){var i;this.Mc||this.uc.clearCookie(this.Nc,this.Lc),this.Uc.lastUserActivityTime=t,this.uc.setValue(this.Pc,function(t){return[Tv,t].join(".")}(t),(i=this.N.wdx,new Date(function(t){return Q(t)+wv}(i)).toUTCString()),this.Oc)},t}();function xv(t){var i={lastUserActivityTime:void 0};if(!t)return i;var n=t.split(".");return n.length<1?i:(n[0],{lastUserActivityTime:A(n[1])})}var Rv,Kv,jv=function(){function t(t,i,n,r,e){var s;this.N=t,this.uc=i,this.Eo=n,this.jc=r,this.Mc=e,s=_f(Mv,"_fs_uid",this.Eo,this.jc),this.Pc=s[0],this.Oc=s[1],this.Nc=s[2],this.Lc=s[3],this.Uc=this.Fc(this.Eo)}return t.prototype.getIdentity=function(){return this.Uc},t.prototype.setIdentity=function(t){this.Uc=t,this.Bc()},t.prototype.Fc=function(t){var i=Af(this.Mc,this.uc,this.Pc,this.Oc,this.Nc,this.Lc),n=i[0],r=i[1],e=function(t,i,n,r){return Av(i,n,ft.bind(null,t),function(t,i){return t.orgId!==r&&i.orgId!==r?null:t.orgId!==r?i:i.orgId!==r?t:i.expirationAbsTimeSeconds>t.expirationAbsTimeSeconds?i:t})}(this.N.wdx,n,r,t);return e||{expirationAbsTimeSeconds:ct(this.N.wdx),orgId:t,userId:"",sessionId:"",appKeyHash:""}},t.prototype.Bc=function(){this.Uc.expirationAbsTimeSeconds++,this.Mc||this.uc.clearCookie(this.Nc,this.Lc);var t,i=this.fo(),n=(t=this.Uc.expirationAbsTimeSeconds,new Date(1e3*t).toUTCString());this.uc.setValue(this.Pc,i,n,this.Oc)},t.prototype.fo=function(){var t,i,n,r=[this.Uc.userId,null!==(t=this.Uc.sessionId)&&void 0!==t?t:"","".concat(null!==(i=this.Uc.sessionStartTime)&&void 0!==i?i:""),"","".concat(null!==(n=this.Uc.pageCount)&&void 0!==n?n:"")].join(":"),e=["",this.Uc.orgId,r];return this.Uc.appKeyHash&&e.push(encodeURIComponent(this.Uc.appKeyHash)),e.push("/".concat(this.Uc.expirationAbsTimeSeconds)),e.join("#")},t}(),Mv="fs_uid",Pv=function(){function t(t,i,n,r){var e,s;void 0===n&&(n=function(){}),void 0===r&&(r=!1),this.N=t,this.xa=i,this.Hc=void 0,i.useMockProtocol?this.Ca=new Hf:this.Ca=null!==(e=i.clientStore)&&void 0!==e?e:new kv(this.N.window.document,n,r);var u=!!i.useNamespace,o=!!i.isolated;this.Dc=null!==(s=i.identityStore)&&void 0!==s?s:new jv(this.N,this.Ca,i.orgId,u,o),this.Wc=new Ev(this.N,this.Ca,i.orgId,u,o),this.zc=new Cv(this.N,this.Ca,i.orgId,u,o)}return t.prototype.getConsentStore=function(){return this.Wc},t.prototype.clear=function(){this.zc.setLastUserActivityTimeMS(void 0);var t=this.Dc.getIdentity();t.sessionStartTime=t.pageCount=void 0,t.userId=t.sessionId=t.appKeyHash=this.Hc="",t.expirationAbsTimeSeconds=ct(this.N.wdx),this.Dc.setIdentity(t)},t.prototype.create=function(t){this.zc.setLastUserActivityTimeMS(t.lastUserActivityTime);var i=this.Dc.getIdentity();this.Dc.setIdentity(n(n({},i),t))},t.prototype.getOrgId=function(){return this.Dc.getIdentity().orgId},t.prototype.getUserId=function(){return this.Dc.getIdentity().userId},t.prototype.setUserId=function(t){var i=this.Dc.getIdentity();i.userId=t,this.Dc.setIdentity(i)},t.prototype.getSessionId=function(){return this.Dc.getIdentity().sessionId},t.prototype.getAppKeyHash=function(){return this.Dc.getIdentity().appKeyHash},t.prototype.getCookies=function(){return this.Ca.cookies},t.prototype.setAppId=function(t){this.Hc=t;var i=this.Dc.getIdentity();i.appKeyHash=Jc(t),this.Dc.setIdentity(i)},t.prototype.getAppId=function(){return this.Hc},t.prototype.setSessionStartTimeMS=function(t){var i=this.Dc.getIdentity();i.sessionStartTime=t,this.Dc.setIdentity(i)},t.prototype.getSessionStartTimeMS=function(){return this.Dc.getIdentity().sessionStartTime},t.prototype.getExpirationAbsTimeSeconds=function(){return this.Dc.getIdentity().expirationAbsTimeSeconds},t.prototype.setLastUserActivityTimeMS=function(t){this.zc.setLastUserActivityTimeMS(t)},t.prototype.getLastUserActivityTimeMS=function(){return this.zc.getLastUserActivityTimeMS()},t.prototype.setPageCount=function(t){var i=this.Dc.getIdentity();i.pageCount=t,this.Dc.setIdentity(i)},t.prototype.getPageCount=function(){return this.Dc.getIdentity().pageCount},t.prototype.getClientStore=function(){return this.Ca},t.prototype.setCookie=function(t,i,n){var r;void 0===n&&(r=this.N.wdx,n=new Date(Q(r)+6048e5).toUTCString()),this.Ca.setCookie(t,i,n)},t.prototype.setCookieDomain=function(t,i){var n=i;(Fi(n)||n.match(/^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$/g))&&(n="");var r=this.xa.cookieDomain;x(r)&&(n=r),this.Ca.setDomain(n)},t.prototype.setIds=function(t,i,n,r){this.setCookieDomain(t,i);var e=this.Dc.getIdentity();e.userId=n,e.sessionId=r,this.Dc.setIdentity(e)},t.prototype.setExpirationAbsTimeSeconds=function(t){var i=this.Dc.getIdentity();i.expirationAbsTimeSeconds=t,this.Dc.setIdentity(i)},t.prototype.clearAppId=function(){var t=this.Dc.getIdentity();return!!t.appKeyHash&&(this.Hc="",t.appKeyHash="",this.Dc.setIdentity(t),!0)},t}();function Ov(t,i,n,r,e){if(t&&"object"==typeof t){var s=0,u={};for(var o in t)if(!(o in e)){var h=t[o];e[o]={value:h,apiSource:i,apiVersion:n,schema:r},u[o]=h,s++}if(0!==s)return u}}function Nv(t,i,n){var r,e={},s={};for(var u in n)if(t.objectHasOwnProp(n,u)){if(i()<=0)break;var o=n[u];if(void 0!==o){var h=Lv(u),a=h[0],c=h[1],f=o,v=c;if(E(t,o))f=(r=Nv(t,i,o))[0],v=r[1];else if(Bv(t,o)){f=[],v=[];for(var l=0,d=o;l<d.length;l++){var p=Nv(t,i,d[l]),w=p[0],g=p[1];f.push(w),v.push(g)}}else"date"===c||Dv(o)?f=Hv(o):S(t,o)&&("dates"===c||o.every(Dv))&&(f=o.map(Hv));e[a]=f,v&&(s[a]=v)}}return[e,s]}function Lv(t){var i=function(t){return /^[a-zA-Z_][a-zA-Z0-9_\\-]*$/.test(t)?t:t.replace(/^[^a-zA-Z_]+|[^a-zA-Z0-9_]/g,"")||t}(t),n=i.lastIndexOf("_");if(-1===n)return[i,null];var r,e=i.substring(0,n),s=i.substring(n+1);return r=s,Fv.test(r)?[e,s]:[i,null]}(Kv=Rv||(Rv={})).TrackEvent="trackEvent",Kv.GetSession="getSession",Kv.Init="init",Kv.Log="log",Kv.Source="source",Kv.Observe="observe",Kv.Restart="restart",Kv.SetIdentity="setIdentity",Kv.SetConfig="setConfig",Kv.SetPage="setPage",Kv.SetProperties="setProperties",Kv.Shutdown="shutdown",Kv.Start="start",Kv.Stat="stat",Kv.GetFullstoryId="getFullstoryId";var Uv,Fv=/^(?:bool|date|int|obj|real|str)s?$/;function Bv(t,i){return S(t,i)&&i.length>0&&i.every(function(i){return E(t,i)})}function Hv(t){if(null!=t){var i=Dv(t)?t:new Date(t);try{return i.toISOString()}catch(i){if("string"==typeof t)return t}}return null}function Dv(t){return null!=t&&t.constructor===Date}function Wv(t,i,n,r,e,s,u){var o,h,a,c,f=1500,v=function(){return--f};return r?(a=(o=zv(t,v,n,r))[0],c=o[1]):(a=(h=Nv(t,v,n))[0],c=h[1]),f<0&&"Too many properties for [".concat(i,"] API call"),i===Nt.Document?{properties:Ov(a,e,s,c,u),schema:c}:{properties:a,schema:c}}function zv(t,i,n,r){var e={},s={};function u(t,i,n){e[t]=i,n&&(s[t]=n)}for(var o in n)if(t.objectHasOwnProp(n,o)){if(i()<=0)break;var h=n[o],a=r[o];if(E(t,h)){var c=zv(t,i,h,a||{}),f=c[0],v=c[1];e[o]=f,a&&(s[o]=v)}else C(h)?u(o,T(h),a):S(t,h)&&k(h,C)?u(o,h.map(T),a):u(o,h,a)}return[e,s]}function qv(t,i,n,r,e,s,u){var o=ds(t,n),h=e?ds(t,e):void 0,a="fs"!==r?r:void 0;switch(i){case Nt.Event:return{Kind:Y.SYS_CUSTOM,Args:[u,o,a,s,h]};case Nt.Document:case Nt.Page:case Nt.User:return{Kind:Y.SYS_SETVAR,Args:[i,o,a,s,h]};default:jr(0,"Unsupported")}}var Vv=function(){function t(t){this.qc={},this.ft=t}return t.create=function(i,n){var r=new t(i);return n&&(r.Vc=n.Vc,r.qc=n.qc),r},t.prototype.unloadCrossPageVars=function(t){switch(t){case bt.Hibernation:case bt.Size:break;default:this.Vc=void 0}},t.prototype.getCrossPageVars=function(){return this.Vc},t.prototype.getDocumentVars=function(){var t,i={};for(var r in this.qc){var e=this.qc[r],s=e.value,u=e.apiSource,o=e.schema,h=e.apiVersion;(l=null!==(t=i[c="".concat(null!=u?u:"").concat("$$$").concat(null!=h?h:"")])&&void 0!==t?t:{properties:{}}).properties[r]=s,o&&(l.schema=n(n({},l.schema),o)),i[c]=l}var a=[];for(var c in i){var f=c.split("$$$"),v=(u=f[0],f[1]),l=i[c];$v(a,qv(this.ft,Nt.Document,l.properties,""===u?void 0:u,l.schema,A(v)))}return a},t.prototype.api=function(t,i){var n,r={events:[],reidentify:!1},e=[];try{var s=function(t){switch(t.operation){case Rv.TrackEvent:return Nt.Event;case Rv.SetPage:return Nt.Page;case Rv.SetProperties:var i=t.option.type;switch(i){case Nt.Document:case Nt.Page:case Nt.User:return i;default:return jr(0,"Invalid scope: ".concat(i))}default:jr(0,"Unsupported")}}(t),u=null===(n=t.source)||void 0===n?void 0:n.integration;if(!function(t,i,n){switch(i){case Nt.Event:var r=n.option.name;return"string"==typeof r||("Custom event name invalid: ".concat(r),!1);case Nt.Document:case Nt.Page:return!0;case Nt.User:var e=n.option.properties;return"object"==typeof e||("Expected argument of type 'object' instead got type: '".concat(typeof e,"', value: ").concat(ds(t,e)),!1);default:jr(0,"Unsupported")}}(this.ft,s,t))return r;var o=function(t,i,n,r){var e=n.option.properties,s=function(t,i){var n=i.option.schema;if(2===i.apiVersion){var r=I(n)&&"properties"in n?n.properties:{};return I(r)?r:{}}}(0,n);if(i===Nt.Event)return{rawProps:e,eventName:n.option.name,rawSchema:s};if(i!==Nt.User||!("uid"in e))return{rawProps:e,rawSchema:s};var u=e.uid;if(!1===u)return delete e.uid,{rawProps:e,rawSchema:s,reidentify:!!r,nextAppId:""};var o=function(t,i,n){var r=i;if("number"==typeof r&&t.mathFloor(r)==r&&("Expected appId of type 'string' instead got value: ".concat(r," of type: ").concat(typeof r),r="".concat(r)),"string"!=typeof r)return"blocking FS.identify API call; uid value (".concat(r,") must be a string"),[void 0,Ut.FsId];var e=r.trim();if(q.indexOf(e.toLowerCase())>=0)return"blocking FS.identify API call; uid value (".concat(e,") is illegal"),[void 0,Ut.FsId];var s=Jc(e),u=void 0;return n&&n!==s&&n!==e&&("user re-identified; existing uid hash (".concat(n,") does not match provided uid (").concat(e,")"),u=Ut.NewUid),[e,u]}(t,u,r),h=o[0],a=o[1];if(h)return e.uid=h,{rawProps:e,rawSchema:s,nextAppId:h,reidentify:a===Ut.NewUid};switch(a){case Ut.FsId:case void 0:break;default:"unexpected failReason returned from setAppId: ".concat(a)}}(this.ft,s,t,i);if(!o)return r;var h=o.rawProps,a=o.rawSchema,c=o.reidentify,f=o.nextAppId,v=o.eventName,l=Wv(this.ft,s,null!=h?h:{},a,u,t.apiVersion,this.qc),d=l.properties,p=l.schema;if(!d&&s===Nt.Document)return r;var w=qv(this.ft,s,null!=d?d:{},u,p,t.apiVersion,v);return $v(e,function(t,i){var n=Gv[t];if(n)return fv({source:i,type:"api",entrypoint:n})}(s,t.source)),$v(e,w),s===Nt.Page&&(this.Vc=w),{events:e,reidentify:!!c,appId:f}}catch(i){return"unexpected exception handling ".concat(t.operation," API call: ").concat(i.message),r}},t}();function $v(t,i){i&&t.push(i)}var Gv=((Uv={})[Nt.User]="setVars",Uv[Nt.Event]="event",Uv),Qv=no||ro||tu,Xv=function(){function t(t,i){void 0===i&&(i=function(t){return new WebSocket(t)}),this.$c=i,this.Gc=!1,this.Qc=!1,this.Ft={},this.Zu=1,this.N=t,this.P=t.options.scheme,this.zh=t.options.recSettingsHost}return t.isSupported=function(){return"WebSocket"in window},t.prototype.page=function(t){var i=this;return new mr(function(n,r){i.Xc({Cmd:ni.Page,Page:t},Ps(i.N),function(t){t.Cmd===ei.Page?n(t.Page):("socket: unexpected page response: ".concat(t.Cmd),r(t.Cmd))},r)})},t.prototype.settings=function(t){var i=this.N.options.settings;if(i)return mr.resolve(i);var n=t.previewMode?Ps(this.N):this.zh;return Ms(this.N,this.P,n,t)},t.prototype.event=function(t){return this.tc({req:ni.Event,rsp:ei.Event},t)},t.prototype.bundle=function(t){return this.tc({req:ni.Bundle,rsp:ei.Bundle},t)},t.prototype.tc=function(t,i){return r(this,void 0,mr,function(){var n,r,s,u=this;return e(this,function(e){switch(e.label){case 0:return[4,Cr(this.N.wdx,this.N.window)];case 1:return e.sent(),n=i.deltaT,r=i.serverPageStart,s=i.serverBundleTime,[2,new mr(function(e,o){var h=Ps(u.N);Yv(u.N.wdx,i.recHost,h);var a=i.bundle,c=a[0],f={Bundle:Jv(a[1]),DeltaT:null===n?void 0:n,OrgId:i.orgId,PageId:i.pageId,PageStart:null==r?void 0:r,PrevBundleTime:null==s?void 0:s,Seq:c,SessionId:i.sessionId,UserId:i.userId},v=u.Xc({Cmd:t.req,Bundle:f},h,function(i){i.Cmd===t.rsp?e([null!=v?v:0,i.Bundle]):"socket: unexpected bundle response: ".concat(i.Cmd)},o)})]}})})},t.prototype.bundleBeacon=function(t){var i=Ps(this.N);return Yv(this.N.wdx,t.recHost,i),xs(this.N,this.P,i,t)},t.prototype.startBeacon=function(t){return Rs(this.N,this.P,Ps(this.N),t)},t.prototype.Xc=function(t,i,n,r){var e=t;e.Seq=this.Zu++;var s=ds(this.N.wdx,e);return this.Ft[e.Seq]={payload:s,win:n,lose:r},this.Yc(i),s.length},t.prototype.Jc=function(t){var i;try{i=U(this.N.wdx,t)}catch(t){return void"socket: error parsing frame: ".concat(t)}var n=this.Ft[i.Seq];delete this.Ft[i.Seq],n?i.Cmd===ei.Error?(i.Fail.Error,n.lose(new Mn(i.Fail.Status,i.Fail.Error))):n.win(i):"socket: mismatched request seq ".concat(i.Seq,"; ignoring")},t.prototype.Zc=function(){if(this.Qc&&this.tf)for(var t in this.Ft){var i=this.Ft[t];i.sent||(this.tf.send(i.payload),i.sent=!0)}},t.prototype["if"]=function(){for(var t in this.Ft){var i=this.Ft[t];i.sent&&(delete this.Ft[t],i.lose(new Mn(0,"Pending request")))}},t.prototype.Yc=function(t){var i=this;if(this.Qc)this.Zc();else if(!this.Gc){this.Gc=!0;var n="".concat("https:"==this.P?"wss:":"ws:","//").concat(t,"/rec/sock");this.tf=this.$c(n),this.tf.onopen=function(t){i.Gc=!1,i.Qc=!0,i.Zc()},this.tf.onmessage=function(t){i.Jc(t.data),i.Zc()},this.tf.onclose=function(t){i.Gc=i.Qc=!1,i["if"]()},this.tf.onerror=function(t){i.Gc=i.Qc=!1,i["if"]()}}},t}();function Yv(t,i,n){i&&Ws(t,n===i,"sock#recHost")}function Jv(t){if("string"===t.type)return JSON.parse(t.data);throw new Error("Unexpected bundle type: ".concat(t.type))}var Zv=function(){function t(t,i){this.N=t,this.nf=!1,this.gh=i}return t.prototype.flush=function(){this.gh(),this.nf=!1},t.prototype.schedule=function(){var t=this;this.nf||(this.nf=!0,mr.resolve().then(function(){t.flush()})["catch"](function(i){return Bs.send(t.N.wdx,"microtask#flush",{err:i})}))},t}(),tl=function(){function t(t){this.N=t,this.rf={start:[],shutdown:[],"internal/bundle":[],"internal/error":[],"internal/fs-init":[]}}return t.prototype.registerListener=function(t,i){var n;if(-1===Gf.indexOf(t)||!i)throw new Error("Invalid event type or missing callback.");var r={disconnected:!1,callback:i,count:0},e=(null!==(n=this.rf[t])&&void 0!==n?n:[]).filter(function(t){return!t.disconnected});return e.push(r),this.rf[t]=e,{disconnect:function(){r.disconnected=!0}}},t.prototype.hasListeners=function(t){var i;return(null!==(i=this.rf[t])&&void 0!==i?i:[]).length>0&&this.rf[t].some(function(t){return!t.disconnected})},t.prototype.takeRecords=function(t){var i,n=null!==(i=this.rf[t.type])&&void 0!==i?i:[];if(0!==n.length)for(var r=0,e=n;r<e.length;r++){var s=e[r];if(!(s.disconnected||s.count>0&&t.once)){s.count+=1;try{s.callback(t.data)}catch(t){"Recording observer callback error: ".concat(Uu(this.N.wdx)(t))}}}},t}(),il=function(){function t(t,i){var n=this;this.N=t,this.Rn=i,this.Ft=[],this.Sh=new Zv(this.N,function(){n.gh()})}return t.prototype.addEvent=function(t){this.Rn.hasListeners(t.type)&&(this.Ft.push(t),this.Sh.schedule())},t.prototype.gh=function(){for(var t=0,i=this.Ft;t<i.length;t++){var n=i[t];this.Rn.takeRecords(n)}this.Ft=[]},t}(),nl="",rl="https:";function el(t){if(t===top||un(t)||sn(t)||hn(t))return!0;try{return t.parent.document,el(t.parent)}catch(t){}return!1}function sl(t,i,n){var r=ah(0,i);return(xn(i,n)||!su)&&i.postMessage&&i.MutationObserver&&ul(r,function(t,i){i.disconnect()})&&ul(i.Map)&&ul(i.Set)&&ul(i.WeakMap)&&t.snapshot.status!==uh.UnrecoverableFailure?!!el(i)||!1:(function(t){try{if(t.snapshot.status===uh.UnrecoverableFailure){for(var i={},n=0;n<t.snapshot.errors.length;n++)i["error".concat(n+1)]=t.snapshot.errors[n];Xs||Bs.send(t,"windexFailure",i),t.snapshot.errors.forEach(u)}}catch(t){}}(t),!1)}function ul(t,i){try{if(t)return void 0!==i?new t(i):new t,!0}catch(t){}return!1}function ol(t,i){var n,r,e;switch(i){case"all":return t.getPageArgs();case"url.now":return null!==(n=t.getCurrentSessionURL(!0))&&void 0!==n?n:null;case"id":return null!==(r=t.getCurrentSession())&&void 0!==r?r:null;default:return null!==(e=t.getCurrentSessionURL())&&void 0!==e?e:null}}var hl=ui.DefaultStatsSettings,al=hl.MaxEventTypeLength,cl=hl.MaxPayloadLength;function fl(t,i,n){switch(i){case"getSession":case"init":case"observe":case"restart":case"setConfig":case"setIdentity":case"shutdown":case"source":case"start":case"getFullstoryId":case"trackEvent":return{operation:i,option:n};case"setPage":return{operation:i,option:{properties:s=(r=n).properties||r}};case"stat":return{operation:i,option:{event_type:(r=n).eventType||r.event_type,properties:r.payload||r.properties}};case"log":var r=n;return t.arrayIsArray(n)&&(r={level:n[0],msg:n[1]}),{operation:i,option:r};case"setVars":case"setProperties":var e=Rv.SetProperties;return r=n,t.arrayIsArray(n)&&(r={type:n[0],properties:s=n[1]}),{operation:e,option:r};case"event":return r=n,n&&"object"==typeof n&&"n"in n&&(r={name:n.n,properties:n.p}),{operation:e=Rv.TrackEvent,option:r};case"user":var s=n;return{operation:e=Rv.SetProperties,option:{type:"user",properties:s}};case"consent":var u=n;return{operation:e=Rv.SetIdentity,option:{consent:u}};case"rec":return n?{operation:Rv.Restart}:{operation:e=Rv.Shutdown};default:"Unrecognized api: ".concat(i)}}var vl="must be an object";function ll(t){return!!t&&"object"==typeof t}function dl(t,i){return i in t}function pl(t,i){var r,e={recoverable:[],unrecoverable:[]};if(!ll(i))return e.unrecoverable.push(wl("options",vl)),[e,void 0];var s={};if(dl(i,"privacy")){var u=function(t,i){var n="privacy.attributeBlocklist",r=[];if(!ll(i))return r.push(wl("privacy",vl)),[r,void 0];if(!dl(i,"attributeBlocklist"))return[r,void 0];if(!S(t,i.attributeBlocklist))return r.push(wl(n,"must be an array")),[r,void 0];for(var e={attributeBlocklist:[]},s=0,u=i.attributeBlocklist;s<u.length;s++){var o=u[s];gl(o)?e.attributeBlocklist.push(o):r.push(wl(n,"invalid rule: ".concat(Uu(t)(o))))}return[r,e]}(t,i.privacy),o=u[0],h=u[1];(r=e.recoverable).push.apply(r,o),s.privacy=h}if(dl(i,"env")){var a=i.env;ll(a)?s.env=n({},a):e.recoverable.push(wl("env",vl))}return[e,s]}function wl(t,i){return"Init Api - invalid ".concat(t," config, ").concat(i)}function gl(t){var i;if(!t||"object"!=typeof t)return!1;var n=t;return ml(n.target,["any","exclude","mask"])&&ml(n.tag)&&ml(n.action,["erase","maskText","scrubUrl","scrubCss"])&&ml(null!==(i=n.type)&&void 0!==i?i:"static",["static","prefix"])}function ml(t,i){return void 0===i&&(i=[]),"string"==typeof t&&(0===i.length||-1!==i.indexOf(t))}function yl(t,i){return i?n(n(n({},t),i),{privacy:(r=t.privacy,e=i.privacy,e?{attributeBlocklist:s(s([],null!==(u=null==r?void 0:r.attributeBlocklist)&&void 0!==u?u:[],!0),null!==(o=null==e?void 0:e.attributeBlocklist)&&void 0!==o?o:[],!0)}:r),env:n(n({},t.env),i.env)}):t;var r,e,u,o}function bl(t,i){return t("function"==typeof i?i():i)}function Sl(t,i,n,r){return void 0===r&&(r=ui.DefaultRecDisabledMessage),{commit:function(i){if(t)return bl(h(t),null!=i?i:n)},discard:function(t){if(i)return kl(h(i),null!=t?t:r)}}}function kl(t,i){if(void 0===i&&(i=ui.DefaultRecDisabledMessage),t){var n=i;try{n=new Error(i)}catch(t){}bl(h(t),n)}}function _l(t,i,n){void 0===i&&(i=!1);for(var r=0,e=t.commit;r<e.length;r++){var s=e[r];i?s.discard(n):s.commit()}for(var u=0,o=t.discard;u<o.length;u++)(s=o[u]).discard(n);t.commit=[],t.discard=[]}var Al=[Rv.GetSession,Rv.Init,Rv.Observe,Rv.SetProperties,Rv.Source,Rv.TrackEvent],Il=[Rv.Observe];function El(t,i){try{var n=t[1],r=t[3],e=void 0===r?a:r,s=n,u=s.type,o=s.callback;e(i.registerListener(u,o))}catch(t){}}function Tl(t,i,n,r,e){void 0===e&&(e=[]),e.push.apply(e,En(t,i));for(var s=0,u=e;s<u.length;s++){var o=u[s],h=o[0],a=o[4];-1!==Il.indexOf(h)?h===Rv.Observe&&El(o,n):kl(a)}r.addEvent({type:zf.SHUTDOWN,data:{reason:bt.SettingsBlocked}})}var Cl=function(t){function n(i,n){void 0===n&&(n=250);var r=t.call(this,i)||this;return r.ef=i.wdx.mathMax(0,n),r}return i(n,t),n.prototype.Li=function(){var t=this;(function(t,i,n){return r(this,void 0,mr,function(){var r;return e(this,function(e){switch(e.label){case 0:return(r=t.requestWindowIdleCallback)?[2,new mr(function(t){r(i,t,{timeout:n})})]:[4,br(t,i,n)];case 1:return e.sent(),[2,Rr(t,42)]}})})})(this.N.wdx,this.N.window,this.ef).then(function(i){i.didTimeout?Cr(t.N.wdx,t.N.window).then(function(){return t.Ni(Rr(t.N.wdx,36))}):t.Ni(i)})},n}(Eo),xl=function(t){function n(i){return t.call(this,i)||this}return i(n,t),n.prototype.Li=function(){var t=this;xr(this.N.wdx,this.N.window).then(function(){Kr(t.N.wdx,t.N.window).then(function(){return t.Ni(Rr(t.N.wdx,36))})})},n}(Eo),Rl=function(){function t(){}return t.prototype.createTopRecorder=function(t){var i;return i=t.options.useMockProtocol?new Ff(t):t.options.useSocket&&Xv.isSupported()?new Xv(t):new bs(t),new mv(t,i)},t.prototype.createInnerRecorder=function(t){return new Sv(t)},t}(),Kl=function(){function t(t,i,n,r){void 0===t&&(t=window),void 0===i&&(i=function(t){var i,n,r=Ni(t);try{return null!==(n=null===(i=Li(t))||void 0===i?void 0:i.getAttribute(Si))&&void 0!==n?n:r}catch(t){return r}}(t)),void 0===n&&(n=new Rl),void 0===r&&(r=ch.create(t)),this.vt=t,this.sf=i,this.uf=n,this.ft=r,this.hf=null,this.af=!1,this.cf=!1,this.ff=fi.Shutdown,this.vf={commit:[],discard:[]},this.lf=new Vv(this.ft);var e={wdx:this.ft,window:this.vt};this.df=new tl(e),this.pf=new il(e,this.df)}return t.prototype.init=function(){var t=this,i=this.ft,r=this.vt,e=this.sf;!function(t){G(t)}(i),ah(0,r),Ae();var s=function(){!function(t,i){i in t||(t[i]={});try{t[i][Mi]=!0}catch(t){}}(r,e),function(t,i){var r=function(t,i){var r=t.snapshot;try{if(vn(i))return n(n({},r),{status:uh.Clean});if(!i.document||r.status!==uh.Unknown)return r;var e=function(t,i){var r=i.functions,e=new Set,s={},u=n({},i.helpers);if(u.functionToString=function(t,i){var n,r,e=null===(n=t["__core-js_shared__"])||void 0===n?void 0:n.inspectSource;if(e){var s=function(){return e(this)};if(ou(s,2))return s}var u=null===(r=t["__core-js_shared__"])||void 0===r?void 0:r["native-function-to-string"];if(ou(u))return u;var o=i[cu];return ou(o)?o:ou(i)?i:void 0}(t,u.functionToString),!u.functionToString)return i;var o=!1;for(var h in r)r[h]?(s[h]=vu(u.functionToString,r[h]),s[h]||(s[h]=du(u.functionToString,u,h)),s[h]?s[h]!==r[h]&&(o=!0):(o=!0,e.add(h))):s[h]=void 0;return{status:e.size>0?uh.Mixed:uh.Clean,functions:o?s:r,helpers:u,errors:[],dirty:e}}(i,r);if(e.status===uh.Clean)return e;var s=function(t){var i=t.document.createElement("iframe");return i.id="FullStory-iframe",i.className="fs-hide",i.style.display="none",i}(i);try{(function(t){var i=t.document,n=i.documentElement,r=i.body||i.head||n||i;return function(t){return!!Gi(t,"_fs_windex_iframe","boolean")}(t)?r:n||r})(i).appendChild(s)}catch(t){return n(n({},r),{status:uh.Clean})}if(!s.contentWindow)return n(n({},r),{status:uh.Clean});var u=fh(s.contentWindow,uh.Clean);u.dirty=e.dirty;var o=m(0,s);return Qv&&o&&(s.contentWindow.close(),o.removeChild(s)),u.status===uh.UnrecoverableFailure?n(n({},r),{status:uh.Clean}):u}catch(i){return Bs.send(t,"windex#createClean",{err:i}),n(n({},r),{status:uh.Clean})}}(t,i);t.rebuildFromSnapshot(r)}(i,r),t.wf(),t.gf(),t.pf.addEvent({type:zf.INTERNAL_FS_INIT,data:{}})};!function(t,i){try{return Mi in(t[i]||{})}catch(t){return!1}}(r,e)&&function(t,i,n,r){var e,s,u,o;try{return!(!function(t){var i;try{return Boolean(null===(i=Li(t))||void 0===i?void 0:i.hasAttribute("data-fs-is-mobile"))}catch(t){return!1}}(i)&&(_(null===(e=i._fs_native_msg_handler)||void 0===e?void 0:e.webShouldCapture)?(Ui(i,n)._init_callback=h(function(){return r()}),i._fs_native_msg_handler.webShouldCapture(n,i.location.href),1):_(null===(o=null===(u=null===(s=i.webkit)||void 0===s?void 0:s.messageHandlers)||void 0===u?void 0:u._fs_native_msg_handler)||void 0===o?void 0:o.postMessage)&&(Ui(i,n)._init_callback=h(function(){return r()}),i.webkit.messageHandlers._fs_native_msg_handler.postMessage([Ai.AllowlistCheck,i.location.href,n]),1)))}catch(r){return Bs.send(t,"webviewRecording#shouldInitWebviewRecordingIfWebView",{err:r,namespace:n,location:i.location.href}),!0}}(i,r,e,s)&&s()},t.prototype.mf=function(t){var i,r,e,s;this.$h=function(t,i,n){var r=n;try{i.filter(function(t){return t[0]===Rv.Init}).forEach(function(i){i[0];var n=i[1];if(n){var e=pl(t,n),s=e[0],u=e[1];0===s.unrecoverable.length&&u&&(r=yl(null!=r?r:{},u))}})}catch(t){}return r}(this.ft,t,this.$h),this.hf=function(t,i,r){var e,s,u=null!==(e=null==r?void 0:r.env)&&void 0!==e?e:{},o=function(t,i){var r,e=t.host||nl;return n(n({},t),{appHost:t.appHost||Kn(e)||nl,captureOnStartup:null===(r=t.captureOnStartup)||void 0===r||r,namespace:i,recHost:t.recHost||Hi(e)||nl,recSettingsHost:t.recSettingsHost||Di(e)||nl,scheme:t.scheme||rl,script:t.script||jn(e),tabId:t.tabId})}(u.isolated?u:n(n({},function(t,i){return{appHost:Ji(t)||nl,assetMapId:nn(t),beacon:pn(t),captureOnStartup:tn(t),cleanValueGetter:_n(t),clientStore:gn(t),cookieDomain:rn(t),disableResume:Sn(t),forceLocalResources:ln(t),identityStore:mn(t),isOuterScript:un(t),isWayfinder:bn(t),multiStorage:yn(t),namespace:i,networkBudget:An(t),orgId:Zi(t)||nl,pageStart:an(t),preHooks:In(t),ready:en(t),recHost:Yi(t)||nl,recSettingsHost:Xi(t)||nl,replayFlags:on(t),request:dn(t),runInIframe:sn(t),scheme:rl,script:Qi(t),settings:wn(t),skipIframeInjection:kn(t),snippetVersion:Cn(t,i),transport:hn(t),useMockProtocol:fn(t),useSocket:cn(t)}}(t,i)),u),i);return o.orgId&&-1===o.orgId.indexOf("FULLSTORY_KEY")&&(o.script||o.skipIframeInjection)&&o.recHost&&o.recSettingsHost&&o.appHost?("localhost:8080"===o.recHost&&(o.scheme="http:"),"\n    captureOnStartup: ".concat(o.captureOnStartup,"\n    cdn host: ").concat(o.recSettingsHost,"\n    disableResume: ").concat(o.disableResume,"\n    orgId: ").concat(o.orgId,"\n    recording host: ").concat(o.recHost,"\n    script: ").concat(null!==(s=o.script)&&void 0!==s?s:"","\n  "),o):null}(this.vt,this.sf,this.$h),this.hf&&(this.yf=(i=this.hf,r=this.vt,s=i.transport,i.isOuterScript?e=!1:r!==top?e=!0:s?i.orgId&&s.init&&s.init(i.orgId)&&(e=!0):e=!1,e),this.cf=xn(this.vt,this.sf),this.yf)},t.prototype.getCurrentSessionURL=function(t){return this.bf(t?"url.now":"url")},t.prototype.getCurrentSession=function(){return this.bf("id")},t.prototype.getFullstoryId=function(t){return Ke(t)},t.prototype.bf=function(t){return this.Sf?ol(this.Sf,t):null},t.prototype.enableConsole=function(){this._api(Rv.SetConfig,{console:!0})},t.prototype.disableConsole=function(){this._api(Rv.SetConfig,{console:!1})},t.prototype.restart=function(){this._api(Rv.Restart)},t.prototype.shutdown=function(){this._api(Rv.Shutdown)},t.prototype.log=function(){for(var t=[],i=0;i<arguments.length;i++)t[i]=arguments[i];this._api(Rv.Log,t)},t.prototype.kf=function(t){void 0===t&&(t=bt.Api),this.ff=fi.Shutdown,this.Sf&&!this._f&&(this.Sf.shutdown(t),this.Sf=null,this.yf||this.lf.unloadCrossPageVars(t),this.pf.addEvent({type:zf.SHUTDOWN,data:{reason:t}}))},t.prototype.Af=function(t){if(void 0===t&&(t=!1),this.ff!==fi.Fatal){if(this._f)return this._f(),void(this._f=null);if(this.Sf){if(!t)return;this.kf(bt.FsShutdownFrame)}this.Ef(!0)}},t.prototype.qu=function(t,i){var n;return r(this,void 0,mr,function(){return e(this,function(r){switch(r.label){case 0:return i&&null==this.vc?[2]:this.ff===fi.Starting?(this.Tf=[t,i],[2]):(this.kf(t),[4,br(this.ft,this.vt,0)]);case 1:return r.sent(),[4,br(this.ft,this.vt,0)];case 2:return r.sent(),i&&this.vc&&Ml(this.vc),t===bt.Hibernation&&(null===(n=this.hf)||void 0===n?void 0:n.disableResume)||this.Af(),[2]}})})},t.prototype._api=function(t,i,n,r,e,s){if(this.ff!==fi.Fatal){var u,o="start"===t||"restart"===t,h=[t,i,n,r||function(t){u=t},e,s];if(!e||this.ff!==fi.Shutdown||o)return!this.Sf||this.Tf?(Tn(this.vt,h,this.sf),void(o&&this.Af())):(this.Cf(h),u);kl(e,this.hf?ui.ShutdownMessage:ui.DefaultRecDisabledMessage)}else kl(e)},t.prototype.Cf=function(t){var i;if(this.Sf){var n=null!==(i=this.xf(this.Sf,[t]))&&void 0!==i?i:{reidentified:!1,applyApi:function(){}},r=n.applyApi,e=n.sideEffects,s=n.initConfig,u=n.observingStart,o=n.recordingShouldBeEnabled,h=n.reidentified,a=n.nextAppId;jl(this.vc,h,a),h?(Tn(this.vt,t,this.sf),this.qu(bt.Reidentify,!0)):this.Rf(this.Sf,{applyApi:r,sideEffects:e,initConfig:s,observingStart:u,recordingShouldBeEnabled:o}),void 0!==o&&(o?this.Af():this.kf(bt.Api))}},t.prototype.xf=function(t,i){var n;return function(t,i){for(var n,r,e,s,u,o,h,a,c,f,v,l=i.appHashKey,d=i.inFrame,p=i.initConfig,w=i.inWebView,g=i.recorder,m=i.recordingObserver,y=i.state,b=i.vars,S=i.windex,k=function(t,i){for(var n=[],r=0,e=i;r<e.length;r++){var s=e[r],u=s[0],o=s[1],h=s[2],a=s[3],c=s[4],f=s[5],v=fl(t,u,o);v&&(v.source="string"==typeof h?{integration:h}:h,v.resolve=a,v.reject=c,v.apiVersion=2===f?2:1,n.push(v))}return n}(S,t),_=[],A=!1,E=!1,T=[],C=[],x=function(t){if(b){var i=function(t,i){switch(i){case"":return"";case void 0:return t;default:return Jc(i)}}(l,f),n=b.api(t,i),r=n.events,e=n.reidentify,s=n.appId;e&&(_=[],A=!0,C.push.apply(C,T),T=[]),void 0!==s&&(f=s),_.push.apply(_,r)}},R=function(t,i){!1!==o||-1!==Il.indexOf(t.operation)?T.push(Sl(t.resolve,t.reject,i)):C.push(Sl(t.resolve,t.reject,i))},K=function(t,i){C.push(Sl(t.resolve,t.reject,void 0,i))},j=function(t){try{if("Processing api: ".concat(t.operation),d&&!function(t,i){if(-1===Al.indexOf(t.operation))return!1;if(t.operation===Rv.SetProperties)switch(t.option.type){case Nt.Document:return!0;case Nt.User:return i&&!function(t){try{var i=t.option.properties;if(I(i)&&("uid"in i))return!0}catch(t){}return!1}(t);case Nt.Page:return i;default:return!0}return!0}(t,w)){var i="FS.".concat(t.operation,"() is not available in an inner frame");return E||(E=!0),K(t,i),"continue"}switch(t.operation){case Rv.Restart:case Rv.Start:o=!0,R(t);break;case Rv.Shutdown:R(t),o=!1;break;case Rv.Log:var f=t.option,l=f.level,b=(i=f.msg,g.console().logEvent(null!=l?l:"log",i?[i]:[],"fs"));b&&_.push(b),R(t);break;case Rv.SetIdentity:var k=function(i){x({operation:Rv.SetProperties,option:i,source:t.source,resolve:t.resolve,reject:t.reject,apiVersion:t.apiVersion})};t.option&&"object"==typeof t.option?(Object.prototype.hasOwnProperty.call(t.option,"consent")&&(c=null!==(n=t.option.consent)&&void 0!==n&&n),Object.prototype.hasOwnProperty.call(t.option,"uid")&&k({type:"user",properties:{uid:t.option.uid}}),Object.prototype.hasOwnProperty.call(t.option,"properties")&&k({type:"user",properties:null!==(r=t.option.properties)&&void 0!==r?r:{},schema:null!==(e=t.option.schema)&&void 0!==e?e:{}}),(null===(s=t.option)||void 0===s?void 0:s.anonymous)&&k({type:"user",properties:{uid:!1}})):k({type:"user",properties:{uid:t.option}}),R(t);break;case Rv.TrackEvent:case Rv.SetPage:case Rv.SetProperties:x(t),R(t);break;case Rv.GetSession:R(t,function(){var i;return ol(g,null===(i=t.option)||void 0===i?void 0:i.format)});break;case Rv.SetConfig:var A=t.option.console;h=A,R(t);break;case Rv.Stat:var T=t.option,C=T.event_type,j=T.properties;_.push(function(t,i,n){var r="string"==typeof i?i.trim().slice(0,al):"";return{Kind:Y.STATS,Args:[r,es(t,Uu(t)(n,cl))]}}(S,C,j)),R(t);break;case Rv.Observe:var M=t.option,P=M.type,O=M.callback;P===zf.START&&(a=!0),R(t,null==m?void 0:m.registerListener(P,O));break;case Rv.Source:var N=fv({source:t.option.source,type:"default",entrypoint:"source"});_.push(N),R(t);break;case Rv.Init:if(y!==fi.Shutdown){K(t,i="Capture already started - init rejected.");break}var L=pl(S,t.option),U=L[0],F=L[1];if(U.unrecoverable.length>0||!F){i="Init config rejected: ".concat(U.unrecoverable.join(",\n")),K(t,i);break}U.recoverable.length>0&&(i="Init config partially rejected: ".concat(U.recoverable.join(",\n"))),v=yl(null!==(u=null!=v?v:p)&&void 0!==u?u:{},F),R(t);break;case Rv.GetFullstoryId:var B=t.option;R(t,function(){return Ke(B)});break;default:jr(0,"invalid operation")}}catch(n){i="unknown error evaluating API",Bs.send(S,i,{err:n,op:t.operation}),"".concat(i," ").concat(n),K(t,i)}},M=0,P=k;M<P.length;M++)j(P[M]);return{applyApi:function(){for(var t=g.queue(),i=0,n=_;i<n.length;i++){var r=n[i];t.enqueue(r)}void 0!==c&&g.setConsent(c),void 0!==v&&g.setInitConfig(v),"boolean"==typeof h&&(h?g.console().start({ConsoleWatcher:!0}):g.console().stop())},initConfig:v,recordingShouldBeEnabled:o,reidentified:A,nextAppId:f,observingStart:a,sideEffects:{commit:T,discard:C}}}(i,{appHashKey:null===(n=this.vc)||void 0===n?void 0:n.getAppKeyHash(),inFrame:this.yf,inWebView:this.cf,initConfig:this.$h,recorder:t,recordingObserver:this.df,state:this.ff,vars:this.lf,windex:this.ft})},t.prototype.Rf=function(t,i){var n,r,e=i.applyApi,s=i.sideEffects,u=i.initConfig,o=i.observingStart,h=i.recordingShouldBeEnabled;e();var a=!1===h,c=t.getIsSessionReady();if(a)_l(s,!1,ui.ShutdownMessage);else if(c){_l(s);var f=t.getCurrentSessionURL();if(o&&f){var v=t.orgSettings();this.pf.addEvent({type:zf.START,data:{sessionUrl:f,settings:v},once:!0})}}else(n=this.vf.commit).push.apply(n,s.commit),(r=this.vf.discard).push.apply(r,s.discard);void 0!==u&&(this.$h=u)},t.prototype._cookies=function(){return this.vc?this.vc.getCookies():null},t.prototype._setCookie=function(t,i){this.vc&&this.vc.setCookie(t,i)},t.prototype._withEventQueue=function(t,i){if(this.Sf){var n=this.Sf.queue(),r=this.Sf.pageSignature();null!=n&&null!=r&&(t===r?i(n):Uu(this.ft)({msg:"Error in _withEventQueue: Page Signature mismatch",pageSignature:r,callerSignature:t},1024))}},t.prototype._withRecorder=function(t,i){if(this.Sf){var n=this.Sf.pageSignature();null!==n&&(void 0===t||t===n?i(this.Sf):Uu(this.ft)({msg:"Error in _withRecorder: Page Signature mismatch",pageSignature:n,callerSignature:t},1024))}},t.prototype.wf=function(){var t=Ui(this.vt,this.sf);if(t)for(var i=0,n=["disableConsole","enableConsole","getCurrentSession","getCurrentSessionURL","getFullstoryId","log","restart","shutdown","_api","_withEventQueue","_withRecorder","_cookies","_setCookie"];i<n.length;i++){var r=n[i];t[r]=N(this[r],this,O)}},t.prototype.gf=function(){var t=this;if("script version ".concat(ys.Ver," (compiled at ").concat(ys.TS,")"),!sl(this.ft,this.vt,this.sf))return this.ff=fi.Fatal,void Tl(this.vt,this.sf,this.df,this.pf);this.Ef(!1),this.Kf(),this.vt.addEventListener("message",Bs.wrap(this.ft,function(i){return t.jf(i)},"wnd#messageListener"))},t.prototype.Mf=function(){var t,i=this;(null===(t=this.hf)||void 0===t?void 0:t.orgId)&&(this.vc=new Pv({wdx:this.ft,window:this.vt},this.hf,function(t){for(var n,r=0,e=t;r<e.length;r++){var s=e[r];null===(n=i.Sf)||void 0===n||n.queue().enqueue({Kind:Y.STORAGE_WRITE_FAILURE,Args:s})}},this.yf))},t.prototype.jf=function(t){if(x(t.data)){var i=this.hf;if(i&&(t.source===this.vt.parent||t.source===this.vt)){var n=Tf({wdx:this.ft,window:this.vt,options:i},t.data);switch(n[0]){case gi.ShutdownFrame:this.kf(bt.FsShutdownFrame);break;case gi.RestartFrame:this.Af(n[1])}}}},t.prototype.Pf=function(t,i){var r,e=this,s=this.ft,u={wdx:s,window:this.vt},o=s.mathRound(null!==(r=h(function(){var t;return null===(t=e.vt.performance)||void 0===t?void 0:t.now()})())&&void 0!==r?r:-1);return{window:this.vt,wdx:s,time:new qu(s),measurer:new xl(u),taskQueue:new Cl(u,2e3),options:n({},this.hf),recording:{bundleApiVersion:function(){var i,n;return null!==(n=null===(i=t())||void 0===i?void 0:i.getBundleApiVersion())&&void 0!==n?n:"v1"},bundleUploadInterval:function(){var i,n;return null!==(n=null===(i=t())||void 0===i?void 0:i.bundleUploadInterval())&&void 0!==n?n:ui.DefaultBundleUploadInterval},heartbeatInterval:function(){var i,n;return null!==(n=null===(i=t())||void 0===i?void 0:i.heartbeatInterval())&&void 0!==n?n:ui.HeartbeatInterval},flags:function(){var i,n;return(null===(n=null===(i=t())||void 0===i?void 0:i.getPageResponse())||void 0===n?void 0:n.Flags)||{}},getCurrentSessionURL:function(i){var n,r;return null!==(r=null===(n=t())||void 0===n?void 0:n.getCurrentSessionURL(i))&&void 0!==r?r:null},identity:this.vc,inFrame:i,inWebView:this.cf,observer:this.pf,pageResponse:function(){var i;return null===(i=t())||void 0===i?void 0:i.getPageResponse()},pageSignature:function(){var i,n;return null!==(n=null===(i=t())||void 0===i?void 0:i.pageSignature())&&void 0!==n?n:null},preroll:o,splitPage:function(t){return e.qu(t,!1)}},queue:function(){var i;return null===(i=t())||void 0===i?void 0:i.queue()}}},t.prototype.Of=function(t,i){if(i){for(var n=0,r=this.lf.getDocumentVars();n<r.length;n++){var e=r[n];t.enqueue(e)}var s=this.lf.getCrossPageVars();void 0!==s&&t.enqueue(s)}},t.prototype.Gh=function(t,i){var n=this;return function(r){var e=r.sessionUrl,s=r.settings;n.ff=fi.Started,i&&t.tellAllFramesTo([gi.RestartFrame]),n.Tf&&(n.qu(n.Tf[0],n.Tf[1]),n.Tf=null),n.pf.addEvent({type:zf.START,data:{sessionUrl:e,settings:s}}),_l(n.vf)}},t.prototype.Qh=function(){var t=this;return function(){t.ff=fi.Fatal,t.pf.addEvent({type:zf.SHUTDOWN,data:{reason:bt.SettingsBlocked}}),_l(t.vf,!0)}},t.prototype.Nf=function(t,i,n){void 0===n&&(n=!1),this.ff=fi.Starting,t.start(n,this.Gh(t,i),this.Qh())},t.prototype.Ef=function(t){var i=[];if(i.push.apply(i,En(this.vt,this.sf)),this.mf(i),this.hf){var n;this.Mf();var r=this.yf,e=this.Pf(function(){return n},!!r);n=r?this.uf.createInnerRecorder(e):this.uf.createTopRecorder(e),this.Lf(e,n,i,t),this.Sf=n}else Tl(this.vt,this.sf,this.df,this.pf,i)},t.prototype.Lf=function(t,i,n,r){var e,s,u,o=this;void 0===r&&(r=!1);var h,a=!1,c=t.options.assetMapId;c&&n.unshift([Ki.Vars,[Nt.Document,{assetMapId:c}]]),this.yf||(this.Of(i.queue(),r),i.queue().enqueue({Kind:Y.REC_FEAT_SUPPORTED,Args:[Rt.CaptureOnStartupEnabled,!1===(null===(e=this.hf)||void 0===e?void 0:e.captureOnStartup)]}));var f=null!==(s=this.xf(i,n))&&void 0!==s?s:{applyApi:function(){}},v=f.applyApi,l=f.sideEffects,d=f.initConfig,p=f.recordingShouldBeEnabled,w=f.reidentified,g=f.nextAppId,m=f.observingStart;this.yf?(a=!1,h=!1):(void 0!==p?a=!p:!1===(null===(u=this.hf)||void 0===u?void 0:u.captureOnStartup)&&(a=!0),this.hf&&(this.hf.captureOnStartup=!0),h=!!w),jl(this.vc,h,g),this.Rf(i,{applyApi:v,sideEffects:l,initConfig:d,observingStart:m,recordingShouldBeEnabled:p}),a?this._f=function(){return o.Nf(i,r,h)}:this.Nf(i,r,h)},t.prototype.Kf=function(){var t=this,i=function(){t.af||(t.af=!0,t.Sf&&t.Sf.onDomLoad())},n=!1,r=function(){n||(n=!0,t.Sf&&t.Sf.onLoad())};switch(document.readyState){case"interactive":i();break;case"complete":i(),r()}this.af||document.addEventListener("DOMContentLoaded",Bs.wrap(this.ft,i,"doc#domLoadedListener")),n||this.vt.addEventListener("load",Bs.wrap(this.ft,function(t){i(),r()},"wnd#loadListener"))},t}();function jl(t,i,n){t&&(i?Ml(t,n):""===n?t.clearAppId():void 0!==n&&t.setAppId(n))}function Ml(t,i){t.clear(),i&&t.setAppId(i)}!function(){try{new Kl().init()}catch(t){try{Bs.send(ch.create(window),"Conductor#init",{err:t})}catch(t){}"Failed to initialize FullStory. ".concat(t)}}()}();