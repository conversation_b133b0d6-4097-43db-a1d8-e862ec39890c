/* Use of this pixel is subject to the Amazon ad specs and policies at http://www.amazon.com/b/?&node=7253015011. Version number: 6, Changeset: Adding in phone number support for setUserData */
!function(){"use strict";const e=1e3,t=86400*e;var n={NAME_MAX_LENGTH:256,EVENT_PARAMETER_MAX_VALUE_LENGTH:1e3,EVENT_NAME_EXCEEDED_MAX_LENGTH_WARNING:"Length of event's name is longer than 256 characters.",EVENT_PARAMETER_NAME_EXCEEDED_MAX_LENGTH_WARNING:"Length of event's parameter name exceeds 256 characters.",EVENT_PARAMETER_VALUE_EXCEEDED_MAX_LENGTH_WARNING:"Length of event's parameter value exceeds 1000 characters.",EVENT_PARAMETER_KEY_EXCEEDED_MAX_LENGTH_WARNING:"Length of event's parameter key exceeds 256 characters.",AMZN_TOKEN_COOKIE_NAME:"aatToken",AMZN_TOKEN_URL_QUERY_PARAM_NAME:"amznToken",NO_CONSENT_COOKIE_NAME:"AMZN-NoCookieConsent",MT_LP_QUERY_PARAM:"aref",MTS_EVENT_ATTRIBUTE:"arefs",MEASUREMENT_TOKEN_COOKIE_NAME:"amznAref",MS_IN_SEC:e,MS_IN_HOUR:3600*e,MS_IN_DAY:t,MEASUREMENT_TOKEN_TTL_IN_MS:30*t,NUM_MAX_MEASUREMENT_TOKENS:147,MEASUREMENT_TOKEN_TIMESTAMP_PAIR_DELIMITER:"|",MEASUREMENT_TIMESTAMP_SEPARATOR:".",MT_TS_PAIR_TS_INDEX:1,NEWEST_MEASUREMENT_TOKEN_INDEX:0,MEASUREMENT_TOKEN_FULLY_ENABLED_REGIONS:["NA"],ARBITRARY_PAST_UNIX_TIMESTAMP:1,AMAZON_CONSENT_AIPES_ENABLED:!1,AIPES_AMAZON_CONSENT_STRING_FIELD_NAME:"amazonConsentString",AIPES_AMAZON_CONSENT_GEO_FIELD_NAME:"geo",AIPES_AMAZON_CONSENT_CONSENT_FIELD_NAME:"consent",AIPES_AMAZON_CONSENT_AMAZON_CONSENT_FIELD_NAME:"amazonConsentFormat",AIPES_AMAZON_CONSENT_AD_STORAGE_FIELD_NAME:"amzn_ad_storage",AIPES_AMAZON_CONSENT_USER_DATA_FIELD_NAME:"amzn_user_data",AIPES_AMAZON_CONSENT_GPP_FIELD_NAME:"gpp",AIPES_AMAZON_CONSENT_TCF_FIELD_NAME:"tcf",AIPES_AMAZON_CONSENT_COUNTRY_CODE_FIELD_NAME:"countryCode",AIPES_AMAZON_CONSENT_IP_ADDRESS_FIELD_NAME:"ipAddress",REPORTING_ATTRIBUTES:["brand","category","productid","attr1","attr2","attr3","attr4","attr5","attr6","attr7","attr8","attr9","attr10"],REPORTING_ATTRIBUTE_MAX_LENGTH:256,REPORTING_ATTRIBUTE_NOT_ALLOWED_CHARACTERS_REGEX:/[^a-zA-Z0-9_]/,REPORTING_ATTRIBUTE_SPACE_REPLACEMENT_REGEX:/[^a-zA-Z0-9]+/g,REPORTING_ATTRIBUTE_MAX_LENGTH_WARNING:"Length of attribute must be between 1 and 256 characters.",REPORTING_ATTRIBUTE_PROHIBITED_CHARACTERS_WARNING:"Attribute may only contain letters, numbers, and the underscore character.",REPORTING_ATTRIBUTE_VALUE_NOT_STRING_WARNING:"Attribute must be a string and cannot be an object or array.",CHROME_EXTENSION_ID:"lfljgabnenicfhcbbfflijkeoebncchk",GDPR_VALUES:["gdpr","gdpr_pd","gdpr_consent"],CONSENT_COOKIE_NAME:"amzn_consent",CONSENT_COOKIE_TTL_IN_MS:24192e5};const o=n,r=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i;var a={isEmpty:function(e){return null==e||""===e||0===Object.keys(e).length||0===e.length},isValidAdTag:function(e){return e&&r.test(e)},measurementTokenTimestampPairsNestedArrToMeasurementTokenCookie:function(e){return e.map((e=>e.join(o.MEASUREMENT_TIMESTAMP_SEPARATOR))).join(o.MEASUREMENT_TOKEN_TIMESTAMP_PAIR_DELIMITER)},measurementTokenCookieToMeasurementTokenTimestampPairsNestedArr:function(e){return e?e.split(o.MEASUREMENT_TOKEN_TIMESTAMP_PAIR_DELIMITER).map((e=>{const[t,n]=e.split(o.MEASUREMENT_TIMESTAMP_SEPARATOR);return[t,Number(n)]})):[]}};const s=n;function i(){}i.prototype.isCookiePresent=function(e){return document.cookie.split(";").some((t=>t.trim().startsWith(`${e}=`)))&&!i.prototype.isCookieMarkedForDeletion(e)},i.prototype.isCookieMarkedForDeletion=function(e){return"0"===i.prototype.getCookieValue(e)},i.prototype.getCookieValue=function(e){return document.cookie.split(";").find((t=>t.trim().startsWith(`${e}=`)))?.split("=")[1]},i.prototype.writeCookie=function(e,t,n){const o=new Date(n).toUTCString();document.cookie=`${e}=${t}; expires=${o}; SameSite=Strict; secure=true;`},i.prototype.deleteCookie=function(e){i.prototype.writeCookie(e,"0",s.ARBITRARY_PAST_UNIX_TIMESTAMP)};var E=i;const c=n,_=E,{isEmpty:A,isValidAdTag:d}=a;function T(e,t,n){e.searchParams.set(t,n);const o=Array.from(e.searchParams.entries());e.search="",o.forEach((n=>{n[0]!==t&&e.searchParams.set(n[0],n[1])})),e.search+=`&${t}=${n}`}var N={createQueryParams:function(e,{tagId:t,eventName:n,eventAttributes:o,consent:r,uuid:a,amazonConsent:s,region:i}){const E="string"==typeof e?new URL(e):e;if(A(t))throw Error("Tag ID is a required parameter.");if(!d(t))throw Error("Invalid ad tag provided.");if(E.searchParams.set("pid",t),A(n))throw Error("Event name is a required parameter.");if(E.searchParams.set("event",n),Array.isArray(o)&&T(E,"items",encodeURI(JSON.stringify(o))),o&&Object.entries(o).forEach((([e,t])=>{c.GDPR_VALUES.includes(e)||(t||""===t)&&("object"==typeof t?E.searchParams.set(e,JSON.stringify(t)):E.searchParams.set(e,t))})),r&&c.GDPR_VALUES.forEach((e=>{r[e]&&E.searchParams.set(e,r[e])})),s&&E.searchParams.set("consentString",JSON.stringify(s)),_.prototype.isCookiePresent.call({},c.AMZN_TOKEN_COOKIE_NAME)){const e=_.prototype.getCookieValue.call({},c.AMZN_TOKEN_COOKIE_NAME);e&&T(E,c.AMZN_TOKEN_URL_QUERY_PARAM_NAME,e)}const N=-1!==c.MEASUREMENT_TOKEN_FULLY_ENABLED_REGIONS.indexOf(i)?_.prototype.getCookieValue.call({},c.MEASUREMENT_TOKEN_COOKIE_NAME):void 0;return N&&E.searchParams.set(c.MTS_EVENT_ATTRIBUTE,N),E.searchParams.set("eventSource","amzn.js"),a&&E.searchParams.set("uuid",a),E}};const{createQueryParams:u}=N,l={"Google Chrome":106,Chromium:106},p="paa-reporting-advertising.amazon";function h(e){const t="/assets/conversion_module.js";switch(e.toLowerCase()){case"beta":case"test":return`https://beta-ara.${p}${t}`;case"gamma":return`https://gamma-ara.${p}${t}`;default:return`https://ara.${p}${t}`}}function m(){const{userAgentData:e}=navigator;return null!=e&&function(e){return e.some((({brand:e,version:t})=>l[e]<=t))}(e.brands)}async function g(e,t){try{const n=h(t);if(e&&n){const t=await e.json();return t&&0!==Object.keys(t).length&&"healthy"!==t?(await(sharedStorage?.createWorklet(n,{dataOrigin:"script-origin"}))).run("conversion-module",{data:t,privateAggregationConfig:{contextId:t?.debugKey}}):Promise.resolve(!1)}}catch{}return Promise.resolve(!1)}var f={performTriggerRegistration:async function(e){const{stage:t}=e;if(!m()||!(document?.featurePolicy?.allowsFeature?.("attribution-reporting")&&document?.featurePolicy?.allowsFeature?.("private-aggregation")&&document?.featurePolicy?.allowsFeature?.("shared-storage")))return Promise.resolve(!1);const n=function(e){let t;switch(e.toLowerCase()){case"beta":case"test":t=`https://beta-ara.${p}/aat`;break;case"gamma":t=`https://gamma-ara.${p}/aat`;break;default:t=`https://ara.${p}/aat`}return new URL(t)}(t),o=u(n,e),r=await async function(e){const t={credentials:"same-origin",keepalive:!0,attributionReporting:{eventSourceEligible:!1,triggerEligible:!0}};let n;try{n=await(window?.fetch(e,t))}catch{}return n}(o.href);return g(r,t),r},generateUUID:function(){return typeof crypto<"u"&&"function"==typeof crypto.randomUUID?crypto.randomUUID():""},generateWorkletUrl:h,runSharedStorageWorklet:g};const M=n;function R(e,t,n){e.length>M.NAME_MAX_LENGTH&&t.push(`${n}, ${e}`)}var O={validateLength:R,validateEventAttributesAsArray:function(e,t){e.forEach((function(e){R(Object.keys(e)[0],t,M.EVENT_PARAMETER_NAME_EXCEEDED_MAX_LENGTH_WARNING),R(e[Object.keys(e)[0]],t,M.EVENT_PARAMETER_VALUE_EXCEEDED_MAX_LENGTH_WARNING)}))},filterGdprValuesOut:function(e){return!M.GDPR_VALUES.includes(e)},validateCustomAttributesForReporting:function(e,t){let n;const o=t[e];let r=o;const a=e.toLowerCase();return M.REPORTING_ATTRIBUTES.includes(a)&&(n={},n[a]={messages:[]},(o.length>M.REPORTING_ATTRIBUTE_MAX_LENGTH||o.length<1)&&n[a].messages.push(M.REPORTING_ATTRIBUTE_MAX_LENGTH_WARNING),"string"==typeof o?(r=r.trim(),r=o.replaceAll(M.REPORTING_ATTRIBUTE_SPACE_REPLACEMENT_REGEX,"_").trim(),M.REPORTING_ATTRIBUTE_NOT_ALLOWED_CHARACTERS_REGEX.test(r)&&n[a].messages.push(M.REPORTING_ATTRIBUTE_PROHIBITED_CHARACTERS_WARNING)):n[a].messages.push(M.REPORTING_ATTRIBUTE_VALUE_NOT_STRING_WARNING),0===n[a].messages.length?n=void 0:(n[a].value=r,r=void 0)),{eventReportingAttributeWarnings:n,newAttributeValue:r}},validateAttributeValueAsObject:function(e,t){if("object"==typeof e&&Object.keys(e)&&Object.keys(e)[0])return Object.keys(e).forEach((function(n){n.length>M.NAME_MAX_LENGTH&&t.push(`${M.EVENT_PARAMETER_KEY_EXCEEDED_MAX_LENGTH_WARNING}, ${n}`),e[n].length>M.EVENT_PARAMETER_MAX_VALUE_LENGTH&&t.push(`${M.EVENT_PARAMETER_VALUE_EXCEEDED_MAX_LENGTH_WARNING}, ${e[n]}`)})),encodeURI(JSON.stringify(e))},validateAttributeValueAsString:function(e,t){"string"==typeof e&&e.length>M.EVENT_PARAMETER_MAX_VALUE_LENGTH&&t.push(`${M.EVENT_PARAMETER_VALUE_EXCEEDED_MAX_LENGTH_WARNING}, ${e}`)}};const I=n,{isEmpty:C,isValidAdTag:S}=a,{createQueryParams:y}=N,{generateUUID:v}=f,{validateLength:P,validateEventAttributesAsArray:k,filterGdprValuesOut:w,validateCustomAttributesForReporting:L,validateAttributeValueAsString:U,validateAttributeValueAsObject:D}=O;function b(e,t){this.endpoints=e,this.region="NA",this.stage="PROD",this.tagIdsByLabels={},this.TCFv2={},this.consentHandler=t}function G(e,t,n){const o=document.createElement("iframe");o.style.display="none",o.setAttribute("src",e),o.setAttribute("id",`tag_fire_${t}_${n}_${Date.now()}`),document.body.appendChild(o)}b.prototype.addTag=function({tagId:e,tagLabel:t}){if(C(e))return void console.warn("Tag id is required for addTag command");if(!S(e))return void console.warn(`Invalid tag id provided: ${e}`);const n=C(t)?e:t;this.tagIdsByLabels[n]=e},b.prototype.getTagIds=function(e){const{tagIdsByLabels:t}=this;return e.map((function(e){return t[e]||e}))},b.prototype.trackEvent=async function(e,t,n){return this.trackEventWithTags(e,t,n,Object.keys(this.tagIdsByLabels))},b.prototype.trackRequest=async function(e,t,n,o){const r=v();let a={...this.TCFv2[e]};const s=this.consentHandler.getTcfString();this.consentHandler.isConsentSet()&&s&&(void 0===a.gdpr_consent||""===a.gdpr_consent)&&(a={gdpr:1,gdpr_pd:1,gdpr_consent:s});try{await async function({eventIngestionEndpoint:e,consent:t,eventAttributes:n,eventName:o,tagId:r,uuid:a,consentHandler:s,region:i}){const E=[],c={},_=JSON.parse(JSON.stringify(n));if(P(o,E,I.EVENT_NAME_EXCEEDED_MAX_LENGTH_WARNING),Array.isArray(n)?k(n,E):n&&Object.keys(n).filter(w).forEach((function(e){let t;P(e,E,I.EVENT_PARAMETER_NAME_EXCEEDED_MAX_LENGTH_WARNING),t=n[e];const{newAttributeValue:o,eventReportingAttributeWarnings:r}=L(e,_);if(r&&(c[e]=r[e.toLowerCase()]),o?_[e]=o:void 0===o&&delete _[e],t){U(t,E);const e=D(t,E);e&&(t=e)}else console.warn(`Key ${e} has no value`)})),E.length>0){const e=`Event has ${E.length} validation errors.`;return console.warn(e),console.warn(E),Promise.reject(e)}const A=y(new URL(e),{tagId:r,eventName:o,eventAttributes:_,consent:t,uuid:a,region:i,amazonConsent:s.getFormattedAmazonConsent(!1)}).href;if(Object.keys(c).length>0&&void 0!==window.chrome&&void 0!==window.chrome.runtime)try{window.chrome.runtime.sendMessage(I.CHROME_EXTENSION_ID,{adTagEventWarnings:!0,eventUrl:A,attributeWarnings:c})}catch{}if(!window.fetch||typeof window.fetch>"u")return G(A,r,o);try{const e=await fetch(A,{method:"get",credentials:"include",mode:"no-cors",keepalive:!0});return Promise.resolve(e)}catch{return console.warn("Event request via fetch failed, reverting to iframe"),G(A,r,o)}}({eventIngestionEndpoint:t,consent:a,eventAttributes:o,eventName:n,tagId:e,uuid:r,consentHandler:this.consentHandler,region:this.region})}catch(e){return console.warn("Event request failed.",e),Promise.reject(e)}try{const{performTriggerRegistration:t}=f;await t({stage:this.stage,consent:a,tagId:e,eventName:n,eventAttributes:o,uuid:r,region:this.region})}catch(e){console.warn("ARA trigger registration failed",e)}return Promise.resolve()},b.prototype.trackEventWithTags=async function(e,t,n,o){const r=this.getPixelEndpoint(this.region),a=t||{},s="No eventName name specified.",i="No valid endpoint.";if(!e)return console.warn(s),Promise.reject(s);if(!r)return console.warn(i),Promise.reject(i);n&&(a.ts=n);const E=this.getTagIds(o).map((t=>this.trackRequest(t,r,e,a)));return Promise.all(E)},b.prototype.trackPixel=function(e,t,n){let o;const r={};let a=t||"";a=a.split("?"),o=a.length>1?a[1]:a[0],o=o.split("&"),o.forEach((e=>{const t=e.split("=");let n,o;t.length<=1||("ex-fargs"===t[0]?(n=this.parsePixelArgs(t[1],"&"),r.fargs_id=n.id,r.fargs_type=n.type):"ex-hargs"===t[0]&&(o=this.parsePixelArgs(t[1],";"),r.hargs_c=o.c,r.hargs_p=o.p))})),this.validatePixelData(r),Object.keys(r).forEach((function(e){r[e]||delete r[e]})),this.trackEvent(e,r,n)},b.prototype.addTCFv2=function(e){this.addTCFv2WithTags(e,Object.keys(this.tagIdsByLabels))},b.prototype.addTCFv2WithTags=function(e,t){const{TCFv2:n}=this;this.getTagIds(t).forEach((function(t){n[t]=e}))},b.prototype.getPixelEndpoint=function(e){const t=this.endpoints[e.toUpperCase()];return""===t||null==t?(console.warn("Endpoint does not exist, please check your region configuration!"),null):t},b.prototype.parsePixelArgs=function(e,t){let n=decodeURIComponent(e);const o={};return n=n.replace(/\?/g,""),n.split(t).forEach((function(e){const t=e.split("=");if(t.length>1){const[e,n]=t;o[e]=n}})),o},b.prototype.validatePixelData=function(e){const t=e.hargs_c&&e.hargs_p,n=e.fargs_id&&e.fargs_type;!t&&!n&&console.warn("Invalid arguments for a trackPixel event, please check your implementation!")},b.prototype.setRegion=function(e){this.region=e},b.prototype.setStage=function(e){this.stage=e};var H=b;const z=n,{measurementTokenTimestampPairsNestedArrToMeasurementTokenCookie:K,measurementTokenCookieToMeasurementTokenTimestampPairsNestedArr:F}=a,V="AIPToken",X="cookieExpiry",Z={method:"POST",mode:"cors",cache:"no-cache",credentials:"omit",headers:{"Content-Type":"application/json"},redirect:"follow",referrerPolicy:"no-referrer-when-downgrade"};function W(e){this.cookieHandler=e,this.alreadySavedMeasurementToken=!1}W.prototype.saveMeasurementTokenInURLToCookieIfPresent=function(e){try{if(-1===z.MEASUREMENT_TOKEN_FULLY_ENABLED_REGIONS.indexOf(e)||this.alreadySavedMeasurementToken)return;const t=new URLSearchParams(window.location.search).get(z.MT_LP_QUERY_PARAM);if(!t)return;let n=this.cookieHandler.getCookieValue(z.MEASUREMENT_TOKEN_COOKIE_NAME);n?.split(z.MEASUREMENT_TOKEN_TIMESTAMP_PAIR_DELIMITER).length>=z.NUM_MAX_MEASUREMENT_TOKENS&&(n=K(F(n).slice(0,-1)));const o=performance&&performance.timeOrigin?Math.ceil(performance.timeOrigin):Date.now(),r=K([[t,o]].concat(F(n))),a=o+z.MEASUREMENT_TOKEN_TTL_IN_MS;this.cookieHandler.writeCookie(z.MEASUREMENT_TOKEN_COOKIE_NAME,r,a),this.alreadySavedMeasurementToken=!0}catch{}},W.prototype.removeAnyExpiredMeasurementTokens=function(){try{const e=this.cookieHandler.getCookieValue(z.MEASUREMENT_TOKEN_COOKIE_NAME);if(!e)return;const t=Date.now()-z.MEASUREMENT_TOKEN_TTL_IN_MS,n=F(e).filter((([,e])=>e>t));if(0===n.length)return void this.cookieHandler.deleteCookie(z.MEASUREMENT_TOKEN_COOKIE_NAME);const o=K(n),r=n[z.NEWEST_MEASUREMENT_TOKEN_INDEX][z.MT_TS_PAIR_TS_INDEX]+z.MEASUREMENT_TOKEN_TTL_IN_MS;this.cookieHandler.writeCookie(z.MEASUREMENT_TOKEN_COOKIE_NAME,o,r)}catch{}},W.prototype.formatRequestBody=function(e){const t={};if(null!==e.gdpr&&(t.gdpr=e.gdpr.enabled?1:0,null!==e.gdpr.consent&&(t.gdprConsent=e.gdpr.consent)),null===e.hashedRecords)throw Error("hashedRecords array is null");if(0===e.hashedRecords.length)throw Error("hashedRecords array is empty");return t.hashedRecords=e.hashedRecords,null!==e.ttl&&(t.ttl=e.ttl),void 0!==e.consentString&&(t[z.AIPES_AMAZON_CONSENT_STRING_FIELD_NAME]=e.consentString),t},W.prototype.requestAmznToken=async function(e){const t={...Z,body:JSON.stringify(e)},n=await fetch("https://tk.amazon-adsystem.com/envelope",t);if(n.ok)return await n.json();const o=await n.text();throw Error(o)},W.prototype.renewAmznToken=async function(e){if(!this.cookieHandler.isCookiePresent(z.AMZN_TOKEN_COOKIE_NAME)&&!this.cookieHandler.isCookiePresent(z.NO_CONSENT_COOKIE_NAME))try{const t=this.formatRequestBody(e),n=await this.requestAmznToken(t);return""===n[V]?this.cookieHandler.writeCookie(z.NO_CONSENT_COOKIE_NAME,n[V],n[X]):this.cookieHandler.writeCookie(z.AMZN_TOKEN_COOKIE_NAME,n[V],n[X]),n}catch(e){console.error(e)}return"no-op"},W.prototype.deleteAmznToken=async function(){this.cookieHandler.isCookiePresent(z.AMZN_TOKEN_COOKIE_NAME)&&this.cookieHandler.deleteCookie(z.AMZN_TOKEN_COOKIE_NAME),this.cookieHandler.isCookiePresent(z.NO_CONSENT_COOKIE_NAME)&&this.cookieHandler.deleteCookie(z.NO_CONSENT_COOKIE_NAME)},W.prototype.updateAmznToken=async function(e){return await this.deleteAmznToken(),this.renewAmznToken(e)};var j=W;function $(e,t){this.tokensHandler=e,this.consentHandler=t}async function x(e){const t=(new TextEncoder).encode(e),n=await crypto.subtle.digest("SHA-256",t);return Array.from(new Uint8Array(n)).map((e=>e.toString(16).padStart(2,"0"))).join("")}function B(e){return null!=e&&"string"==typeof e&&e.length>0}$.prototype.setUserData=async function(e){const t={gdpr:{enabled:!1,consent:""},hashedRecords:[],ttl:9600},n=e[1];n.gdpr&&(t.gdpr=n.gdpr),n.ttl&&(t.ttl=n.ttl);const o=/[A-Fa-f0-9]{64}/;if(B(n.email)){const e={type:"email",record:""};n.email=n.email.trim().toLowerCase(),o.test(n.email)?e.record=n.email:e.record=await x(n.email),t.hashedRecords.push(e)}if(B(n.phonenumber)){const e={type:"phonenumber",record:""};o.test(n.phonenumber)?e.record=n.phonenumber:e.record=await x(function(e){let t=e.replace(/[^\d+]+/g,"");return t=t.replace(/^00/,"+"),t.match(/^1/)&&(t=`+${t}`),t.match(/^\+/)||(t=`+1${t}`),t=t.replace(/^\+/,""),t}(n.phonenumber)),t.hashedRecords.push(e)}if(this.consentHandler.isConsentSet()&&this.consentHandler.isAipesEgressEnabled()){t.consentString=this.consentHandler.getFormattedAmazonConsent(!1);const e=this.consentHandler.getTcfString();void 0!==e&&""!==e&&""===t.gdpr.consent&&(t.gdpr.enabled=!0,t.gdpr.consent=e)}return t.hashedRecords.length>0?this.tokensHandler.updateAmznToken(t):Promise.resolve()};var Q=$;function q(e,t,n,o,r){this.eventTracker=e,this.setUserDataHandler=t,this.tokensHandler=n,this.aatEventsQueue=[],this.isSetUserDataInProcess=!1,this.consentHandler=r}q.prototype.processCommandQueue=async function(e){return Promise.all((e||[]).map((e=>this.processCommand(e[0],e[1]))))},q.prototype.processCommand=async function(e,t=Date.now()){const n=Array.prototype.slice.call(e),o=e[0],r=`Unsupported tag command "${o}"`,a={trackevent:(e,t)=>this.trackEvent(e,t),trackpixel:(e,t)=>this.trackPixel(e,t),pixel:(e,t)=>this.withTag(e,t),withtag:(e,t)=>this.withTag(e,t),addpixel:e=>this.addTag(e),addtag:e=>this.addTag(e),addtcfv2:e=>this.addTCFv2(e),setregion:e=>this.setRegion(e),setstage:e=>this.setStage(e),setuserdata:e=>this.setUserData(e)},s=o.toLowerCase();return a[s]?a[s](n,t):(console.warn(r),Promise.reject(r))},q.prototype.setUserData=async function(e){this.isSetUserDataInProcess=!0;try{await this.setUserDataHandler.setUserData(e),this.isSetUserDataInProcess=!1,this.processAatEventsQueue()}catch(e){return console.warn(e),Promise.reject(e)}return Promise.resolve()},q.prototype.processAatEventsQueue=function(){if(this.aatEventsQueue.length)for(;this.aatEventsQueue.length;){const{argumentArray:e,timestamp:t,tagLabel:n}=this.aatEventsQueue.pop();this.trackEvent(e,t,n).catch((e=>{console.warn(e)}))}},q.prototype.trackEvent=async function(e,t,n){if(this.isSetUserDataInProcess)return this.aatEventsQueue.unshift({argumentArray:e,timestamp:t,tagLabel:n}),Promise.resolve();this.tokensHandler.removeAnyExpiredMeasurementTokens();const o=e[1],r=e[2];return void 0!==n?this.eventTracker.trackEventWithTags(o,r,t,[n]):this.eventTracker.trackEvent(o,r,t)},q.prototype.trackPixel=function(e,t){this.eventTracker.trackPixel("__pixel__",e[1],t)},q.prototype.withTag=async function(e,t){const n=e[1],o=e[2]||"",r=`Unsupported command "${o}" used after "withTag" command`;switch(o.toUpperCase()){case"TRACKEVENT":return this.trackEvent(e.slice(2),t,n);case"ADDTCFV2":this.addTCFv2(e.slice(2),n);break;default:return console.warn(r),Promise.reject(r)}return Promise.resolve()},q.prototype.addTag=function(e){const t=e[2],n=e[1];this.eventTracker.addTag({tagId:n,tagLabel:t})},q.prototype.addTCFv2=function(e,t){const n=e[1];void 0!==t?this.eventTracker.addTCFv2WithTags(n,[t]):this.eventTracker.addTCFv2(n)},q.prototype.setRegion=function(e){const t=e[1].toUpperCase();this.eventTracker.setRegion(t),this.tokensHandler.saveMeasurementTokenInURLToCookieIfPresent(t)},q.prototype.setStage=function(e){const t=e[1].toUpperCase();this.eventTracker.setStage(t)},q.prototype.listen=function(){this.eventListener.init()},q.prototype.setAmazonConsent=function(e){this.consentHandler.setAmazonConsent(e[1])};var Y=q;const J=n;function ee(e){this.amazonConsent=void 0,this.cookieHandler=e}ee.prototype.init=function(){try{const e=this.cookieHandler.getCookieValue(J.CONSENT_COOKIE_NAME);if(e){const t=JSON.parse(decodeURIComponent(e));this.setAmazonConsent({...t,AMAZON_CONSENT_AIPES_ENABLED:!0})}}catch(e){console.warn("Error reading consent cookie:",e)}window.addEventListener("amznConsentChange",(e=>{this.setAmazonConsent({...e?.detail?.consent,AMAZON_CONSENT_AIPES_ENABLED:!0})}))},ee.prototype.setAmazonConsent=function(e){this.amazonConsent=e},ee.prototype.isConsentSet=function(){return void 0!==this.amazonConsent},ee.prototype.isAipesEgressEnabled=function(){return this.amazonConsent.AMAZON_CONSENT_AIPES_ENABLED},ee.prototype.getFormattedAmazonConsent=function(e){if(void 0===this.amazonConsent)return;const t={},n={};return void 0!==this.amazonConsent.geo&&(t[J.AIPES_AMAZON_CONSENT_GEO_FIELD_NAME]=function(e){const t={};return void 0!==e.countryCode&&(t[J.AIPES_AMAZON_CONSENT_COUNTRY_CODE_FIELD_NAME]=e.countryCode),void 0!==e.ipAddress&&(t[J.AIPES_AMAZON_CONSENT_IP_ADDRESS_FIELD_NAME]=e.ipAddress),t}(this.amazonConsent.geo)),void 0!==this.amazonConsent.amazonConsentFormat&&(n[J.AIPES_AMAZON_CONSENT_AMAZON_CONSENT_FIELD_NAME]=function(e){const t={};return void 0!==e.amznAdStorage&&(t[J.AIPES_AMAZON_CONSENT_AD_STORAGE_FIELD_NAME]=e.amznAdStorage),void 0!==e.amznUserData&&(t[J.AIPES_AMAZON_CONSENT_USER_DATA_FIELD_NAME]=e.amznUserData),t}(this.amazonConsent.amazonConsentFormat)),void 0!==this.amazonConsent.gpp&&(n[J.AIPES_AMAZON_CONSENT_GPP_FIELD_NAME]=this.amazonConsent.gpp),e&&void 0!==this.amazonConsent.tcf&&(n[J.AIPES_AMAZON_CONSENT_TCF_FIELD_NAME]=this.amazonConsent.tcf),t[J.AIPES_AMAZON_CONSENT_CONSENT_FIELD_NAME]=n,t},ee.prototype.getTcfString=function(){if(void 0!==this.amazonConsent)return this.amazonConsent.tcf};const te={NA:"https://s.amazon-adsystem.com/iu3",EU:"https://aax-eu.amazon-adsystem.com/s/iu3",FE:"https://aax-fe.amazon-adsystem.com/s/iu3"},ne=H,oe=E,re=j,ae=Q,se=Y,ie=ee;!async function(){const e=new oe,t=new ie(e);t.init();const n=new ne(te,t),o=new re(e),r=new ae(o,t);let a=new se(n,r,o,void 0,t);if(void 0!==window.__tcfapi){const e=(e,n)=>{n&&"tcloaded"===e.eventStatus&&t.setAmazonConsent({tcf:e.tcString})};window.__tcfapi("addEventListener",2,e)}if(void 0!==window.__gpp){const e=()=>{const e=window.__gpp("getGPPString");void 0!==e&&t.setAmazonConsent({gpp:e})};window.__gpp("addEventListener",e,"tcfeuv2")}o.removeAnyExpiredMeasurementTokens(),window.amzn&&window.amzn.q&&a.processCommandQueue(window.amzn.q).catch((e=>{console.error("Error processing event queue",e)})),window.amzn=async function(...e){return a.processCommand(e).catch((e=>{console.error("Error processing command",e)}))},window.renewToken=async function(e){return o.renewAmznToken(e)},window.updateToken=async function(e){return o.updateAmznToken(e)},window.deleteToken=async function(){return o.deleteAmznToken()}}()}();
