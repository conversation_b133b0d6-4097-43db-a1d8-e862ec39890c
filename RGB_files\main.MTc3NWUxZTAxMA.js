!function(){"use strict";var t,e,r,n,o,i,a,c,u,s={},l={};function f(t){var e=l[t];if(void 0!==e)return e.exports;var r=l[t]={exports:{}};return s[t](r,r.exports,f),r.exports}f.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(t){if("object"==typeof window)return window}}(),f.rv=function(){return"1.3.15"},f.ruid="bundler=rspack@1.3.15",(t=a||(a={})).WINDOWS_PHONE="Windows Phone",t.ANDROID="android",t.IOS="ios",t.PC="pc",(e=c||(c={})).MUSICAL_LY="musical_ly",e.MUSICALLY_GO="musically_go",e.TRILL="trill",e.ULTRALITE="ultralite",e.LEMON8="lemon8",(i={})[c.LEMON8]={},i[c.MUSICAL_LY]=((n={})[a.IOS]="33.4.0",n[a.ANDROID]="23.1.0",n),i[c.TRILL]=((o={})[a.IOS]="33.4.0",o[a.ANDROID]="23.1.0",o);var p=function(){return"undefined"!=typeof window?window:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:void 0!==f.g?f.g:Function("return this")()},h=function(){return p().TiktokAnalyticsObject||"ttq"},y=function(){return p()[h()]},d=function(t){try{var e=y()._plugins||{};if(null!=e[t])return!!e[t];return!0}catch(t){return!0}};function _(){_=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function u(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,r){return t[e]=r}}function s(t,e,r,o){var i,a,c,u,s=Object.create((e&&e.prototype instanceof p?e:p).prototype);return n(s,"_invoke",{value:(i=t,a=r,c=new I(o||[]),u="suspendedStart",function(t,e){if("executing"===u)throw Error("Generator is already running");if("completed"===u){if("throw"===t)throw e;return T()}for(c.method=t,c.arg=e;;){var r=c.delegate;if(r){var n=function t(e,r){var n=r.method,o=e.iterator[n];if(void 0===o)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=void 0,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+n+"' method")),f;var i=l(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,f;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=void 0),r.delegate=null,f):a:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,f)}(r,c);if(n){if(n===f)continue;return n}}if("next"===c.method)c.sent=c._sent=c.arg;else if("throw"===c.method){if("suspendedStart"===u)throw u="completed",c.arg;c.dispatchException(c.arg)}else"return"===c.method&&c.abrupt("return",c.arg);u="executing";var o=l(i,a,c);if("normal"===o.type){if(u=c.done?"completed":"suspendedYield",o.arg===f)continue;return{value:o.arg,done:c.done}}"throw"===o.type&&(u="completed",c.method="throw",c.arg=o.arg)}})}),s}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=s;var f={};function p(){}function h(){}function y(){}var d={};u(d,i,function(){return this});var v=Object.getPrototypeOf,g=v&&v(v(N([])));g&&g!==e&&r.call(g,i)&&(d=g);var m=y.prototype=p.prototype=Object.create(d);function b(t){["next","throw","return"].forEach(function(e){u(t,e,function(t){return this._invoke(e,t)})})}function w(t,e){var o;n(this,"_invoke",{value:function(n,i){function a(){return new e(function(o,a){!function n(o,i,a,c){var u=l(t[o],t,i);if("throw"!==u.type){var s=u.arg,f=s.value;return f&&"object"==typeof f&&r.call(f,"__await")?e.resolve(f.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(f).then(function(t){s.value=t,a(s)},function(t){return n("throw",t,a,c)})}c(u.arg)}(n,i,o,a)})}return o=o?o.then(a,a):a()}})}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function E(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function N(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:T}}function T(){return{value:void 0,done:!0}}return h.prototype=y,n(m,"constructor",{value:y,configurable:!0}),n(y,"constructor",{value:h,configurable:!0}),h.displayName=u(y,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,u(t,c,"GeneratorFunction")),t.prototype=Object.create(m),t},t.awrap=function(t){return{__await:t}},b(w.prototype),u(w.prototype,a,function(){return this}),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(s(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then(function(t){return t.done?t.value:a.next()})},b(m),u(m,c,"Generator"),u(m,i,function(){return this}),u(m,"toString",function(){return"[object Generator]"}),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=N,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(E),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),u=r.call(i,"finallyLoc");if(c&&u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,f):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),f},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),E(r),f}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;E(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:N(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),f}},t}function v(t,e,r){return(v=!function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}()?function(t,e,r){var n=[null];n.push.apply(n,e);var o=new(Function.bind.apply(t,n));return r&&g(o,r.prototype),o}:Reflect.construct.bind()).apply(null,arguments)}function g(t,e){return(g=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var m={error:[]};(r=u||(u={})).LOAD_START="load_start",r.LOAD_END="load_end",r.BEFORE_INIT="before_init",r.INIT_START="init_start",r.INIT_END="init_end",r.JSB_INIT_START="jsb_init_start",r.JSB_INIT_END="jsb_init_end",r.BEFORE_AD_INFO_INIT_START="before_ad_info_init_start",r.AD_INFO_INIT_START="ad_info_init_start",r.AD_INFO_INIT_END="ad_info_init_end",r.IDENTIFY_INIT_START="identify_init_start",r.IDENTIFY_INIT_END="identify_init_end",r.PLUGIN_INIT_START="_init_start",r.PLUGIN_INIT_END="_init_end",r.PIXEL_SEND="pixel_send",r.PIXEL_SEND_PCM="pixel_send_PCM",r.JSB_SEND="jsb_send",r.HTTP_SEND="http_send",r.HANDLE_CACHE="handle_cache",r.INIT_ERROR="init_error",r.PIXEL_EMPTY="pixel_empty",r.JSB_ERROR="jsb_error",r.API_ERROR="api_error",r.PLUGIN_ERROR="plugin_error",r.CUSTOM_INFO="custom_info",r.CUSTOM_ERROR="custom_error",r.CUSTOM_TIMER="custom_timer";try{(function(){if(/function bind\(\) \{[\s\S]*\[native code\][\s\S]*\}/.test(Function.prototype.bind.toString()))return!0;function t(){}return new(t.bind.apply(t,[void 0,1])) instanceof t})()&&!Function.prototype._ttq_bind?Object.defineProperty(Function.prototype,"_ttq_bind",{value:Function.prototype.bind,enumerable:!1,writable:!1,configurable:!1}):Function.prototype._ttq_bind||Object.defineProperty(Function.prototype,"_ttq_bind",{value:function(t){if("function"!=typeof this)throw TypeError("What is being called by bind is not a function.");var e=t||window,r=Array.prototype.slice.call(arguments).slice(1),n=Symbol("key");return e[n]=this,function t(){return this instanceof t?v(e[n],r.concat(Array.prototype.slice.call(arguments))):e[n].apply(e,r.concat(Array.prototype.slice.call(arguments)))}},enumerable:!1,writable:!1,configurable:!1}),Object._ttq_keys||(Object._ttq_keys=function(t){try{if(Array.isArray(t))return Object.keys(t).filter(function(t){return -1===["each","eachSlice","all","any","collect","detect","findAll","grep","include","inGroupsOf","inject","invoke","max","min","partition","pluck","reject","sortBy","toArray","zip","size","inspect","select","member","_reverse","_each","clear","first","last","compact","flatten","without","uniq","intersect","clone","toJSON","remove","swap","putAll"].indexOf(t)});return Object.keys(t)}catch(e){return Object.keys(t)}});var b=h();function w(t){return null===t?"NULL":void 0===t?"UNDEFINED":"[object Object]"===Object.prototype.toString.call(t)||"[object Array]"===Object.prototype.toString.call(t)?JSON.stringify(t):t.toString()}/function Map\(\) \{[\s\S]*\[native code\][\s\S]*\}/.test(Map.toString())?window[b]._ttq_map=Map:window[b]._ttq_map||(window[b]._ttq_map=function(){this.items={},this.size=0},window[b]._ttq_map.prototype.set=function(t,e){return!this.has(t)&&(this.items[w(t)]=e,this.size++),this},window[b]._ttq_map.prototype.get=function(t){return this.items[w(t)]},window[b]._ttq_map.prototype.has=function(t){return void 0!==this.items[w(t)]},window[b]._ttq_map.prototype.delete=function(t){return this.has(t)&&(delete this.items[w(t)],this.size--),this},window[b]._ttq_map.prototype.clear=function(){this.items={},this.size=0},window[b]._ttq_map.prototype.keys=function(){var t=_().mark(n),e=[];for(var r in this.items)this.has(r)&&e.push(r);function n(){return _().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.delegateYield(e,"t0",1);case 1:case"end":return t.stop()}},t)}return n()},window[b]._ttq_map.prototype.values=function(){var t=_().mark(n),e=[];for(var r in this.items)this.has(r)&&e.push(this.items[r]);function n(){return _().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.delegateYield(e,"t0",1);case 1:case"end":return t.stop()}},t)}return n()}),function(){if(/function create\(\) \{[\s\S]*\[native code\][\s\S]*\}/.test(Map.toString())){Object._ttq_create=Object.create;return}Object._ttq_create=function(){function t(){}var e=Object.prototype.hasOwnProperty;return function(r,n){if("object"!=typeof r&&"function"!=typeof r)throw TypeError("Object prototype may only be an Object or null");t.prototype=r;var o=new t;return t.prototype=null,null!=n&&Object.keys(n).forEach(function(t){var r=n[t];if("object"==typeof r&&null!==r)e.call(r,"value")?o[t]=r.value:("function"==typeof r.get||"function"==typeof r.set)&&Object.defineProperty(o,t,r);else throw TypeError("Property description must be an object: "+r)}),o}}()}()}catch(t){!function(t,e,r,n){void 0===r&&(r={}),void 0===n&&(n=!1);try{var o=y(),i=o.getPlugin&&o.getPlugin("Monitor")||null;i&&i.error&&"function"==typeof i.error?i.error.call(i,t,e,r,n):d("Monitor")&&m.error.push({event:t,err:e,detail:r,withoutJSB:n})}catch(t){}}(u.INIT_ERROR,t)}}();!function(){var t={278:function(t,e,r){t=r.nmd(t);var n,o="__lodash_hash_undefined__",i="[object Arguments]",a="[object Boolean]",c="[object Date]",s="[object Function]",u="[object GeneratorFunction]",f="[object Map]",l="[object Number]",p="[object Object]",h="[object Promise]",d="[object RegExp]",v="[object Set]",_="[object String]",g="[object Symbol]",y="[object WeakMap]",m="[object ArrayBuffer]",w="[object DataView]",E="[object Float32Array]",b="[object Float64Array]",I="[object Int8Array]",O="[object Int16Array]",T="[object Int32Array]",N="[object Uint8Array]",R="[object Uint8ClampedArray]",S="[object Uint16Array]",P="[object Uint32Array]",A=/\w*$/,x=/^\[object .+?Constructor\]$/,L=/^(?:0|[1-9]\d*)$/,C={};C[i]=C["[object Array]"]=C[m]=C[w]=C[a]=C[c]=C[E]=C[b]=C[I]=C[O]=C[T]=C[f]=C[l]=C[p]=C[d]=C[v]=C[_]=C[g]=C[N]=C[R]=C[S]=C[P]=!0,C["[object Error]"]=C[s]=C[y]=!1;var k="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g,D="object"==typeof self&&self&&self.Object===Object&&self,j=k||D||Function("return this")(),M=e&&!e.nodeType&&e,F=M&&t&&!t.nodeType&&t,q=F&&F.exports===M;function U(t,e){return t.set(e[0],e[1]),t}function G(t,e){return t.add(e),t}function H(t,e,r,n){var o=-1,i=t?t.length:0;for(n&&i&&(r=t[++o]);++o<i;)r=e(r,t[o],o,t);return r}function V(t){var e=!1;if(null!=t&&"function"!=typeof t.toString)try{e=!!(t+"")}catch(t){}return e}function B(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r}function Y(t,e){return function(r){return t(e(r))}}function K(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}var W=Array.prototype,J=Function.prototype,X=Object.prototype,Q=j["__core-js_shared__"],Z=(n=/[^.]+$/.exec(Q&&Q.keys&&Q.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"",z=J.toString,$=X.hasOwnProperty,tt=X.toString,te=RegExp("^"+z.call($).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),tr=q?j.Buffer:void 0,tn=j.Symbol,to=j.Uint8Array,ti=Y(Object.getPrototypeOf,Object),ta=Object.create,tc=X.propertyIsEnumerable,ts=W.splice,tu=Object.getOwnPropertySymbols,tf=tr?tr.isBuffer:void 0,tl=Y(Object.keys,Object),tp=tk(j,"DataView"),th=tk(j,"Map"),td=tk(j,"Promise"),tv=tk(j,"Set"),t_=tk(j,"WeakMap"),tg=tk(Object,"create"),ty=tF(tp),tm=tF(th),tw=tF(td),tE=tF(tv),tb=tF(t_),tI=tn?tn.prototype:void 0,tO=tI?tI.valueOf:void 0;function tT(t){var e=-1,r=t?t.length:0;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}function tN(t){var e=-1,r=t?t.length:0;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}function tR(t){var e=-1,r=t?t.length:0;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}function tS(t){this.__data__=new tN(t)}function tP(t,e,r){var n=t[e];$.call(t,e)&&tq(n,r)&&(void 0!==r||e in t)||(t[e]=r)}function tA(t,e){for(var r=t.length;r--;)if(tq(t[r][0],e))return r;return -1}function tx(t){var e=new t.constructor(t.byteLength);return new to(e).set(new to(t)),e}function tL(t,e,r,n){r||(r={});for(var o=-1,i=e.length;++o<i;){var a=e[o],c=n?n(r[a],t[a],a,r,t):void 0;tP(r,a,void 0===c?t[a]:c)}return r}function tC(t,e){var r,n,o=t.__data__;return("string"==(n=typeof(r=e))||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==r:null===r)?o["string"==typeof e?"string":"hash"]:o.map}function tk(t,e){var r,n=null==t?void 0:t[e];return!(!tB(n)||(r=n,Z&&Z in r))&&(tV(n)||V(n)?te:x).test(tF(n))?n:void 0}tT.prototype.clear=function(){this.__data__=tg?tg(null):{}},tT.prototype.delete=function(t){return this.has(t)&&delete this.__data__[t]},tT.prototype.get=function(t){var e=this.__data__;if(tg){var r=e[t];return r===o?void 0:r}return $.call(e,t)?e[t]:void 0},tT.prototype.has=function(t){var e=this.__data__;return tg?void 0!==e[t]:$.call(e,t)},tT.prototype.set=function(t,e){return this.__data__[t]=tg&&void 0===e?o:e,this},tN.prototype.clear=function(){this.__data__=[]},tN.prototype.delete=function(t){var e=this.__data__,r=tA(e,t);return!(r<0)&&(r==e.length-1?e.pop():ts.call(e,r,1),!0)},tN.prototype.get=function(t){var e=this.__data__,r=tA(e,t);return r<0?void 0:e[r][1]},tN.prototype.has=function(t){return tA(this.__data__,t)>-1},tN.prototype.set=function(t,e){var r=this.__data__,n=tA(r,t);return n<0?r.push([t,e]):r[n][1]=e,this},tR.prototype.clear=function(){this.__data__={hash:new tT,map:new(th||tN),string:new tT}},tR.prototype.delete=function(t){return tC(this,t).delete(t)},tR.prototype.get=function(t){return tC(this,t).get(t)},tR.prototype.has=function(t){return tC(this,t).has(t)},tR.prototype.set=function(t,e){return tC(this,t).set(t,e),this},tS.prototype.clear=function(){this.__data__=new tN},tS.prototype.delete=function(t){return this.__data__.delete(t)},tS.prototype.get=function(t){return this.__data__.get(t)},tS.prototype.has=function(t){return this.__data__.has(t)},tS.prototype.set=function(t,e){var r=this.__data__;if(r instanceof tN){var n=r.__data__;if(!th||n.length<199)return n.push([t,e]),this;r=this.__data__=new tR(n)}return r.set(t,e),this};var tD=tu?Y(tu,Object):function(){return[]},tj=function(t){return tt.call(t)};function tM(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||X)}function tF(t){if(null!=t){try{return z.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function tq(t,e){return t===e||t!=t&&e!=e}(tp&&tj(new tp(new ArrayBuffer(1)))!=w||th&&tj(new th)!=f||td&&tj(td.resolve())!=h||tv&&tj(new tv)!=v||t_&&tj(new t_)!=y)&&(tj=function(t){var e=tt.call(t),r=e==p?t.constructor:void 0,n=r?tF(r):void 0;if(n)switch(n){case ty:return w;case tm:return f;case tw:return h;case tE:return v;case tb:return y}return e});var tU=Array.isArray;function tG(t){var e;return null!=t&&"number"==typeof(e=t.length)&&e>-1&&e%1==0&&e<=0x1fffffffffffff&&!tV(t)}var tH=tf||function(){return!1};function tV(t){var e=tB(t)?tt.call(t):"";return e==s||e==u}function tB(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function tY(t){return tG(t)?function(t,e){var r,n,o,a,c,s=tU(t)||(o=n=r=t)&&"object"==typeof o&&tG(n)&&$.call(r,"callee")&&(!tc.call(r,"callee")||tt.call(r)==i)?function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}(t.length,String):[],u=s.length,f=!!u;for(var l in t){$.call(t,l)&&!(f&&("length"==l||(a=l,(c=null==(c=u)?0x1fffffffffffff:c)&&("number"==typeof a||L.test(a))&&a>-1&&a%1==0&&a<c)))&&s.push(l)}return s}(t):function(t){if(!tM(t))return tl(t);var e=[];for(var r in Object(t))$.call(t,r)&&"constructor"!=r&&e.push(r);return e}(t)}t.exports=function(t){return function t(e,r,n,o,h,y,x){if(o&&(L=y?o(e,h,y,x):o(e)),void 0!==L)return L;if(!tB(e))return e;var L,k=tU(e);if(k){if(j=(D=e).length,M=D.constructor(j),j&&"string"==typeof D[0]&&$.call(D,"index")&&(M.index=D.index,M.input=D.input),L=M,!r){var D,j,M,F=e,q=L,Y=-1,W=F.length;for(q||(q=Array(W));++Y<W;)q[Y]=F[Y];return q}}else{var J,X,Q,Z,z,tt=tj(e),te=tt==s||tt==u;if(tH(e)){var tr=e,tn=r;if(tn)return tr.slice();var to=new tr.constructor(tr.length);return tr.copy(to),to}if(tt==p||tt==i||te&&!y){if(V(e))return y?e:{};if(L="function"!=typeof(J=te?{}:e).constructor||tM(J)?{}:tB(X=ti(J))?ta(X):{},!r){return Q=e,Z=(z=L)&&tL(e,tY(e),z),tL(Q,tD(Q),Z)}}else{if(!C[tt])return y?e:{};L=function(t,e,r,n){var o,i,s,u=t.constructor;switch(e){case m:return tx(t);case a:case c:return new u(+t);case w:return o=n?tx(t.buffer):t.buffer,new t.constructor(o,t.byteOffset,t.byteLength);case E:case b:case I:case O:case T:case N:case R:case S:case P:return i=n?tx(t.buffer):t.buffer,new t.constructor(i,t.byteOffset,t.length);case f:return H(n?r(B(t),!0):B(t),U,new t.constructor);case l:case _:return new u(t);case d:return(s=new t.constructor(t.source,A.exec(t))).lastIndex=t.lastIndex,s;case v:return H(n?r(K(t),!0):K(t),G,new t.constructor);case g:return tO?Object(tO.call(t)):{}}}(e,tt,t,r)}}x||(x=new tS);var tc=x.get(e);if(tc)return tc;if(x.set(e,L),!k)var ts=n?function(t){var e;return e=tY(t),tU(t)?e:function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}(e,tD(t))}(e):tY(e);return!function(t,e){for(var r=-1,n=t?t.length:0;++r<n&&!1!==e(t[r],r,t););}(ts||e,function(i,a){ts&&(i=e[a=i]),tP(L,a,t(i,r,n,o,a,e,x))}),L}(t,!0,!0)}},616:function(){}},e={};function r(n){var o=e[n];if(void 0!==o)return o.exports;var i=e[n]={id:n,loaded:!1,exports:{}};return t[n](i,i.exports,r),i.loaded=!0,i.exports}r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,{a:e}),e},r.d=function(t,e){for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(t){if("object"==typeof window)return window}}(),r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.nmd=function(t){return t.paths=[],t.children||(t.children=[]),t},r.rv=function(){return"1.3.15"},r.ruid="bundler=rspack@1.3.15",function(){"use strict";var t,e,n="tt_adInfo",o="tt_appInfo",i="_tt_enable_cookie",a="_ttp",c="tt_sessionId",s="tt_pixel_session_index",u="ttcsid";(iM=aE||(aE={})).WINDOWS_PHONE="Windows Phone",iM.ANDROID="android",iM.IOS="ios",iM.PC="pc",(iF=ab||(ab={})).MUSICAL_LY="musical_ly",iF.MUSICALLY_GO="musically_go",iF.TRILL="trill",iF.ULTRALITE="ultralite",iF.LEMON8="lemon8";var f=((aw={})[ab.LEMON8]={},aw[ab.MUSICAL_LY]=((ay={})[aE.IOS]="33.4.0",ay[aE.ANDROID]="23.1.0",ay),aw[ab.TRILL]=((am={})[aE.IOS]="33.4.0",am[aE.ANDROID]="23.1.0",am),aw),l={expires:390},p="ttoclid",h={"www.agoda.com":"tag"},d=null,v=function(t){d=t},_=function(){return"undefined"!=typeof window?window:"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:void 0!==r.g?r.g:Function("return this")()},g=function(){return _().TiktokAnalyticsObject||"ttq"},y=function(){var t=_();return d||t[g()]},m=function(){var t=_();return void 0!==t.DedicatedWorkerGlobalScope?t instanceof t.DedicatedWorkerGlobalScope:"DedicatedWorkerGlobalScope"===t.constructor.name},w=function(){return!!y()._is_onsite},E=function(){var t=_();return("object"==typeof navigator&&navigator.userAgent?navigator.userAgent:"")||t._userAgent},b=function(t){try{var e=y();return e&&e._self_host_config&&e._self_host_config[t]||""}catch(t){return""}},I=function(t,e){void 0===e&&(e=[]);try{return e.includes(t)}catch(t){return!1}},O=function(t){var e=y(),r=e._i||{},n=t&&r[t];return t&&n&&n._partner?n._partner:e._partner?e._partner:""},T=function(t){var e=y(),r=e._i||{};return Object.keys(r).filter(function(e){return r[e]._partner===t}).length>0||e._partner===t},N=function(t){try{var e=y()._plugins||{};if(null!=e[t])return!!e[t];return!0}catch(t){return!0}},R=function(){try{var t=y()._ppf;return null==t.printAndClear?void 0:t.printAndClear()}catch(t){}},S={info:[],error:[]};function P(t,e,r){void 0===e&&(e={}),void 0===r&&(r=!1);try{var n=y(),o=n.getPlugin&&n.getPlugin("Monitor")||null;o&&o.info&&"function"==typeof o.info?o.info.call(o,t,e,r):N("Monitor")&&S.info.push({event:t,detail:e,withoutJSB:r})}catch(t){}}function A(t,e,r,n){void 0===r&&(r={}),void 0===n&&(n=!1);try{var o=y(),i=o.getPlugin&&o.getPlugin("Monitor")||null;i&&i.error&&"function"==typeof i.error?i.error.call(i,t,e,r,n):N("Monitor")&&S.error.push({event:t,err:e,detail:r,withoutJSB:n})}catch(t){}}function x(t,e){try{var r=y(),n=r.getPlugin&&r.getPlugin("DiagnosticsConsole")||null;n&&n.warn.apply(n,[t,e])}catch(t){}}function L(){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var t={taskName:window.ttq._pf_tn,functionName:"getPixelDetail",start:performance.now()}}catch(t){}var e={lib:"ttq",pixelCode:"MOCK_SHOP_ID"};try{var r=document&&document.currentScript,n=r&&r.getAttribute("data-id")||"",o=g()||"ttq";e={pixelCode:n,lib:o}}catch(t){e={lib:"ttq",pixelCode:""}}try{window.ttq&&window.ttq._ppf&&(t.end=performance.now(),window.ttq._ppf.push(t))}catch(t){}return e}var C=function(){try{var t=y();if(!(t&&t._legacy&&0!==t._legacy.length))return!1;return Object.keys(t._t).length>Object.keys(t._legacy||[]).length||t.reporters.length>Object.keys(t._legacy||[]).length}catch(t){return!1}};function k(t){var e=Error(t);return e.source="ulid",e}(iq=aI||(aI={})).LOAD_START="load_start",iq.LOAD_END="load_end",iq.BEFORE_INIT="before_init",iq.INIT_START="init_start",iq.INIT_END="init_end",iq.JSB_INIT_START="jsb_init_start",iq.JSB_INIT_END="jsb_init_end",iq.BEFORE_AD_INFO_INIT_START="before_ad_info_init_start",iq.AD_INFO_INIT_START="ad_info_init_start",iq.AD_INFO_INIT_END="ad_info_init_end",iq.IDENTIFY_INIT_START="identify_init_start",iq.IDENTIFY_INIT_END="identify_init_end",iq.PLUGIN_INIT_START="_init_start",iq.PLUGIN_INIT_END="_init_end",iq.PIXEL_SEND="pixel_send",iq.PIXEL_SEND_PCM="pixel_send_PCM",iq.JSB_SEND="jsb_send",iq.HTTP_SEND="http_send",iq.HANDLE_CACHE="handle_cache",iq.INIT_ERROR="init_error",iq.PIXEL_EMPTY="pixel_empty",iq.JSB_ERROR="jsb_error",iq.API_ERROR="api_error",iq.PLUGIN_ERROR="plugin_error",iq.CUSTOM_INFO="custom_info",iq.CUSTOM_ERROR="custom_error",iq.CUSTOM_TIMER="custom_timer",(iU=aO||(aO={}))[iU.NOT_SURE=0]="NOT_SURE",iU[iU.INVOKE_METHOD_ENABLED=1]="INVOKE_METHOD_ENABLED",iU[iU.INVOKE_METHOD_NOT_ENABLED=2]="INVOKE_METHOD_NOT_ENABLED",(iG=aT||(aT={})).NORMAL="1",iG.NOT_CROSS_DOMAIN_IFRAME="2",iG.CROSS_DOMAIN_IFRAME="3",iG.WEB_WORKER="4",iG.SANDBOX_IFRAME="5",iG.GTM_IFRAME="6",iG.URL_IN_QUERY_IFRAME="7",iG.UNKNOWN_IFRAME="8";var D="0123456789ABCDEFGHJKMNPQRSTVWXYZ",j=D.length,M=(iH||(iH=function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=arguments[1];e||(e="undefined"!=typeof window?window:null);var n=e&&(e.crypto||e.msCrypto);if(n)return function(){var t=new Uint8Array(1);return n.getRandomValues(t),t[0]/255};try{var o=r(616);return function(){return o.randomBytes(1).readUInt8()/255}}catch(t){}if(t)return function(){return Math.random()};throw k("secure crypto unusable, insecure Math.random not allowed")}()),function(t){return isNaN(t)&&(t=Date.now()),function(t,e){if(isNaN(t))throw Error(t+" must be a number");if(t>0xffffffffffff)throw k("cannot encode time greater than 281474976710655");if(t<0)throw k("time must be positive");if(!1===Number.isInteger(t))throw k("time must be an integer");for(var r=void 0,n="";e>0;e--)r=t%j,n=D.charAt(r)+n,t=(t-r)/j;return n}(t,10)+function(t,e){for(var r="";t>0;t--)r=function(t){var e=Math.floor(t()*j);return e===j&&(e=j-1),D.charAt(e)}(e)+r;return r}(16,iH)});(iV=aN||(aN={})).EMPTY_VALUE="empty_value",iV.WRONG_FORMAT="wrong_format",iV.CORRECT_FORMAT="correct_format",iV.HASHED="hashed",iV.HASHED_ERR="hashed_err",iV.HASHED_CORRECT="hashed_correct",iV.PLAINTEXT_EMAIL="plaintext_email",iV.PLAINTEXT_PHONE="plaintext_phone",(iB=aR||(aR={})).EMPTY_VALUE="empty_value",iB.PLAIN_EMAIL="plain_email",iB.PLAIN_PHONE="plain_phone",iB.HASHED="hashed",iB.FILTER_EVENTS="filter_events",iB.UNKNOWN_INVALID="unknown_invalid",iB.BASE64_STRING_HASHED="base64_string_hashed",iB.BASE64_HEX_HASHED="base64_hex_hashed",iB.PLAIN_MDN_EMAIL="plain_mdn_email",iB.ZIP_CODE_IS_NOT_HASHED="zip_code_is_not_hashed",iB.ZIP_CODE_IS_NOT_US="zip_code_is_not_us",iB.ZIP_CODE_IS_HASHED="zip_code_is_hashed",iB.ZIP_CODE_IS_US="zip_code_is_us",(iY=aS||(aS={})).Manual="manual",iY.ManualV2="manual_v2",iY.Auto="auto",iY.EBManual="eb_manual",(iK=aP||(aP={})).empty="empty",iK.whitespace="whitespace",iK.hardcode="hardcode",iK.encode="encode",(iW=aA||(aA={})).letterCase="letter_case",iW.isNotValidEmail="is_not_valid_email",iW.isNotPossibleEmail="is_not_possible_email",iW.domainTypo="domain_typo",iW.addressFormat="address_format",(iJ=ax||(ax={})).invalidCountry="invalid_country",iJ.notANumber="not_a_number",iJ.tooShort="too_short",iJ.tooLong="too_long",iJ.invalidLength="invalid_length",iJ.emptyCountryCodeThroughIP="empty_country_code_through_ip",iJ.invalidCountryAfterInjectPlus="invalid_country_after_inject_plus",iJ.notANumberAfterInjectPlus="not_a_number_after_inject_plus",iJ.tooShortAfterInjectPlus="too_short_after_inject_plus",iJ.tooLongAfterInjectPlus="too_long_after_inject_plus",iJ.invalidLengthAfterInjectPlus="invalid_length_after_inject_plus",iJ.invalidCountryAfterInjectCountry="invalid_country_after_inject_country",iJ.notANumberAfterInjectCountry="not_a_number_after_inject_country",iJ.tooShortAfterInjectCountry="too_short_after_inject_country",iJ.tooLongAfterInjectCountry="too_long_after_inject_country",iJ.invalidLengthAfterInjectCountry="invalid_length_after_inject_country",(iX=aL||(aL={})).missing="missing",iX.valid="valid",iX.invalid="invalid";var F={raw_email:{label:aL.missing},raw_auto_email:{label:aL.missing},raw_phone:{label:aL.missing},raw_auto_phone:{label:aL.missing},hashed_email:{label:aL.missing},hashed_phone:{label:aL.missing},raw_eb_email:{label:aL.missing},raw_eb_phone:{label:aL.missing}};(iQ=aC||(aC={}))[iQ.UNKNOWN=0]="UNKNOWN",iQ[iQ.HOLD=1]="HOLD",iQ[iQ.REVOKE=2]="REVOKE",iQ[iQ.GRANT=3]="GRANT";var q=["phone_number","email","external_id"],U={EMAIL_IS_HASHED:"email_is_hashed",PHONE_IS_HASHED:"phone_is_hashed",SHA256_EMAIL:"sha256_email",SHA256_PHONE:"sha256_phone"},G="auto_trigger_type",H=function(t){return"[object Object]"===Object.prototype.toString.call(t)},V=function(t){return t+"-"+Date.now()+"-"+(Math.floor(Math.random()*(9e12-1))+1e12)},B=function(t,e,r){return void 0===r&&(r="-"),""+t+r+e};function Y(t,e){void 0===e&&(e=500);var r=-1;return function(){var n=Array.prototype.slice.apply(arguments);Date.now()-r>=e&&(t.apply(void 0,n),r=Date.now())}}var K=function(t){return/^(0|([1-9]\d*))$/.test(""+t)};function W(){W=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function u(t,e,r,o){var i,a,c,s,u=Object._ttq_create((e&&e.prototype instanceof p?e:p).prototype);return n(u,"_invoke",{value:(i=t,a=r,c=new I(o||[]),s="suspendedStart",function(t,e){if("executing"===s)throw Error("Generator is already running");if("completed"===s){if("throw"===t)throw e;return T()}for(c.method=t,c.arg=e;;){var r=c.delegate;if(r){var n=function t(e,r){var n=r.method,o=e.iterator[n];if(void 0===o)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=void 0,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+n+"' method")),l;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,l;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=void 0),r.delegate=null,l):a:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,l)}(r,c);if(n){if(n===l)continue;return n}}if("next"===c.method)c.sent=c._sent=c.arg;else if("throw"===c.method){if("suspendedStart"===s)throw s="completed",c.arg;c.dispatchException(c.arg)}else"return"===c.method&&c.abrupt("return",c.arg);s="executing";var o=f(i,a,c);if("normal"===o.type){if(s=c.done?"completed":"suspendedYield",o.arg===l)continue;return{value:o.arg,done:c.done}}"throw"===o.type&&(s="completed",c.method="throw",c.arg=o.arg)}})}),u}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var l={};function p(){}function h(){}function d(){}var v={};s(v,i,function(){return this});var _=Object.getPrototypeOf,g=_&&_(_(O([])));g&&g!==e&&r.call(g,i)&&(v=g);var y=d.prototype=p.prototype=Object._ttq_create(v);function m(t){["next","throw","return"].forEach(function(e){s(t,e,function(t){return this._invoke(e,t)})})}function w(t,e){var o;n(this,"_invoke",{value:function(n,i){function a(){return new e(function(o,a){!function n(o,i,a,c){var s=f(t[o],t,i);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(l).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(n,i,o,a)})}return o=o?o.then(a,a):a()}})}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function b(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function O(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:T}}function T(){return{value:void 0,done:!0}}return h.prototype=d,n(y,"constructor",{value:d,configurable:!0}),n(d,"constructor",{value:h,configurable:!0}),h.displayName=s(d,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,s(t,c,"GeneratorFunction")),t.prototype=Object._ttq_create(y),t},t.awrap=function(t){return{__await:t}},m(w.prototype),s(w.prototype,a,function(){return this}),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(u(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then(function(t){return t.done?t.value:a.next()})},m(y),s(y,c,"Generator"),s(y,i,function(){return this}),s(y,"toString",function(){return"[object Generator]"}),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=O,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),b(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;b(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:O(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},t}function J(t,e,r,n,o,i,a){try{var c=t[i](a),s=c.value}catch(t){r(t);return}c.done?e(s):Promise.resolve(s).then(n,o)}var X=function(t){},Q=function(t){return!!t},Z=function(t){return void 0!==t.metric_name},z=function(t){return"CompletePayment"===t||"Purchase"===t},$=function(t){var e;return Object.keys((null==t||null==(e=t.context)?void 0:e.user)||{}).some(function(t){return -1!==q.indexOf(t)})};function tt(t,e){var r,n=t;return function(){if(n){for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];r=t.apply(e,i),n=null}return r}}var te=function(t){var e;return void 0===(e=t)&&(e=21),crypto.getRandomValues(new Uint8Array(e)).reduce(function(t,e){return(e&=63)<36?t+=e.toString(36):e<62?t+=(e-26).toString(36).toUpperCase():e>62?t+="-":t+="_",t},"")},tr=function(){for(var t=M(Date.now());27!==t.length;)t.length>27?t=t.slice(0,27):t+="_";return t},tn=function(t,e){if(0===Object.keys(t).length)return{};var r={identity_params:{}},n={email:["email_is_hashed","sha256_email"],phone_number:["phone_is_hashed","sha256_phone"],zip_code:["zip_code"]};return Object.entries(e).forEach(function(e){var o=e[0];e[1]&&n[o]&&n[o].forEach(function(e){if(r.identity_params[e]=[aN.EMPTY_VALUE],t[e]){var n=t[e]||[aN.EMPTY_VALUE];r.identity_params&&(r.identity_params[e]=[].concat(n))}})}),r},to=function(t,e){var r={identity_params:{}};return 0===Object.keys(t).length?{}:(Object.entries(e).forEach(function(e){var n=e[0];if(e[1])if(t[n]&&t[n].length){var o=t[n]||[aN.EMPTY_VALUE];r.identity_params[n]=[].concat(o)}else r.identity_params[n]=[aN.EMPTY_VALUE]}),r)};function ti(t,e){var r=Object.assign({},t);return e.forEach(function(t){null!==r[t]&&void 0!==r[t]&&delete r[t]}),r}var ta=function(t,e){if(!t)return{};var r={};return Object.keys(t).forEach(function(n){e[n]&&(r[n]=t[n])}),r};function tc(t,e,r){var n;return function(){for(var o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];clearTimeout(n),n=setTimeout(function(){t.apply(r,i)},e)}}function ts(t){return tu.apply(this,arguments)}function tu(){var t;return t=W().mark(function t(e){return W().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return void 0===e&&(e=500),t.abrupt("return",new Promise(function(t){setTimeout(function(){t(!0)},e)}));case 2:case"end":return t.stop()}},t)}),(tu=function(){var e=this,r=arguments;return new Promise(function(n,o){var i=t.apply(e,r);function a(t){J(i,n,o,a,c,"next",t)}function c(t){J(i,n,o,a,c,"throw",t)}a(void 0)})}).apply(this,arguments)}var tf=["input[type='button']","input[type='image']","input[type='submit']","button","[class*=btn]","[class*=Btn]","[class*=button]","[class*=Button]","[role*=button]","[id*=btn]","[id*=Btn]","[id*=button]","[id*=Button]","a"],tl=["[href^='tel:']","[href^='callto:']","[href^='sms:']","[href^='skype:']","[href^='whatsapp:']","[href^='mailto:']"],tp=function(t){var e=tf.some(function(e){return t.matches(e)}),r=tl.some(function(e){return t.matches(e)});return e&&!r};function th(t,e){var r={};return t&&("string"==typeof t||"number"==typeof t?r.external_id=t.toString():H(t)&&(r=t)),e&&H(e)&&Object.assign(r,e),r}var td=function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var e,r={taskName:window.ttq._pf_tn,functionName:"getPixelScriptByPixelCode",start:performance.now()}}catch(t){}for(var n=Array.prototype.slice.call(document.getElementsByTagName("script")),o=0;o<n.length;o++){var i=n[o];if(i.innerHTML&&i.innerHTML.indexOf(t)>-1){e=i;break}}try{window.ttq&&window.ttq._ppf&&(r.end=performance.now(),window.ttq._ppf.push(r))}catch(t){}return e},tv=function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var e={taskName:window.ttq._pf_tn,functionName:"getPixelInstalledPosition",start:performance.now()}}catch(t){}var r="unknown";try{var n=t&&td(t);n&&(t_(n)&&(r="isInHead"),ty(n)&&(r="isInBodyTop10"))}catch(t){}try{window.ttq&&window.ttq._ppf&&(e.end=performance.now(),window.ttq._ppf.push(e))}catch(t){}return r},t_=function t(e){var r=e.parentElement;return!!r&&("HEAD"===r.tagName||t(r))},tg=function(t,e){for(var r=[document.body],n=0;n<=t&&r.length;){var o,i=r.pop();if(i===e)return!0;if(!((null==i?void 0:i.tagName.toLowerCase())==="script"&&(null==(o=i.src)?void 0:o.indexOf("analytics.tiktok.com"))>-1)){if(n++,"object"==typeof i&&i.children)for(var a=i.children.length-1;a>=0;a--)r.push(i.children[a])}}return!1},ty=function(t){return tg(10,t)};(iZ=ak||(ak={})).EXTERNAL="external",iZ.APP="app",iZ.TIKTOK="tiktok";var tm={ID:Symbol.for("ID"),Type:Symbol.for("type"),Partner:Symbol.for("partner"),Options:Symbol.for("Options"),Plugins:Symbol.for("Plugins"),Rules:Symbol.for("Rules"),Info:Symbol.for("Info"),ExtraParams:Symbol.for("extraParams"),WebLibraryInfo:Symbol.for("WebLibraryInfo"),SignalType:Symbol.for("SignalType"),IsOnsitePage:Symbol.for("IsOnsitePage")};(iz=aD||(aD={}))[iz.OFFSITE=0]="OFFSITE",iz[iz.ONSITE=1]="ONSITE";var tw=function(){var t,e;return(null==(t=y())||null==(e=t._env)?void 0:e.env)||ak.EXTERNAL},tE=function(){var t,e;return null!=(t=null==(e=y())?void 0:e._is_onsite)?t:aD.OFFSITE},tb=function(t){return(t||tw())!==ak.EXTERNAL},tI=function(t){return(t||tw())===ak.TIKTOK},tO=function(){var t=E();return/windows phone/i.test(t)?aE.WINDOWS_PHONE:/android/i.test(t)?aE.ANDROID:/iPad|iPhone|iPod/.test(t)?aE.IOS:aE.PC},tT=function(){try{return navigator.userAgentData.getHighEntropyValues(["model","platformVersion"])}catch(t){return Promise.resolve({})}},tN=function(){return"android"===tO()},tR=function(){return"ios"===tO()},tS=function(){return!1},tP=function(){return window.top!==window},tA=tt(function(){return/open_news/i.test(E())}),tx=tt(function(){return/ultralite/i.test(E())}),tL=function(){try{return window&&window.top&&window.top.location.href,!1}catch(t){return!0}},tC=function(){try{var t=new URL(decodeURIComponent(window.location.href)),e=/https?:\/\/[^\s/$.?#].[^\s]*/i;return e.test(t.search)||e.test(t.pathname)}catch(t){return!1}},tk=function(){try{if(!tL())return aT.NOT_CROSS_DOMAIN_IFRAME;if(tC())return aT.URL_IN_QUERY_IFRAME;if(window.google_tag_manager)return aT.GTM_IFRAME;if(window.name&&"web-pixel-sandbox"===window.name)return aT.SANDBOX_IFRAME;return aT.CROSS_DOMAIN_IFRAME}catch(t){return aT.UNKNOWN_IFRAME}},tD=function(){return m()?aT.WEB_WORKER:tP()?tk():aT.NORMAL},tj=function(){var t=E();if(t)for(var e=0,r=Object.values(ab);e<r.length;e++){var n=r[e];if(t.includes(n))return n}},tM=function(){var t=E();if(t){var e=t.match(/\bapp_version\/(\S*)/),r=e&&e[1]?e[1].match(/^\d+\.\d+\.\d+$/):void 0;return r?r[0]:void 0}},tF=function(){var t=E();if(t)for(var e=Object.keys(ab),r=0;r<e.length;r++){var n=RegExp("\\b"+ab[e[r]]+"_(\\S*)"),o=t.match(n),i=o&&o[1]?o[1].match(/^\d+\.\d+\.\d+$/):void 0;if(i)return i[0]}},tq=function(){var t=tj(),e=tO(),r=tR()?tF():tN()?tM():null;if(!r||!t||!f[t])return!1;var n=f[t];return!n[e]||tU(n[e],r)},tU=function(t,e){for(var r=t.split("."),n=e.split("."),o=0;o<Math.max(r.length,n.length);o++){var i=parseInt(r[o])||Number.MAX_VALUE,a=parseInt(n[o])||-1;if(i<a)break;if(i>a)return!1}return!0},tG=function(t){var e=tj();return void 0!==e&&t.has(e)},tH=function(){var t=_();try{return!!(t.Shopify&&t.Shopify.shop&&t.Shopify.shop.indexOf(".myshopify.com")>0||t.name&&t.name.startsWith("web-pixel-sandbox-CUSTOM-shopify"))}catch(t){}return!1},tV=function(){var t=y();return"object"==typeof t&&t._i?t._i:{}},tB=function(t,e){var r=tV()||{};Object.keys(r).forEach(function(n){var o=r[n];o._init||o.push([t].concat(e))})},tY=function(t,e,r){var n=(tV()||{})[t];if(n){if(n._init)return;n.push([e].concat(r))}};(i$=aj||(aj={})).PIXEL_CODE="pixelCode",i$.EVENT_SOURCE_ID="eventSourceId",i$.SHOP_ID="shopId";var tK={TTQ:Symbol.for("TTQ"),GLOBAL_TTQ:Symbol.for("GLOBAL_TTQ"),SHOPIFY_TTQ:Symbol.for("SHOPIFY_TTQ"),ENV:Symbol.for("ENV"),CONTEXT:Symbol.for("CONTEXT"),REPORTER:Symbol.for("REPORTER"),REPORTERS:Symbol.for("REPORTERS"),PLUGIN:Symbol.for("PLUGIN"),PLUGINS:Symbol.for("PLUGINS"),TTQ_GLOBAL_OPTIONS:Symbol.for("TTQ_GLOBAL_OPTIONS"),PERFORMANCE_PLUGIN:Symbol.for("PERFORMANCE_PLUGIN"),INTERACTION_PLUGIN:Symbol.for("INTERACTION_PLUGIN"),INTERACTION_PLUGIN_MONITOR:Symbol.for("INTERACTION_PLUGIN_MONITOR"),PERFORMANCE_PLUGIN_MONITOR:Symbol.for("PERFORMANCE_PLUGIN_MONITOR"),ADVANCED_MATCHING_PLUGIN:Symbol.for("ADVANCED_MATCHING_PLUGIN"),AUTO_ADVANCED_MATCHING_PLUGIN:Symbol.for("AUTO_ADVANCED_MATCHING_PLUGIN"),CALLBACK_PLUGIN:Symbol.for("CALLBACK_PLUGIN"),IDENTIFY_PLUGIN:Symbol.for("IDENTIFY_PLUGIN"),MONITOR_PLUGIN:Symbol.for("MONITOR_PLUGIN"),WEB_FL_PLUGIN:Symbol.for("WEB_FL_PLUGIN"),SHOPIFY_PLUGIN:Symbol.for("SHOPIFY_PLUGIN"),AUTO_CONFIG_PLUGIN:Symbol.for("AUTO_CONFIG_PLUGIN"),DIAGNOSTICS_CONSOLE_PLUGIN:Symbol.for("DIAGNOSTICS_CONSOLE_PLUGIN"),COMPETITOR_INSIGHT_PLUGIN:Symbol.for("COMPETITOR_INSIGHT_PLUGIN"),PANGLE_COOKIE_MATCHING_PLUGIN:Symbol.for("PANGLE_COOKIE_MATCHING_PLUGIN"),EVENT_BUILDER_PLUGIN:Symbol.for("EVENT_BUILDER_PLUGIN"),ENRICH_IPV6_PLUGIN:Symbol.for("ENRICH_IPV6_PLUGIN"),RUNTIME_MEASUREMENT_PLUGIN:Symbol.for("RUNTIME_MEASUREMENT_PLUGIN"),PAGE_PERFORMANCE_MONITOR:Symbol.for("PAGE_PERFORMANCE_MONITOR"),PAGE_INTERACTION_MONITOR:Symbol.for("PAGE_INTERACTION_MONITOR"),PAGEDATA_PLUGIN:Symbol.for("PAGEDATA_PLUGIN"),HISTORY_OBSERVER:Symbol.for("HISTORY_OBSERVER"),BATCH_SERVICE:Symbol.for("BATCH_SERVICE"),REPORT_SERVICE:Symbol.for("REPORT_SERVICE"),AD_SERVICE:Symbol.for("AD_SERVICE"),APP_SERVICE:Symbol.for("APP_SERVICE"),BRIDGE_SERVICE:Symbol.for("BRIDGE"),HTTP_SERVICE:Symbol.for("HTTP_SERVICE"),COOKIE_SERVICE:Symbol.for("COOKIE_SERVICE"),CONSENT_SERVICE:Symbol.for("CONSENT_SERVICE"),JS_BRIDGE:Symbol.for("JS_BRIDGE"),TTQ_REPORTERS:Symbol.for("TTQ_REPORTERS"),INTERACTION_MONITOR:Symbol.for("INTERACTION_MONITOR"),PERFORMANCE_MONITOR:Symbol.for("PERFORMANCE_MONITOR"),SANDBOX_PIXEL_API:Symbol("SANDBOX_PIXEL_API")};(i0=aM||(aM={})).INIT_START="initStart",i0.INIT_END="initEnd",i0.PATCH_END="patchEnd",i0.CONTEXT_INIT_START="contextInitStart",i0.CONTEXT_INIT_END="contextInitEnd",i0.PAGE_URL_WILL_CHANGE="pageUrlWillChange",i0.PAGE_URL_DID_CHANGE="pageUrlDidChange",i0.PAGE_DID_LOAD="pageDidLoad",i0.PAGE_WILL_LEAVE="pageWillLeave",i0.AD_INFO_INIT_START="adInfoInitStart",i0.AD_INFO_INIT_END="adInfoInitEnd",i0.BEFORE_AD_INFO_INIT_START="beforeAdInfoInitStart",i0.PIXEL_SEND="pixelSend",i0.PIXEL_DID_MOUNT="pixelDidMount",(i1=aF||(aF={})).UNKNOWN="-1",i1.LOADING="0",i1.INTERACTIVE="1",i1.COMPLETE="2",(i2=aq||(aq={})).HISTORY_CHANGE="hc",i2.URL_CHANGE="uc";var tW=["page","track","identify"],tJ=["holdConsent","revokeConsent","grantConsent"],tX=["identify"],tQ=["instance","instances","loadPixel","enableCookie","disableCookie","holdConsent","revokeConsent","grantConsent"];(i3=aU||(aU={})).EMPTY_EVENT_TYPE_NAME="EMPTY_EVENT_TYPE_NAME",i3.MISMATCHED_EVENT_TYPE_NAME_FOR_CUSTOM_EVENT="MISMATCHED_EVENT_TYPE_NAME_FOR_CUSTOM_EVENT",i3.LONG_EVENT_TYPE_NAME="LONG_EVENT_TYPE_NAME",i3.MISSING_VALUE_PARAMETER="MISSING_VALUE_PARAMETER",i3.MISSING_CURRENCY_PARAMETER="MISSING_CURRENCY_PARAMETER",i3.MISSING_CONTENT_ID="MISSING_CONTENT_ID",i3.MISSING_EMAIL_AND_PHONE="MISSING_EMAIL_AND_PHONE",i3.INVALID_EVENT_PARAMETER_VALUE="INVALID_EVENT_PARAMETER_VALUE",i3.INVALID_CURRENCY_CODE="INVALID_CURRENCY_CODE",i3.INVALID_CONTENT_ID="INVALID_CONTENT_ID",i3.INVALID_CONTENT_TYPE="INVALID_CONTENT_TYPE",i3.INVALID_EMAIL_FORMAT="INVALID_EMAIL_FORMAT",i3.INVALID_PHONE_NUMBER_FORMAT="INVALID_PHONE_NUMBER_FORMAT",i3.INVALID_EMAIL_INFORMATION="INVALID_EMAIL_INFORMATION",i3.INVALID_PHONE_NUMBER_INFORMATION="INVALID_PHONE_NUMBER_INFORMATION",i3.DUPLICATE_PIXEL_CODE="DUPLICATE_PIXEL_CODE",i3.MISSING_PIXEL_CODE="MISSING_PIXEL_CODE",i3.INVALID_PIXEL_CODE="INVALID_PIXEL_CODE";var tZ=function(t,e,r){t.isBound(e)?t.rebind(e).toConstantValue(r):t.bind(e).toConstantValue(r)},tz=function(){var t=y();return t&&t._i||{}},t$=function(t,e,r){void 0===r&&(r=aD.OFFSITE);try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var n={taskName:window.ttq._pf_tn,functionName:"webTtqFactory",start:performance.now()}}catch(t){}tZ(t,tK.ENV,e),tZ(t,tm.SignalType,r);var o=t.get(tK.TTQ);try{window.ttq&&window.ttq._ppf&&(n.end=performance.now(),window.ttq._ppf.push(n))}catch(t){}return o},t0=function(t,e){var r,n=e.id,o=e.type,i=void 0===o?aj.PIXEL_CODE:o,a=e.info,c=e.options,s=e.plugins,u=void 0===s?{}:s,f=e.rules;try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var l={taskName:window.ttq._pf_tn,functionName:"webReporterFactory",start:performance.now()}}catch(t){}var p=t.get(tK.TTQ),h=t.get(tK.TTQ_REPORTERS);if(!h.some(function(t){return t.getReporterId()===n})){tZ(t,tm.ID,n),tZ(t,tm.Type,i),tZ(t,tm.Info,a||((r={})[i]=n,r)),tZ(t,tm.Options,void 0===c?{}:c),tZ(t,tm.Plugins,u),tZ(t,tm.Rules,void 0===f?[]:f),p.enableFirstPartyCookie((null==a?void 0:a.firstPartyCookieEnabled)||!1);var d=t.get(tK.REPORTER);if(u){var v=u.AdvancedMatching,_=u.AutoAdvancedMatching,g={};v&&Object.assign(g,v),_&&Object.assign(g,_),d.setAdvancedMatchingAvailableProperties(g)}d.on("beforeReport",function(t,e,r,n,o){p.dispatch(aM.PIXEL_SEND,t,e,r,n,o)}),h.push(d),t.rebind(tK.TTQ_REPORTERS).toConstantValue(h),p.dispatch(aM.PIXEL_DID_MOUNT,d);try{window.ttq&&window.ttq._ppf&&(l.end=performance.now(),window.ttq._ppf.push(l))}catch(t){}return d}},t1=function(t,e){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var r={taskName:window.ttq._pf_tn,functionName:"mergeWebGlobalTtq",start:performance.now()}}catch(t){}["getReporter","usePlugin","getPlugin","resetCookieExpires"].forEach(function(r){t[r]=function(){for(var t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];return e[r].apply(e,n)}}),t.context=e.context,t.reporters=e.reporters;try{window.ttq&&window.ttq._ppf&&(r.end=performance.now(),window.ttq._ppf.push(r))}catch(t){}return t},t2=function(t,e){var r=t||{},n=r._partner,o=r._ttp,i=r._self_host_config,a=r._usd_exchange_rate,c=r._legacy;return Object.assign(e,{partner:n,ttp:o,cc:r._cc,self_host_config:i,usd_exchange_rate:a,legacy:c,variation_id:r._variation_id,vids:r._vids,server_unqiue_id:r._server_unique_id,currency_list:r._currency_list,plugins:r._plugins,aam:r._aam,auto_config:r._auto_config,cde:r._cde,cookieBasedSessionConfig:r._csid_config}),e},t3=function(t,e){var r=t.get(tK.TTQ_GLOBAL_OPTIONS)||{};t2(e,r),t.isBound(tK.TTQ_GLOBAL_OPTIONS)?t.rebind(tK.TTQ_GLOBAL_OPTIONS).toConstantValue(r):t.bind(tK.TTQ_GLOBAL_OPTIONS).toConstantValue(r)},t6=function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var e={taskName:window.ttq._pf_tn,functionName:"freezeAPI",start:performance.now()}}catch(t){}var r=_(),n=g(),o=y(),i=L().pixelCode,a=void 0===i?"":i;tQ.forEach(function(e){Object.defineProperty(o,e,{get:function(){return function(){try{var r=Array.prototype.slice.call(arguments);return tJ.indexOf(e)>-1&&setTimeout(function(){P(aI.CUSTOM_INFO,{pixelCode:a,custom_name:e})}),t[e].apply(t,r)}catch(t){return A(aI.API_ERROR,t,{extJSON:{api:e}}),{}}}},set:function(){}})}),tW.forEach(function(e){Object.defineProperty(o,e,{get:function(){return function(){try{var r=1==arguments.length&&void 0===arguments[0]?[]:Array.prototype.slice.call(arguments);return tB(e,r),t[e].apply(t,r)}catch(t){return A(aI.API_ERROR,t,{extJSON:{api:e}}),{}}}},set:function(){}})}),r[n]._mounted=!0,r[n].initialize=!0,v(r[n]);try{window.ttq&&window.ttq._ppf&&(e.end=performance.now(),window.ttq._ppf.push(e))}catch(t){}},t5=function(t,e,r){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var n={taskName:window.ttq._pf_tn,functionName:"handleCache",start:performance.now()}}catch(t){}t8(e),Object.entries(tz()).forEach(function(n){var o=n[0],i=n[1];if(!i._init&&("Tealium"===O(o)||w()||i.info)&&(e.getReporter(o)?x(aU.DUPLICATE_PIXEL_CODE):(r||t0)(t,{id:o,type:aj.PIXEL_CODE,info:i.info,options:i.options,rules:i.rules,plugins:i.plugins}),i._init=!0,i.length>0))for(;i.length;){var a=i.shift();if(a){var c=a[0],s=a.slice(1),u=e.instance(o);if(u)switch(c){case"identify":e.identify(s[0],s[1]);break;case"page":e.page(s[0]);break;case"track":u.track(s[0],s[1],s[2]||{});break;default:u[c]?u[c](s[0],s[1],s[2]||{}):A(aI.CUSTOM_ERROR,Error("action not find: "+u[c]))}}}}),t4(e);try{window.ttq&&window.ttq._ppf&&(n.end=performance.now(),window.ttq._ppf.push(n))}catch(t){}},t8=function(t){try{var e=y();[].concat(tJ,tX).forEach(function(r){e.find(function(n,o){var i=n[0],a=n.slice(1);if(i===r)return t[r].apply(t,a),e.splice(o,1),!0})})}catch(t){A(aI.API_ERROR,t,{extJSON:{api:"handleP0APICache"}})}},t4=function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var e={taskName:window.ttq._pf_tn,functionName:"handleGlobalCache",start:performance.now()}}catch(t){}var r=y();if(r.length>0)for(;r.length;){var n=r.shift();if(n){var o=n[0],i=n.slice(1);switch(!T("Tealium")&&tB(o,i),o){case"identify":t.identify(i[0],i[1]);break;case"page":t.page(i[0]);break;case"track":t.track(i[0],i[1],i[2]||{});break;case"enableCookie":t.enableCookie();break;case"disableCookie":t.disableCookie();break;case"holdConsent":t.holdConsent();break;case"revokeConsent":t.revokeConsent();break;case"grantConsent":t.grantConsent()}}}try{window.ttq&&window.ttq._ppf&&(e.end=performance.now(),window.ttq._ppf.push(e))}catch(t){}};function t9(t,e,r){return(t9=!function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}()?function(t,e,r){var n=[null];n.push.apply(n,e);var o=new(Function.bind.apply(t,n));return r&&t7(o,r.prototype),o}:Reflect.construct.bind()).apply(null,arguments)}function t7(t,e){return(t7=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var et="_ttq_inject",ee=function(){function t(){this.bindings={},this.rebind=this.bind}var e=t.prototype;return e.bind=function(t){var e=this;return{to:function(r){return e.bindings[t.toString()]={constructor:r,singleton:!1},e},toConstantValue:function(r){return e.bindings[t.toString()]={value:r,singleton:!0},e}}},e.isBound=function(t){return!!this.bindings[t.toString()]},e.inSingletonScope=function(){var t=Object.keys(this.bindings),e=t[t.length-1];if(e){var r=this.bindings[e];r&&(this.bindings[e]=Object.assign(Object.assign({},r),{singleton:!0}))}return this},e.get=function(t){var e=this.bindings[t.toString()];if(!e)throw Error("No binding found for identifier: "+String(t));return void 0!==e.value?e.value:e.singleton?(e.instance||(e.instance=this.createInstance(e.constructor)),e.instance):this.createInstance(e.constructor)},e.createInstance=function(t){var e=this;if(!t)throw Error("Cannot create instance because constructor is not defined.");var r=(t[et]||[]).map(function(t){try{return e.get(t.identifier)}catch(e){if(t.optional)return;throw e}});return t9(t,r)},t}();function er(){return function(t){}}function en(t){return function(e,r,n){e[et]=e[et]||[],e[et][n]=Object.assign({},e[et][n],{identifier:t})}}function eo(){return function(t,e,r){t[et]=t[et]||[],t[et][r]=Object.assign({},t[et][r],{optional:!0})}}function ei(t,e){try{var r=new URL(t).searchParams.getAll(e);return r[r.length-1]||""}catch(t){return""}}(i6=aG||(aG={})).BIND="bind",i6.REBIND="rebind";var ea=function(t,e,r){try{var n=ei(e,t);if(n)return n;return ei(r||"",t)}catch(t){}return""},ec=function(){if(!tP())return{url:window.location.href,referrer:document.referrer};if(!tL()){var t,e,r,n;return{url:(null==(t=window)||null==(e=t.top)?void 0:e.location.href)||"",referrer:(null==(r=window)||null==(n=r.top)?void 0:n.document.referrer)||""}}var o=window.location.href,i=document.referrer;if(/doubleclick\.net/.test(window.location.hostname)){var a=window.location.pathname,c={};return a.split(";").forEach(function(t){var e=t.split("="),r=e[0],n=e[1];c[r]=decodeURIComponent(n)}),{url:c["~oref"]||o,referrer:document.referrer}}return{url:o,referrer:i}},es=function(t){var e=ec().url;try{return new URL(t||e)}catch(t){}return null},eu=function(t){try{var e=window.sessionStorage.getItem(t);if(!e)return null;return JSON.parse(e)}catch(t){return null}},ef=function(t){try{window.sessionStorage.removeItem(t)}catch(t){}},el=function(t,e){try{var r=JSON.stringify(e);window.sessionStorage.setItem(t,r)}catch(t){}},ep="https://analytics.tiktok.com/api/v2",eh=ep+"/pixel",ed=ep+"/performance",ev=ep+"/interaction",e_=ep+"/performance_interaction",eg=ep+"/pixel/perf",ey=ep+"/pixel/inter",em=ep+"/pixel/act",ew=ep+"/monitor",eE="https://analytics-ipv6.tiktokw.us/ipv6/enrich_ipv6",eb="ttclid",eI="_toutiao_params",eO=function(t,e){try{var r=ea(eb,t,e)||void 0,n=ea("ext_params",t,e)||void 0,o=ea(eI,t,e)||void 0,i=parseInt(ea("ttuts",t,e),10)||void 0,a=o?JSON.parse(o):{},c=a.log_extra,s=void 0===c?void 0:c,u=a.idc,f=void 0===u?void 0:u,l=a.cid,p=void 0===l?void 0:l;return{callback:r,ext_params:n,log_extra:s,creative_id:p,idc:f,ttuts:i,ad_info_from:(s||f||p)&&"url"}}catch(t){return{}}},eT=function(t,e){try{var r=t.log_extra,n=t.ttuts;if(!tR())return!0;if(!tI(e)){if(null!=n)return 1!==n;return!0}if(r){var o=JSON.parse(r);return 1!==o.user_tracking_status}if(null===t.ATTStatus||void 0===t.ATTStatus)return!0;return 3===t.ATTStatus}catch(t){return!1}},eN=function(t,e){var r={};try{var n=t.creative_id,o=(t.callback,t.idc),i=t.convert_id,a=t.ad_info_from,c=t.ad_info_status,s=t.log_extra,u=t.ext_params,f=t.ATTStatus;if(n&&(r.creative_id=n),o&&(r.idc=o),i&&(r.convert_id=i),a&&(r.ad_info_from=a),c&&(r.ad_info_status=c),u&&(r.ext_params=u),f&&(r.ATTStatus=f),s){var l=JSON.parse(s),p=l.ad_user_agent,h=l.ad_id,d=l.rit,v=l.ocbs,_=l.vid,g=l.idc,y=l.country_id;h&&(r.ad_id=h),d&&(r.rit=d),p&&(r.ad_user_agent=p),v&&(r.ocbs=v),_&&(r.vid=_),g&&(r.idc=g),y&&(r.country_id=y)}return r}catch(t){return e&&e(t),r}},eR=function(t){void 0===t&&(t={});var e=Object.assign({},{path:"/"},t);"number"==typeof e.expires&&(e.expires=new Date(Date.now()+864e5*e.expires)),e.expires instanceof Date&&(e.expires=e.expires.toUTCString());var r="";for(var n in e)e[n]&&(r+="; "+n,!0!==e[n]&&(r+="="+e[n].split(";")[0]));return r},eS=function(t,e,r,n){void 0===r&&(r="/"),void 0===n&&(n=new Date(Date.now()+864e5).toUTCString()),ek(t,e,{path:r,expires:n})},eP=function(t){return t&&t.cde?Object.assign({},l,{expires:t.cde}):l},eA=0,ex=0,eL=function(t){var e={};return document.cookie.split(";").forEach(function(t){var r=t.split("=");e[r[0].trim()]=r.slice(1).join("=")}),e[t]||""},eC=function(t){if(0===document.cookie.length)return"";var e=eL(t);return e?unescape(e):""},ek=function(t,e,r){try{if(r){var n=window.location.hostname.split(".");if(ex=n.length,(eA=eM())&&eA<ex){r.domain="."+n.slice(ex-eA-1).join("."),eF(t,e,r);return}for(var o="",i=0;i<ex&&(r.domain=o="."+n[ex-i-1]+o,eA=i,!eF(t,e,r));i++);}else document.cookie=t+"="+e+eR(r)}catch(t){A(aI.API_ERROR,t,{extJSON:{position:"setCookieToHighestDomain"}})}},eD=function(t){var e=t.index,r=t.main;sessionStorage.setItem(s,JSON.stringify({index:e,main:r}))},ej=function(t){var e=t.split(".");return e.length>=3&&e.includes("tt")&&!isNaN(Number(e[2]))&&Number(e[2])>0},eM=function(){if(eA)return eA;var t=eC(a);return t&&ej(t)?Number(t.split(".")[2]):0},eF=function(t,e,r){var n;return t!==a||ej(e)||(e=(n=e)?n.split(".")[0]+".tt."+(tH()?0:eM()):""),document.cookie=t+"="+e+eR(r),e===eC(t)},eq=function(t,e,r){void 0===t&&(t=""),void 0===e&&(e="");try{var n=ea("tt_test_id",t,r);return n&&n!==e&&eS("tt_test_id",n,void 0,"session"),n||e}catch(t){return""}};function eU(){return(eU=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var eG=function(t,e){var r;return!!(null!=e&&null!=(r=e.cookieBasedSessionConfig)&&r.enable&&t.firstPartyCookieEnabled)},eH=function(){var t=new Date().getTime();return{sessionId:t.toString()+"::"+te(20),sessionCount:1,lastEventTS:t,isSessionStart:!0}},eV=function(t){var e={};return t.split("; ").forEach(function(t){var r=t.split("="),n=r[0],o=r[1];if(n.startsWith(u)){var i=n.replace(u,"").replace("_",""),a=o.split("."),c=a[0],s=a[1],f=a[2];c&&K(s)&&K(f)&&(e[i]={sessionId:c,sessionCount:Number(s),lastEventTS:Number(f)})}}),e},eB=function(t){var e=t.sessionCount,r=t.lastEventTS,n=new Date().getTime();return n-r>18e5?{sessionId:n.toString()+"::"+te(20),sessionCount:e+1,lastEventTS:n,isSessionStart:!0}:eU({},t,{lastEventTS:n,isSessionStart:!1})},eY=function(t,e,r){var n=t.sessionId,o=t.sessionCount,i=t.isSessionStart;return Object.assign({csid:n,page_csid:e.sessionId,csct:o},i?{css:1}:{},r?{pixel_code:r}:{})},eK=function(){function t(t){this.pixelCode="",this.loaded=!1,this.status=1,this.name="",this.advertiserID="",this.setupMode=0,this.partner="",this.reporterInfo={},this.plugins={},this.options={},this.rules=[],this.pixelCode=t}var e=t.prototype;return e.getParameterInfo=function(){return Promise.resolve({pixelCode:this.pixelCode,name:this.name,status:this.status,setupMode:this.setupMode,advertiserID:this.advertiserID,partner:this.partner,is_onsite:!1,advancedMatchingAvailableProperties:{}})},e.getReporterId=function(){return""},e.getReporterUniqueLoadId=function(){return""},e.getReporterPartner=function(){},e.getReporterInfo=function(){return{reporter:{}}},e.getReportResultSet=function(){return[]},e.isOnsite=function(){return!1},e.isPartnerReporter=function(){return!1},e.setAdvancedMatchingAvailableProperties=function(t){},e.clearHistory=function(){},e.page=function(t){},e.track=function(t,e,r){return Promise.resolve(null)},e.getUserInfo=function(t){return{}},e.getReporterMatchedUserFormatInfo=function(){return{}},e.getReporterMatchedUserFormatInfoV2=function(){return{}},e.assemblyData=function(){return{event:"",message_id:"",event_id:"",is_onsite:!1,properties:{},context:{ad:{},device:{},library:{name:"",version:""},page:{url:""},pageview_id:"",session_id:"",variation_id:"",user:{}},partner:"",timestamp:""}},e.assemblySelfHostData=function(){return this.assemblyData()},e.trackSync=function(){},e.getReportEventHistoryKey=function(t){return"tiktok"},e.hasReportEventHistory=function(t,e){return!1},e.getCookieBasedSession=function(){return eH()},e.setCookieBasedSession=function(t){},t}();new eK("empty");var eW=function(){function t(t,e){this.initialize=!1,this.plugins=[],this.observers=[],this.reporters=[],this.context=t,this.reportService=e}var e=t.prototype;return e.init=function(t,e){this.initContextInfo(t,e),this.initialize=!0},e.initContextInfo=function(t,e){var r=this;this.dispatch(aM.CONTEXT_INIT_START),this.initAdInfo(t,e),this.initAppInfo(t,e),this.initLocalServiceInfo(),this.reportService.pushPreposition(Promise.resolve().then(function(){return r.initUserInfo()})),this.initTestId(t,e),this.dispatch(aM.CONTEXT_INIT_END)},e.setPageIndex=function(t){},e.setPageInfo=function(t,e){var r=this.context.getPageInfo().url;if(r!==t){this.dispatch(aM.PAGE_URL_WILL_CHANGE,e||r,t);var n=this.context.setPageInfo(t,e||r);(null==n?void 0:n.pageIndex)&&this.setPageIndex(n.pageIndex),this.dispatch(aM.PAGE_URL_DID_CHANGE,t,r)}},e.initAdInfo=function(t,e){},e.initOffsiteAdInfo=function(t){},e.initAppInfo=function(t,e){},e.initUserInfo=function(){},e.initTestId=function(t,e){},e.usePlugin=function(t){try{if(!this.plugins.find(function(e){return e.name===t.name})){this.plugins.push(t);var e=t.name;e&&(this[e[0].toLowerCase()+e.slice(1)+"Plugin"]=t)}}catch(t){}},e.useObserver=function(t){try{if(!this.observers.find(function(e){return e.name===t.name})){this.observers.push(t);var e=t.name;e&&(this[""+(e[0].toLowerCase()+e.slice(1))]=t)}}catch(t){}},e.getPlugin=function(t){return this.plugins.find(function(e){return e.name===t})||null},e.getReporter=function(t){return this.reporters.find(function(e){return e.getReporterId()===t})},e.instance=function(t){var e=this.getReporter(t);return e||(A(aI.PIXEL_EMPTY,Error(""),{pixelCode:t}),new eK(t))},e.instances=function(){return this.reporters},e.identify=function(t,e){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var r={taskName:window.ttq._pf_tn||"identify_api_handler",functionName:window.ttq._pf_tn&&"identify_api_handler",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="identify_api_handler")}}catch(t){}var n=th(t,e);this.context.setUserInfo(n);try{window.ttq&&window.ttq._ppf&&(r.end=performance.now(),window.ttq._ppf.push(r),"identify_api_handler"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}},e.page=function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var e={taskName:window.ttq._pf_tn||"page_api_handler",functionName:window.ttq._pf_tn&&"page_api_handler",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="page_api_handler")}}catch(t){}t.url!==this.context.getPageInfo().url&&(this.setPageInfo(t.url,t.referrer),this.reporters.forEach(function(t){t.clearHistory()}));var r=Object.assign({},t);delete r.url,delete r.referrer,this.reporters.forEach(function(t){t.page(r)});try{window.ttq&&window.ttq._ppf&&(e.end=performance.now(),window.ttq._ppf.push(e),"page_api_handler"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}},e.isOnsitePage=function(){return this.context.getSignalType()===aD.ONSITE||this.reporters.every(function(t){return t.isOnsite()})},e.track=function(t,e,r){void 0===e&&(e={}),void 0===r&&(r={});try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var n={taskName:window.ttq._pf_tn||"track_api_handler",functionName:window.ttq._pf_tn&&"track_api_handler",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="track_api_handler")}}catch(t){}this.instances().forEach(function(n,o){n.track(t,e,Object.assign({_i:o},r))});try{window.ttq&&window.ttq._ppf&&(n.end=performance.now(),window.ttq._ppf.push(n),"track_api_handler"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}},e.dispatch=function(t){for(var e=arguments.length,r=Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];this.plugins.concat(this.observers).forEach(function(e){if("function"==typeof e[t])try{e[t].apply(e,r)}catch(n){A(aI.PLUGIN_ERROR,n,{extJSON:{plugin_name:e.name,cycle_name:t,data:r}})}})},e.getAllReportResultSet=function(){return this.instances().reduce(function(t,e){return t.concat(e.getReportResultSet())},[])},e.resetCookieExpires=function(){},e.enableCookie=function(){},e.disableCookie=function(){},e.enableFirstPartyCookie=function(t){},e.holdConsent=function(){},e.revokeConsent=function(){},e.grantConsent=function(){},e.initLocalServiceInfo=function(){},t}();function eJ(){eJ=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function u(t,e,r,o){var i,a,c,s,u=Object._ttq_create((e&&e.prototype instanceof p?e:p).prototype);return n(u,"_invoke",{value:(i=t,a=r,c=new I(o||[]),s="suspendedStart",function(t,e){if("executing"===s)throw Error("Generator is already running");if("completed"===s){if("throw"===t)throw e;return T()}for(c.method=t,c.arg=e;;){var r=c.delegate;if(r){var n=function t(e,r){var n=r.method,o=e.iterator[n];if(void 0===o)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=void 0,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+n+"' method")),l;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,l;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=void 0),r.delegate=null,l):a:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,l)}(r,c);if(n){if(n===l)continue;return n}}if("next"===c.method)c.sent=c._sent=c.arg;else if("throw"===c.method){if("suspendedStart"===s)throw s="completed",c.arg;c.dispatchException(c.arg)}else"return"===c.method&&c.abrupt("return",c.arg);s="executing";var o=f(i,a,c);if("normal"===o.type){if(s=c.done?"completed":"suspendedYield",o.arg===l)continue;return{value:o.arg,done:c.done}}"throw"===o.type&&(s="completed",c.method="throw",c.arg=o.arg)}})}),u}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var l={};function p(){}function h(){}function d(){}var v={};s(v,i,function(){return this});var _=Object.getPrototypeOf,g=_&&_(_(O([])));g&&g!==e&&r.call(g,i)&&(v=g);var y=d.prototype=p.prototype=Object._ttq_create(v);function m(t){["next","throw","return"].forEach(function(e){s(t,e,function(t){return this._invoke(e,t)})})}function w(t,e){var o;n(this,"_invoke",{value:function(n,i){function a(){return new e(function(o,a){!function n(o,i,a,c){var s=f(t[o],t,i);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(l).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(n,i,o,a)})}return o=o?o.then(a,a):a()}})}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function b(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function O(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:T}}function T(){return{value:void 0,done:!0}}return h.prototype=d,n(y,"constructor",{value:d,configurable:!0}),n(d,"constructor",{value:h,configurable:!0}),h.displayName=s(d,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,s(t,c,"GeneratorFunction")),t.prototype=Object._ttq_create(y),t},t.awrap=function(t){return{__await:t}},m(w.prototype),s(w.prototype,a,function(){return this}),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(u(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then(function(t){return t.done?t.value:a.next()})},m(y),s(y,c,"Generator"),s(y,i,function(){return this}),s(y,"toString",function(){return"[object Generator]"}),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=O,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),b(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;b(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:O(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},t}function eX(t,e,r,n,o,i,a){try{var c=t[i](a),s=c.value}catch(t){r(t);return}c.done?e(s):Promise.resolve(s).then(n,o)}eW=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er()],eW);var eQ=(i5=eJ().mark(function t(e,r){return eJ().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===r&&(r=1),!(r>=0)){t.next=13;break}return t.prev=2,t.next=5,function(t){return new Promise(function(e,r){var n=document.createElement("script");n.type="text/javascript",n.async=!0,n.src=t;var o=document.getElementsByTagName("script")[0];o&&o.parentNode?o.parentNode.insertBefore(n,o):r("none element"),n.onload=function(){e(!0)},n.onerror=r})}(e);case 5:return t.abrupt("return",Promise.resolve(!0));case 8:return t.prev=8,t.t0=t.catch(2),t.abrupt("return",eQ.call(null,e,r-1));case 11:t.next=14;break;case 13:throw Error;case 14:case"end":return t.stop()}},t,null,[[2,8]])}),i8=function(){var t=this,e=arguments;return new Promise(function(r,n){var o=i5.apply(t,e);function i(t){eX(o,r,n,i,a,"next",t)}function a(t){eX(o,r,n,i,a,"throw",t)}i(void 0)})},function(t,e){return i8.apply(this,arguments)});(i4=aH||(aH={})).TRACK="track",i4.PERFORMANCE="performance",i4.INTERACTION="interaction",i4.PCM="PCM",i4.PERFORMANCE_INTERACTION="performance_interaction",i4.SELFHOST="selfhost",i4.AUTO_CONFIG="auto_config",i4.PAGE="Pf",i4.PAGE_PERFORMANCE="page_performance",i4.PAGE_INTERACTION="page_interaction";var eZ=["EnrichAM"];(i9=aV||(aV={})).LDU="limited_data_use",i9.EVENTID="eventID",i9.EVENT_ID="event_id",(i7=aB||(aB={}))[i7.defaultReport=0]="defaultReport",i7[i7.httpReport=1]="httpReport",i7[i7.htmlHttpReport=2]="htmlHttpReport",(at=aY||(aY={}))[at.P0=0]="P0",at[at.P1=1]="P1",at[at.P2=2]="P2";var ez=["ttuts","ad_info_from"],e$="Pageview",e0=[],e1=["AED","ALL","AMD","ARS","AUD","AZN","BDT","BGN","BHD","BIF","BOB","BRL","BYN","CAD","CHF","CLP","CNY","COP","CRC","CZK","DKK","DOP","DZD","EGP","EUR","GBP","GEL","GTQ","HKD","HNL","HUF","IDR","ILS","INR","IQD","ISK","JOD","JPY","KES","KHR","KRW","KWD","KZT","LBP","MAD","MOP","MXN","MYR","NGN","NIO","NOK","NZD","OMR","PAB","PEN","PHP","PKR","PLN","PYG","QAR","RON","RSD","RUB","SAR","SEK","SGD","THB","TND","TRY","TWD","TZS","UAH","USD","UZS","VES","VND","ZAR"],e2=function(){function t(){this.events={}}var e=t.prototype;return e.on=function(t,e){var r=this.events[t]||[];r.push(e),this.events[t]=r},e.emit=function(t){for(var e=arguments.length,r=Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];(this.events[t]||[]).forEach(function(t){return t.apply(void 0,r)})},e.off=function(t,e){var r=this.events[t]||[];this.events[t]=r.filter(function(t){return t!==e})},t}();e2=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er()],e2);var e3=function(t,e){if("selfhost"===t&&e&&b(e))return"https://"+b(e)+"/api/v2/pixel";var r={track:eh,performance:ed,interaction:ev,performance_interaction:e_,auto_config:em,page_performance:eg,page_interaction:ey}[t];return r||null};function e6(){return(e6=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function e5(t,e){return(e5=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var e8=function(t){function e(e,r){var n;return(n=t.call(this)||this).reporterInfo={},n.options={},n.plugins={},n.rules=[],n.reportEventHistory={},n.reportResultSet=[],n.selfHostConfig={},n.currentHref="",n.advancedMatchingAvailableProperties={external_id:!0,partner_id:!0},n.session=eH(),n.reportService=r,n.context=e,n}e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,e5(e,t);var r=e.prototype;return r.getParameterInfo=function(){var t=this;return this.getInstance().then(function(){var e=t.reporterInfo,r=e.name,n=e.status,o=e.setupMode,i=e.advertiserID,a=e.is_onsite;return{pixelCode:t.getReporterId(),name:void 0===r?"":r,status:void 0===n?1:n,setupMode:void 0===o?0:o,advertiserID:(void 0===i?"":i).toString(),partner:t.getReporterPartner()||"",is_onsite:void 0!==a&&a,advancedMatchingAvailableProperties:t.advancedMatchingAvailableProperties,rules:t.rules}})},r.getInstance=function(){return this.pixelPromise=Promise.resolve(this)},r.getReporterId=function(){return""},r.getReporterUniqueLoadId=function(){return""+this.getReporterId()},r.getReporterPartner=function(){},r.getReporterInfo=function(){return{pixel:{code:this.getReporterId()}}},r.setAdvancedMatchingAvailableProperties=function(t){this.advancedMatchingAvailableProperties=Object.assign({},this.advancedMatchingAvailableProperties,t)},r.isOnsite=function(){return!1},r.isPartnerReporter=function(){return!1},r.getReportResultSet=function(){return this.reportResultSet},r.getUserInfo=function(t){return{}},r.getReporterMatchedUserFormatInfo=function(){return{}},r.getReporterMatchedUserFormatInfoV2=function(){return{}},r.getReportEventHistoryKey=function(t){return"tiktok"},r.getCookieBasedSession=function(){return this.session},r.setCookieBasedSession=function(t){t&&(this.session=t)},r.convertCookieBasedSession=function(t){return{}},r.clearHistory=function(){this.reportEventHistory={}},r.pushReport=function(t,e){void 0===e&&(e="tiktok"),this.reportEventHistory[e]||(this.reportEventHistory[e]=[]),this.reportEventHistory[e].push(t)},r.hasReportEventHistory=function(t,e){var r=this.getReportEventHistoryKey(e);return this.reportEventHistory[r]?!!(e0.includes(t)&&this.reportEventHistory[r].includes(t)):(this.reportEventHistory[r]=[],!1)},r.page=function(t){void 0===t&&(t={})},r.track=function(t,e,r,n,o){var i=this,a=n||aH.TRACK,c=o||aB.defaultReport;return!this.reportService||this.hasReportEventHistory(t,c)?Promise.resolve(null):(this.pushReport(t,this.getReportEventHistoryKey(c)),this.reportService.reportPreTasks.then(function(){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var n={taskName:window.ttq._pf_tn||"track_after_report_preposition",functionName:window.ttq._pf_tn&&"track_after_report_preposition",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="track_after_report_preposition")}}catch(t){}var o=i.getReporterId(),s=i.trackSync(o,t,e,r,a,c);i.trackPostTask({reporterId:o,eventType:t,properties:e,eventConfig:r,type:a,reportType:c,reportData:s});try{window.ttq&&window.ttq._ppf&&(n.end=performance.now(),window.ttq._ppf.push(n),"track_after_report_preposition"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}return Promise.resolve({reporterId:o,eventType:t,properties:e,eventConfig:r,type:a,reportType:c,reportData:s})}))},r.getEventType=function(t){return t},r.trackPostTask=function(t){},r.trackSync=function(t,e,r,n,o,i,a){void 0===o&&(o=aH.TRACK),void 0===i&&(i=aB.defaultReport);var c=o!==aH.SELFHOST?this.assemblyData(t,e,r,n,o):this.assemblySelfHostData(t,e,r,n,o),s=a||e3(o,t);if(null!==s&&this.reportService)return this.emit("beforeReport",t,e,c,n,o),this.reportResultSet.push(this.reportService.report(s,c,i,aY.P0)),c},r.handlePropertiesToOptions=function(t,e){var r={};return e.forEach(function(e){r[e]=t[e],delete t[e]}),r},r.assemblyData=function(t,e,r,n,o){void 0===r&&(r={}),void 0===n&&(n={}),void 0===o&&(o=aH.TRACK);var i=this.context.getAllData(),a=i.adInfo,c=i.userInfo,s=i.appInfo,u=i.pageSign,f=i.libraryInfo,l=i.pageInfo,p=i.signalType,h=u.sessionId,d=u.variationId,v=Object.assign({},r),_=v&&v.is_standard_mode;v&&v.pixelMethod&&delete v.pixelMethod;var g=Object.assign({},f,{version:this.context.isLegacyPixel(t)?"legacy-"+f.version:f.version}),y=Object.assign({},ti(a,ez),{device_id:s.device_id,uid:s.user_id}),m=this.handlePropertiesToOptions(v,[aV.LDU,aV.EVENTID,aV.EVENT_ID]),w=this.options.limited_data_use,E=null!==m.limited_data_use&&void 0!==m.limited_data_use?m.limited_data_use:w;null==E?delete m.limited_data_use:m.limited_data_use=!!E;var b=n&&(n.event_id||n.eventID)||"";m.event_id=b||m.event_id||m.eventID||"",delete m.eventID;var I=this.getReporterInfo();I.pixel&&(I.pixel.runtime=tD(),_&&(I.pixel.mode="standard"));var O=this.getUserInfo(aS.Manual)||{},T=this.getUserInfo(aS.ManualV2)||{},N=this.getReporterMatchedUserFormatInfoV2()||{},R=this.getUserInfo(aS.Auto)||{};R.auto_trigger_type&&(Object.assign(v,{auto_trigger_type:R.auto_trigger_type}),delete R.auto_trigger_type);var S=this.getUserInfo(aS.EBManual)||{};tN()&&Object.assign(v,{android_version:s.android_version,device_model:s.device_model});var P={};c.anonymous_id&&(P.anonymous_id=c.anonymous_id);var A=this.getEventType(e),x=this.convertCookieBasedSession(o);return Object.assign({event:A,event_id:b,message_id:B(V("messageId"),t),is_onsite:!!p,timestamp:new Date().toJSON(),context:e6({ad:y,device:{platform:s.platform},user:Object.assign({},P,O,T,R,S)},I,{page:Object.assign({},l),library:Object.assign({},g),session_id:B(h,t),pageview_id:B(this.context.getPageViewId(),this.getReporterUniqueLoadId(),"::"),variation_id:d||""},x),_inspection:N,properties:v},m)},r.assemblySelfHostData=function(t,e,r,n,o){return void 0===r&&(r={}),void 0===n&&(n={}),this.assemblyData(t,e,r,n,o)},e}(e2);e8=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er()],e8);var e4={ViewForm:"ViewContent",ViewConsultationPage:"ViewContent",ViewDownloadPage:"ViewContent",Checkout:"PlaceAnOrder",Registration:"CompleteRegistration",AddBilling:"AddPaymentInfo",StartCheckout:"InitiateCheckout",ClickInDownloadPage:"ClickButton",ClickInConsultationPage:"ClickButton",ClickForm:"ClickButton",ClickToDownload:"Download",Consult:"Contact",ConsultByPhone:"Contact",CompletePayment:"Purchase",SubmitForm:"Lead"},e9=["event_experiment","dynamic_parameter_config","eb_version","eb_rule_id","tf"],e7=function(t){var e=t.getUserInfo().anonymous_id,r=eC(a);e!==r&&t.setUserInfoWithoutIdentifyPlugin({anonymous_id:r})},rt=function(t,e,r){try{var n=window.location.hostname.split(".");if(void 0!==aK&&aK<n.length){document.cookie=t+"="+e+eR(Object.assign({},r,{domain:"."+n.slice(aK).join(".")}));return}for(var o=n.length-2;o>=0;o--){var i="."+n.slice(o).join(".");if(document.cookie=t+"="+e+eR(Object.assign({},r,{domain:i})),eC(t)===e){aK=o;break}}}catch(t){}},re=function(t,e,r){var n,o=eH();return e&&(o=eB(e)),rt(t?u+"_"+t:u,(n=o).sessionId+"."+n.sessionCount+"."+n.lastEventTS,eP(r)),o};function rr(t,e){return(rr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var rn=function(t){function e(e){var r,n,o=e.id,i=e.type,a=e.isOnsitePage,c=e.context,s=e.reporterInfo,u=e.ttqOptions,f=e.reportService,l=e.plugins,p=e.rules,h=e.options;return(n=t.call(this,c,f)||this).ttp="",n.loaded=!1,n.id=o,n.pixelCode=o,n.type=i,n.isOnsitePage=a,n.options=(void 0===h?{}:h)||{},n.plugins=(void 0===l?{}:l)||{},n.rules=(void 0===p?[]:p)||[],n.reporterInfo=Object.assign(s||{},((r={})[i]=o,r)),n.ttp=u.ttp||"",n.currency_list=u.currency_list||null,n.ttqPartner=u.partner||"",n.selfHostConfig=u.self_host_config||{},n.ttqOptions=u,n.pixelPromise=n.getInstance(),n}e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,rr(e,t);var r=e.prototype;return r.identify=function(t,e){var r=th(t,e);this.context.setUserInfo(r)},r.getReporterId=function(){return this.id||""},r.getReporterUniqueLoadId=function(){return this.reporterInfo.loadId+"-"+this.getReporterId()},r.getReporterPartner=function(){var t;return(null==(t=this.reporterInfo)?void 0:t.partner)||""},r.setPixelInfo=function(t,e,r){var n,o=this.type;this.reporterInfo=Object.assign(this.reporterInfo,Object.assign({},t),((n={})[o]=this.getReporterId(),n)),e&&(this.rules=e),r&&(this.plugins=r)},r.getInstance=function(){return Promise.resolve(this)},r.getReporterInfo=function(){return this.reporterInfo.pixelCode?t.prototype.getReporterInfo.call(this):{shop_id:this.reporterInfo.shopId,eventSourceId:this.reporterInfo.eventSourceId}},r.getUserInfo=function(t){var e=this.context.getUserInfo(),r=ta(e,Object.assign({},this.advancedMatchingAvailableProperties));switch(t){case aS.Manual:return ta(this.isPartnerReporter()?r:e,{external_id:!0,email:!0,phone_number:!0,ttoclid:!0});case aS.ManualV2:return ta(this.isPartnerReporter()?r:e,{first_name:!0,last_name:!0,city:!0,state:!0,country:!0,zip_code:!0,partner_id:!0,ttoclid:!0});case aS.EBManual:return ta(e,{eb_email:!0,eb_phone_number:!0});case aS.Auto:var n=ta(r,{external_id:!0,auto_email:!0,auto_phone_number:!0,ttoclid:!0});return Object.assign(n,(n.auto_email||n.auto_phone_number)&&e.auto_trigger_type?{auto_trigger_type:e.auto_trigger_type}:{});default:return r}},r.getReporterMatchedUserFormatInfo=function(){var t=this.context.getUserFormatInfo(),e=to(t,this.isPartnerReporter()?this.advancedMatchingAvailableProperties:{external_id:!0,email:!0,phone_number:!0}),r=ta(t,{auto_email:!0,auto_phone_number:!0});return Object.keys(r).length>0&&(e.identity_params||(e.identity_params={}),Object.assign(e.identity_params,r)),e},r.getReporterMatchedUserFormatInfoV2=function(){return tn(this.context.getUserFormatInfoV2(),this.isPartnerReporter()?this.advancedMatchingAvailableProperties:{external_id:!0,email:!0,phone_number:!0,first_name:!0,last_name:!0,city:!0,state:!0,country:!0,zip_code:!0,partner_id:!0})},r.isOnsite=function(){var t;return!!(null==(t=this.reporterInfo)?void 0:t.is_onsite)},r.isPartnerReporter=function(){var t=this.getReporterPartner();return!!(t&&"None"!==t)},r.getSignalDiagnosticLabels=function(){var t=this.context.getSignalDiagnosticLabels();if(!t)return Object.assign({},F);var e=this.advancedMatchingAvailableProperties,r=e.email,n=e.phone_number,o=e.auto_email,i=e.auto_phone_number;return Object.assign({},F,ta(t,{raw_email:r=!this.isPartnerReporter()||r,raw_phone:n=!this.isPartnerReporter()||n,hashed_email:r,hashed_phone:n,raw_auto_email:o,raw_auto_phone:i,raw_eb_email:!0,raw_eb_phone:!0}))},r.setCookieBasedSession=function(t){t&&(this.session=t)},r.convertCookieBasedSession=function(t){return eG(this.reporterInfo,this.ttqOptions)&&t!==aH.AUTO_CONFIG?eY(this.getCookieBasedSession(),this.context.getPageCookieBasedSession()):{}},r.assemblyData=function(e,r,n,o,i){void 0===n&&(n={}),void 0===o&&(o={}),void 0===i&&(i=aH.TRACK);var a,c,s=t.prototype.assemblyData.call(this,e,r,n,o,i);s.is_onsite=this.isOnsitePage.value;var u=O(e)||this.ttqPartner;u&&(s.partner=u),s.signal_diagnostic_labels=this.getSignalDiagnosticLabels();var f=E();f&&(s.context.userAgent=f);var l=function(){try{var t=document.readyState;if("loading"==t)return aF.LOADING;if("interactive"==t)return aF.INTERACTIVE;if("complete"==t)return aF.COMPLETE;return aF.UNKNOWN}catch(t){return aF.UNKNOWN}}();return l&&(s.context.page.load_progress=l),a=s.properties,void 0===(c=s._inspection)&&(c={}),e9.forEach(function(t){a.hasOwnProperty(t)&&(c[t]=a[t],delete a[t])}),s._inspection=c,i!==aH.PAGE&&(s._inspection.ppf=R()),s._inspection.vids=this.context.getVids(),s.context.ad.sdk_env=tw(),s.context.ad.jsb_status=function(){try{var t,e;return[aO.INVOKE_METHOD_ENABLED,aO.INVOKE_METHOD_NOT_ENABLED][[!!(null!=(t=window)&&null!=(e=t.ToutiaoJSBridge)&&e.invokeMethod),!0].findIndex(function(t){return t})]}catch(t){return aO.NOT_SURE}}(),i!==aH.INTERACTION&&i!==aH.PERFORMANCE&&i!==aH.PERFORMANCE_INTERACTION||!1!==this.context.getEnableAdTracking()||this.isOnsitePage.value||(s.context.user={},s.context.ad=this.context.getOffsiteAdInfo(),s.context.ad=ti(s.context.ad,ez)),s},r.page=function(t){void 0===t&&(t={});var e=ec().url;e!==this.currentHref&&(this.currentHref=e,this.track(e$,t,{}))},r.track=function(e,r,n,o,i){var a=this;void 0===r&&(r={}),void 0===n&&(n={}),void 0===o&&(o=aH.TRACK),void 0===i&&(i=aB.defaultReport);var c=function(){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var c={taskName:window.ttq._pf_tn||"track_after_reporter_init",functionName:window.ttq._pf_tn&&"track_after_reporter_init",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="track_after_reporter_init")}}catch(t){}var s=a.getReporterId();if(eZ.includes(e))return t.prototype.track.call(a,e,r,n,o,i);var u=Object.assign({},n);a.selfHostConfig[s]&&!n.eventID&&(u=Object.assign({},u,{eventID:B(V("default_eventId"),s)}));try{window.ttq&&window.ttq._ppf&&(c.end=performance.now(),window.ttq._ppf.push(c),"track_after_reporter_init"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}return t.prototype.track.call(a,e,r,u,o,i)};return this.loaded?c():this.getInstance().then(c)},r.getEventType=function(t){return e4[t]||t},r.trackSync=function(e,r,n,o,i,a,c){if(void 0===n&&(n={}),void 0===o&&(o={}),void 0===i&&(i=aH.TRACK),void 0===a&&(a=aB.defaultReport),"track"===i&&P(aI.PIXEL_SEND,{pixelCode:e,extJSON:{event:r}}),i!==aH.TRACK)return void t.prototype.trackSync.call(this,e,r,n,o,i,a,c);n&&"string"==typeof n.currency&&(n.currency=n.currency.toUpperCase());var s,u=this.context.getTestID();if(u){var f=this.assemblyData(e,r,n,o);return f.tt_test_id=u,f.context.ad={},null==(s=this===null||void 0===this?void 0:this.reportService)||s.report(c||eh,f,aB.htmlHttpReport,aY.P0),f}if(n&&"object"==typeof n){var l,p=n,h=p.value,d=p.currency;void 0===h||!isNaN(h)&&h>=0||P(aI.CUSTOM_ERROR,{pixelCode:e,custom_name:"invalid_value",extJSON:{event:r,value:h,currency:d}}),void 0===d||(void 0===(l=this.currency_list)&&(l=null),(l||e1).includes(d))||P(aI.CUSTOM_ERROR,{pixelCode:e,custom_name:"invalid_currency",extJSON:{event:r,value:h,currency:d}})}return t.prototype.trackSync.call(this,e,r,n,o,i,a,c)},r.trackPostTask=function(t){var e=t.reporterId,r=t.eventType,n=t.properties,o=t.eventConfig;eZ.includes(r)||this.selfHostConfig[e]&&!this.hasReportEventHistory(r,aB.htmlHttpReport)&&(this.pushReport(r,this.getReportEventHistoryKey(aB.htmlHttpReport)),this.trackSync(e,r,n,o,aH.SELFHOST,aB.htmlHttpReport))},r.getReportEventHistoryKey=function(t){return t===aB.htmlHttpReport?this.selfHostConfig[this.getReporterId()]:"tiktok"},r.assemblySelfHostData=function(t,e,r,n,o){void 0===r&&(r={}),void 0===n&&(n={});var i=this.assemblyData(t,e,r,n,o),a=this.ttp;return a&&(i.context.user.ttp=a),i},e}(e8);function ro(t,e){return(ro=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var ri=function(t,e){return function(r,n){e(r,n,t)}},ra=function(t){function e(e,r,n,o,i,a,c,s,u,f){return t.call(this,{id:e,type:r,isOnsitePage:n,context:o,reporterInfo:i,ttqOptions:a,reportService:c,plugins:s,rules:u,options:f})||this}e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,ro(e,t);var r=e.prototype;return r.getInstance=function(){var t,e=this;if(this.pixelPromise)return this.pixelPromise;var r=(t=this.id,tV()[t]||{});return w()||r&&r.info?(this.loaded=!0,this.pixelPromise=Promise.resolve(this)):(this.pixelPromise=new Promise(function(t,r){var n,o,i=es();eQ((n=e.id,o=(null==i?void 0:i.hostname)||"",(tS()?"/static/config.js":"https://analytics.tiktok.com/i18n/pixel/config.js")+"?sdkid="+n+"&hostname="+o)).then(function(){e.loaded=!0,t(e)}).catch(function(t){e.pixelPromise=null,r(t)})}),this.pixelPromise)},r.setCookieBasedSession=function(e){var r=re(this.pixelCode,e,this.ttqOptions);t.prototype.setCookieBasedSession.call(this,r)},r.track=function(e,r,n,o,i){if(void 0===r&&(r={}),void 0===n&&(n={}),void 0===o&&(o=aH.TRACK),void 0===i&&(i=aB.defaultReport),n&&n.pixel_code&&this.getReporterId()!==n.pixel_code)return Promise.resolve(null);if(eG(this.reporterInfo,this.ttqOptions)){var a=eV(document.cookie);this.setCookieBasedSession(a[this.getReporterId()]),this.context.setPageCookieBasedSession(a[""])}return t.prototype.track.call(this,e,r,n,o,i)},e}(rn);function rc(t,e){return(rc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var rs=function(t){function e(){return t.apply(this,arguments)||this}e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,rc(e,t);var r=e.prototype;return r.getInstance=function(){return this.pixelPromise=Promise.resolve(this),this.pixelPromise},r.track=function(t,e,r){return void 0===e&&(e={}),void 0===r&&(r={}),tY(this.getReporterId(),"track",[t,e,r]),Promise.resolve(null)},e}(ra=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er(),ri(0,en(tm.ID)),ri(1,en(tm.Type)),ri(2,en(tm.IsOnsitePage)),ri(3,en(tK.CONTEXT)),ri(4,en(tm.Info)),ri(5,en(tK.TTQ_GLOBAL_OPTIONS)),ri(6,en(tK.REPORT_SERVICE)),ri(7,en(tm.Plugins)),ri(7,eo()),ri(8,en(tm.Rules)),ri(8,eo()),ri(9,en(tm.Options)),ri(9,eo())],ra));function ru(){ru=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function u(t,e,r,o){var i,a,c,s,u=Object._ttq_create((e&&e.prototype instanceof p?e:p).prototype);return n(u,"_invoke",{value:(i=t,a=r,c=new I(o||[]),s="suspendedStart",function(t,e){if("executing"===s)throw Error("Generator is already running");if("completed"===s){if("throw"===t)throw e;return T()}for(c.method=t,c.arg=e;;){var r=c.delegate;if(r){var n=function t(e,r){var n=r.method,o=e.iterator[n];if(void 0===o)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=void 0,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+n+"' method")),l;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,l;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=void 0),r.delegate=null,l):a:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,l)}(r,c);if(n){if(n===l)continue;return n}}if("next"===c.method)c.sent=c._sent=c.arg;else if("throw"===c.method){if("suspendedStart"===s)throw s="completed",c.arg;c.dispatchException(c.arg)}else"return"===c.method&&c.abrupt("return",c.arg);s="executing";var o=f(i,a,c);if("normal"===o.type){if(s=c.done?"completed":"suspendedYield",o.arg===l)continue;return{value:o.arg,done:c.done}}"throw"===o.type&&(s="completed",c.method="throw",c.arg=o.arg)}})}),u}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var l={};function p(){}function h(){}function d(){}var v={};s(v,i,function(){return this});var _=Object.getPrototypeOf,g=_&&_(_(O([])));g&&g!==e&&r.call(g,i)&&(v=g);var y=d.prototype=p.prototype=Object._ttq_create(v);function m(t){["next","throw","return"].forEach(function(e){s(t,e,function(t){return this._invoke(e,t)})})}function w(t,e){var o;n(this,"_invoke",{value:function(n,i){function a(){return new e(function(o,a){!function n(o,i,a,c){var s=f(t[o],t,i);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(l).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(n,i,o,a)})}return o=o?o.then(a,a):a()}})}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function b(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function O(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:T}}function T(){return{value:void 0,done:!0}}return h.prototype=d,n(y,"constructor",{value:d,configurable:!0}),n(d,"constructor",{value:h,configurable:!0}),h.displayName=s(d,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,s(t,c,"GeneratorFunction")),t.prototype=Object._ttq_create(y),t},t.awrap=function(t){return{__await:t}},m(w.prototype),s(w.prototype,a,function(){return this}),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(u(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then(function(t){return t.done?t.value:a.next()})},m(y),s(y,c,"Generator"),s(y,i,function(){return this}),s(y,"toString",function(){return"[object Generator]"}),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=O,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),b(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;b(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:O(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},t}function rf(t,e,r,n,o,i,a){try{var c=t[i](a),s=c.value}catch(t){r(t);return}c.done?e(s):Promise.resolve(s).then(n,o)}function rl(t){return function(){var e=this,r=arguments;return new Promise(function(n,o){var i=t.apply(e,r);function a(t){rf(i,n,o,a,c,"next",t)}function c(t){rf(i,n,o,a,c,"throw",t)}a(void 0)})}}function rp(t,e){return(rp=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var rh=function(t,e){return function(r,n){e(r,n,t)}},rd=function(t){function e(e,r,n,o,i,a,c,s,u,f,l,p,h,d,v,_,g,y,m,w,E){(b=t.call(this,e,i)||this).context=e,b.env=s,b.reporters=r,b.cookieService=a,b.reportService=i,b.consentService=c,b.adService=n,b.appService=o,b.historyObserver=w,b.autoAdvancedMatchingPlugin=f,b.callbackPlugin=l,b.identifyPlugin=p,b.monitorPlugin=u,b.shopifyPlugin=h,b.autoConfigPlugin=d,b.diagnosticsConsolePlugin=v,b.pangleCookieMatchingPlugin=_,b.eventBuilderPlugin=g,b.pagedataPlugin=m,b.enrichIpv6Plugin=y,b.runtimeMeasurementPlugin=E,b.historyObserver&&b.useObserver(b.historyObserver),b.autoAdvancedMatchingPlugin&&b.usePlugin(b.autoAdvancedMatchingPlugin),b.callbackPlugin&&b.usePlugin(b.callbackPlugin),b.identifyPlugin&&b.usePlugin(b.identifyPlugin),b.monitorPlugin&&b.usePlugin(b.monitorPlugin),b.shopifyPlugin&&b.usePlugin(b.shopifyPlugin),b.autoConfigPlugin&&b.usePlugin(b.autoConfigPlugin),b.diagnosticsConsolePlugin&&b.usePlugin(b.diagnosticsConsolePlugin),b.pangleCookieMatchingPlugin&&b.usePlugin(b.pangleCookieMatchingPlugin),b.eventBuilderPlugin&&b.usePlugin(b.eventBuilderPlugin),b.enrichIpv6Plugin&&b.usePlugin(b.enrichIpv6Plugin),b.runtimeMeasurementPlugin&&b.usePlugin(b.runtimeMeasurementPlugin),b.monitorPlugin&&(S.info.forEach(function(t){var e;null==(e=b.monitorPlugin)||e.info(t.event,t.detail,t.withoutJSB)}),S.error.forEach(function(t){var e;null==(e=b.monitorPlugin)||e.error(t.event,t.err,t.detail,t.withoutJSB)}),S.info=[],S.error=[]),b.dispatch(aM.INIT_START),b.pagedataPlugin&&b.usePlugin(b.pagedataPlugin),b.onPageLoaded(),b.onPageLeave();var b,I=ec(),O=I.url,T=I.referrer;return b.init(O,T),b.setPageInfo(O,T),b.dispatch(aM.INIT_END),b}e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,rp(e,t);var r,i=e.prototype;return i.initAdInfo=function(t,e){this.dispatch(aM.BEFORE_AD_INFO_INIT_START);var r=eu(n);if(r)return void this.initAdCache(r);if(tq()){var o=eO(t,e);o&&(o.creative_id&&o.log_extra||o.callback)&&(this.dispatch(aM.AD_INFO_INIT_START),el(n,o),this.setAdInfo(o),this.initOffsiteAdInfo(o));return}this.initBaseAdInfo(t,e)},i.initAdCache=function(t){this.dispatch(aM.AD_INFO_INIT_START),t.ad_info_from="cache",t.ad_info_status="fulfilled(cache)",this.setAdInfo(t),this.initOffsiteAdInfo(t)},i.initBaseAdInfo=function(t,e){var r=this;this.adService.webBridgeService.jsbridge&&this.dispatch(aM.AD_INFO_INIT_START),this.reportService.pushPreposition(rl(ru().mark(function n(){var o;return ru().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.prev=0,n.next=3,r.adService.getAdInfo(t,e);case 3:o=n.sent,r.context.setAdInfo(o),r.initOffsiteAdInfo(o),n.next=11;break;case 8:n.prev=8,n.t0=n.catch(0),A(aI.INIT_ERROR,n.t0,{extJSON:{position:"initAdInfo"}});case 11:case"end":return n.stop()}},n,null,[[0,8]])}))())},i.initOffsiteAdInfo=function(t){var e=eN(t,function(t){A(aI.INIT_ERROR,t,{extJSON:{position:"handleAdInfoOfficial"}})});this.context.setOffsiteAdInfo(e);var r=eT(t,this.env);this.context.setEnableAdTracking(r),this.dispatch(aM.AD_INFO_INIT_END,{extJSON:{enabledAdTracking:r}})},i.initAppInfo=function(t,e){var r=this,n=eu(o);if(n)return void this.context.setAppInfo(n);this.reportService.pushPreposition(rl(ru().mark(function n(){var o;return ru().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:return n.next=2,r.initBaseAppInfo(t,e);case 2:return o=n.sent,n.abrupt("return",o);case 4:case"end":return n.stop()}},n)}))())},r=rl(ru().mark(function t(e,r){var n;return ru().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.appService.getAppInfo(e,r);case 2:return n=t.sent,this.context.setAppInfo(n),t.abrupt("return",n);case 5:case"end":return t.stop()}},t,this)})),i.initBaseAppInfo=function(t,e){return r.apply(this,arguments)},i.initTestId=function(t,e){if(!this.context.getTestID()){var r=eq(t,eC("tt_test_id"),e);this.context.setTestID(r)}},i.initUserInfo=function(){this.setCookieInfo()},i.initLocalServiceInfo=function(){var t,e,r=eu(p);if(r)return void this.context.setUserInfoWithoutIdentifyPlugin(((t={})[p]=r,t));var n=ec(),o=n.url,i=n.referrer,a=ea(h[location.hostname]?h[location.hostname]:p,o,i);a&&(el(p,a),this.context.setUserInfoWithoutIdentifyPlugin(((e={})[p]=a,e)))},i.setPageIndex=function(t){t&&eD(t)},i.instance=function(t){this.beforeAPIExecution();var e=this.getReporter(t);return e||new rs(t,aj.PIXEL_CODE,{value:!1},this.context,{pixelCode:t},{},{})},i.instances=function(){return this.beforeAPIExecution(),this.reporters},i.page=function(e){this.beforeAPIExecution();var r=ec(),n=r.url,o=r.referrer;t.prototype.page.call(this,Object.assign({url:(null==e?void 0:e.page)||n,referrer:(null==e?void 0:e.referrer)||o},e))},i.track=function(e,r,n){void 0===r&&(r={}),void 0===n&&(n={});try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var o={taskName:window.ttq._pf_tn,functionName:"web_track_handler",start:performance.now()}}catch(t){}this.beforeAPIExecution();var i=n.pixel_code;if(void 0===i&&t.prototype.track.call(this,e,r,n),void 0!==i){var a=this.instance(i);a instanceof rs||a.track(e,r,n)}try{window.ttq&&window.ttq._ppf&&(o.end=performance.now(),window.ttq._ppf.push(o))}catch(t){}},i.identify=function(e,r){this.beforeAPIExecution(),t.prototype.identify.call(this,e,r)},i.setAdInfo=function(t){this.context.setAdInfo(t)},i.enableFirstPartyCookie=function(t){this.cookieService.enableFirstPartyCookie(t),t&&this.setCookieInfo()},i.enableCookie=function(){this.cookieService.enableFirstPartyCookie(!0),this.setCookieInfo(),this.cookieService.enableCookie()},i.disableCookie=function(){this.cookieService.disableCookie(),this.context.setUserInfoWithoutIdentifyPlugin({anonymous_id:void 0}),this.disablePangleCookie()},i.holdConsent=function(){this.consentService.setConsentMode(aC.HOLD)},i.revokeConsent=function(){this.consentService.setConsentMode(aC.REVOKE)},i.grantConsent=function(){this.consentService.setConsentMode(aC.GRANT)},i.disablePangleCookie=function(){this.pangleCookieMatchingPlugin&&this.pangleCookieMatchingPlugin.disablePangleCookie()},i.setAnonymousId=function(t){this.cookieService.setAnonymousId(t),this.initUserInfo()},i.resetCookieExpires=function(){this.cookieService.resetExpires()},i.setCookieInfo=function(){if(this.cookieService.canUseCookie()){var t=this.cookieService.getAnonymousId();t&&this.context.setUserInfoWithoutIdentifyPlugin({anonymous_id:t})}},i.onPageLoaded=function(){var t=this;window.addEventListener("load",function(){t.dispatch(aM.PAGE_DID_LOAD)},{once:!0})},i.onPageLeave=function(){var t=this,e=tt(function(){var e=Date.now();t.dispatch(aM.PAGE_WILL_LEAVE,e),t.consentService.updateCache()});document.addEventListener("visibilitychange",function(){"hidden"===document.visibilityState&&e()}),"onpagehide"in window&&window.addEventListener("pagehide",function(){e()}),window.addEventListener("beforeunload",function(){return e},{once:!0})},i.beforeAPIExecution=function(){try{var t,e;t=this.context,ec().url===t.getPageInfo().url||this.setPageInfo(ec().url),e=this.context,eC(a)===e.getUserInfo().anonymous_id||e7(this.context)}catch(t){A(aI.API_ERROR,t,{extJSON:{position:"beforeAPIExecution"}})}},i.loadPixel=function(t,e){if(t){if(this.reporters.find(function(e){return e.getReporterId()===t}))return void x(aU.DUPLICATE_PIXEL_CODE);y().load(t,e||{})}},e}(eW),rv=rd=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er(),rh(0,en(tK.CONTEXT)),rh(1,en(tK.TTQ_REPORTERS)),rh(2,en(tK.AD_SERVICE)),rh(3,en(tK.APP_SERVICE)),rh(4,en(tK.REPORT_SERVICE)),rh(5,en(tK.COOKIE_SERVICE)),rh(6,en(tK.CONSENT_SERVICE)),rh(7,en(tK.ENV)),rh(8,en(tK.MONITOR_PLUGIN)),rh(8,eo()),rh(9,en(tK.AUTO_ADVANCED_MATCHING_PLUGIN)),rh(9,eo()),rh(10,en(tK.CALLBACK_PLUGIN)),rh(10,eo()),rh(11,en(tK.IDENTIFY_PLUGIN)),rh(11,eo()),rh(12,en(tK.SHOPIFY_PLUGIN)),rh(12,eo()),rh(13,en(tK.AUTO_CONFIG_PLUGIN)),rh(13,eo()),rh(14,en(tK.DIAGNOSTICS_CONSOLE_PLUGIN)),rh(14,eo()),rh(15,en(tK.PANGLE_COOKIE_MATCHING_PLUGIN)),rh(15,eo()),rh(16,en(tK.EVENT_BUILDER_PLUGIN)),rh(16,eo()),rh(17,en(tK.ENRICH_IPV6_PLUGIN)),rh(17,eo()),rh(18,en(tK.PAGEDATA_PLUGIN)),rh(18,eo()),rh(19,en(tK.HISTORY_OBSERVER)),rh(19,eo()),rh(20,en(tK.RUNTIME_MEASUREMENT_PLUGIN)),rh(20,eo())],rd);function r_(){return(r_=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var rg=function(){function t(t){this.userFormatInfo={},this.userFormatInfoV2={},this.enableAdTracking=!0,this.offsiteAdInfo={},this.tt_test_id="",this.signalDiagnosticLabels=r_({},F),this.pageCookieBasedSession=eH(),this.init(t)}var e=t.prototype;return e.init=function(t){this.userInfo={},this.adInfo={},this.appInfo={},this.pageInfo={url:"",referrer:""},this.pageSign={sessionId:"",pageId:""},this.libraryInfo=t},e.getAllData=function(){return{userInfo:this.userInfo,adInfo:this.adInfo,appInfo:this.appInfo,libraryInfo:this.libraryInfo,pageInfo:this.pageInfo,pageSign:this.pageSign,signalType:this.signalType,userFormatInfo:this.userFormatInfo,userFormatInfoV2:this.userFormatInfoV2,enableAdTracking:this.enableAdTracking,offsiteAdInfo:this.offsiteAdInfo,tt_test_id:this.tt_test_id}},e.getLibraryInfo=function(){return this.libraryInfo},e.setSignalType=function(t){this.signalType=t},e.getSignalType=function(){return this.signalType},e.setTestID=function(t){this.tt_test_id=t},e.getTestID=function(){return this.tt_test_id},e.setEnableAdTracking=function(t){this.enableAdTracking=t},e.getEnableAdTracking=function(){return this.enableAdTracking},e.setOffsiteAdInfo=function(t){this.offsiteAdInfo=Object.assign({},this.offsiteAdInfo,t)},e.getOffsiteAdInfo=function(){return this.offsiteAdInfo},e.getUserFormatInfo=function(){return this.userFormatInfo},e.setUserFormatInfo=function(t){void 0===t&&(t={}),Object.assign(this.userFormatInfo,t)},e.getUserFormatInfoV2=function(){return this.userFormatInfoV2},e.setUserFormatInfoV2=function(t){void 0===t&&(t={}),Object.assign(this.userFormatInfoV2,t)},e.setUserInfo=function(t){void 0===t&&(t={}),Object.assign(this.userInfo,t)},e.setUserInfoWithoutIdentifyPlugin=function(t){t&&Object.assign(this.userInfo,t)},e.getUserInfo=function(){return this.userInfo},e.getAdInfo=function(){return this.adInfo},e.setAdInfo=function(t){t&&(this.adInfo?this.adInfo=Object.assign({},this.adInfo,t):this.adInfo=t)},e.getAppInfo=function(){return this.appInfo},e.setAppInfo=function(t){t&&(this.appInfo=Object.assign({},this.appInfo,t))},e.getPageInfo=function(){return this.pageInfo},e.getPageSign=function(){return this.pageSign},e.setPageInfo=function(t,e){var r=r_({},this.pageInfo),n=r_({},this.pageSign);if(r.url!==t){var o=r.url;if(void 0!==r.url&&(r.referrer=r.url),void 0!==e&&(r.referrer=e),void 0!==n.pageIndex){var i=n.pageIndex,a=i.index,c=i.sub,s=i.main;n.pageIndex={index:++a,sub:++c,main:s}}return r.url=t,this.pageInfo=r,this.pageSign=n,{from:o,pageIndex:n.pageIndex}}},e.setPageInfoData=function(t){this.pageInfo=Object.assign({},this.pageInfo,t)},e.getSessionIdFromCache=function(){return null},e.setSessionIdToCache=function(t){},e.setSignalDiagnosticLabels=function(t){Object.assign(this.signalDiagnosticLabels,t)},e.getSignalDiagnosticLabels=function(){return this.signalDiagnosticLabels},e.getPageId=function(t){return void 0===t&&(t=""+Date.now()),t+"-"+te(5)},e.getPageViewId=function(){var t=this.pageSign,e=t.pageId,r=t.pageIndex;return""+e+(r?"."+r.main+"."+r.sub:"")},e.getVariationId=function(){return""},e.getVids=function(){return""},e.isLegacyPixel=function(t){return!1},e.initPageSign=function(){var t=this.getSessionIdFromCache();null===t&&(t=V("sessionId"),this.setSessionIdToCache(t));var e={sessionId:t,pageId:V("pageId")};this.pageSign=e},e.getPageCookieBasedSession=function(){return this.pageCookieBasedSession},e.setPageCookieBasedSession=function(t){t&&(this.pageCookieBasedSession=t)},t}();function ry(t,e){return(ry=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}rg=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er()],rg);var rm=function(t,e){return function(r,n){e(r,n,t)}},rw=function(t){function e(e,r,n,o,i){var a;return(a=t.call(this,e)||this).setSignalType(i||aD.OFFSITE),a.pageSign={sessionId:"",pageId:"",variationId:"",vids:"",pageIndex:{main:-1,sub:-1,index:-1}},a.legacy=n.legacy||[],a.variationId=n.variation_id||"",a.vids=n.vids||"",a.serverUniqueId=n.server_unqiue_id||"",a.ttqOptions=n,a.reportService=r,a.initPageSign(),tI(o)&&tR()&&(a.enableAdTracking=!1),a.data=function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(a),a}e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,ry(e,t);var r=e.prototype;return r.getSessionIdFromCache=function(){var t=null;try{t=JSON.parse(sessionStorage.getItem(c)||"")}catch(t){}return t},r.setSessionIdToCache=function(t){el(c,t)},r.getVariationId=function(){return this.variationId},r.getVids=function(){return this.vids},r.isLegacyPixel=function(t){return I(t,this.legacy)},r.assignPageInfo=function(t){Object.assign(this.pageInfo,t)},r.getSessionIndex=function(){var t={main:-1,sub:-1,index:-1};try{var e=JSON.parse(sessionStorage.getItem(s)||"{}");if(e)return Object.assign({},t,e)}catch(t){}return t},r.setUserInfo=function(t){var e=this;if(void 0===t&&(t={}),0!==Object.keys(t).length){var r={};Object.entries(t).forEach(function(t){var n,o=t[0],i=t[1];if(i){if(o===G)return void e.setUserInfoWithoutIdentifyPlugin(((n={})[G]=i,n));r[o]=String(i).trim()}});var n=y(),o=null==n?void 0:n.getPlugin("Identify");o&&this.reportService.pushPreposition(o.handleUserProperties(r,t).then(function(t){void 0===t&&(t={});try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var n={taskName:window.ttq._pf_tn||"identify_after_encryption",functionName:window.ttq._pf_tn&&"identify_after_encryption",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="identify_after_encryption")}}catch(t){}var i=t,a=i.userProperties,c=i.userDataFormat,s=i.userDataFormatV2;if(a){Object.assign(e.userInfo,a);var u=e.getUserFormatInfo()||{},f=e.getUserFormatInfoV2()||{},l=e.getSignalDiagnosticLabels()||{};if(e.setUserFormatInfo(Object.assign({},u,c)),e.setUserFormatInfoV2(Object.assign({},f,s)),e.setSignalDiagnosticLabels(Object.assign({},l,t.identifierLabel||{})),0===Object.keys(e.userInfo).length||1===Object.keys(r).length&&Object.keys(r).includes("external_id"))return;var p=o.reporters[0]||null,h=p?Object.keys(Object.assign({},p.getUserInfo(aS.Manual),p.getUserInfo(aS.Auto),p.getUserInfo(aS.EBManual))):[];p&&h.length&&p.track("EnrichAM",{},{},aH.TRACK)}try{window.ttq&&window.ttq._ppf&&(n.end=performance.now(),window.ttq._ppf.push(n),"identify_after_encryption"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}}).catch(function(t){A(aI.API_ERROR,t,{extJSON:{api:"identify"}})}))}},r.initPageSign=function(){var t,e,r=this.getSessionIdFromCache();null===r&&(r=(t=this.serverUniqueId)?""+t+"::"+te(20):V("sessionId"),this.setSessionIdToCache(r));var n=this.getPageId((e=r)?e.split("::")[0]:""),o=this.getVariationId(),i=this.getSessionIndex();i.main++,this.pageSign={sessionId:r,pageId:n,variationId:o,pageIndex:i}},r.setPageCookieBasedSession=function(e){var r=re("",e,this.ttqOptions);t.prototype.setPageCookieBasedSession.call(this,r)},e}(rg);rw=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er(),rm(0,en(tm.WebLibraryInfo)),rm(1,en(tK.REPORT_SERVICE)),rm(2,en(tK.TTQ_GLOBAL_OPTIONS)),rm(3,en(tK.ENV)),rm(3,eo()),rm(4,en(tm.SignalType)),rm(4,eo())],rw);var rE=String.fromCharCode.bind(String),rb=Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="),rI=function(t){for(var e,r,n,o,i="",a=t.length%3,c=0;c<t.length;){if((r=t.charCodeAt(c++))>255||(n=t.charCodeAt(c++))>255||(o=t.charCodeAt(c++))>255)throw TypeError("invalid character found");i+=rb[(e=r<<16|n<<8|o)>>18&63]+rb[e>>12&63]+rb[e>>6&63]+rb[63&e]}return a?i.slice(0,a-3)+"===".substring(a):i},rO=function(t){if(t.length<2){var e=t.charCodeAt(0);return e<128?t:e<2048?rE(192|e>>>6)+rE(128|63&e):rE(224|e>>>12&15)+rE(128|e>>>6&63)+rE(128|63&e)}var r=65536+(t.charCodeAt(0)-55296)*1024+(t.charCodeAt(1)-56320);return rE(240|r>>>18&7)+rE(128|r>>>12&63)+rE(128|r>>>6&63)+rE(128|63&r)},rT=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,rN=function(t){return rI(JSON.stringify(t).replace(rT,rO)).replace(/=/g,"").replace(/[+\/]/g,function(t){return"+"===t?"-":"_"})};function rR(){rR=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function u(t,e,r,o){var i,a,c,s,u=Object._ttq_create((e&&e.prototype instanceof p?e:p).prototype);return n(u,"_invoke",{value:(i=t,a=r,c=new I(o||[]),s="suspendedStart",function(t,e){if("executing"===s)throw Error("Generator is already running");if("completed"===s){if("throw"===t)throw e;return T()}for(c.method=t,c.arg=e;;){var r=c.delegate;if(r){var n=function t(e,r){var n=r.method,o=e.iterator[n];if(void 0===o)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=void 0,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+n+"' method")),l;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,l;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=void 0),r.delegate=null,l):a:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,l)}(r,c);if(n){if(n===l)continue;return n}}if("next"===c.method)c.sent=c._sent=c.arg;else if("throw"===c.method){if("suspendedStart"===s)throw s="completed",c.arg;c.dispatchException(c.arg)}else"return"===c.method&&c.abrupt("return",c.arg);s="executing";var o=f(i,a,c);if("normal"===o.type){if(s=c.done?"completed":"suspendedYield",o.arg===l)continue;return{value:o.arg,done:c.done}}"throw"===o.type&&(s="completed",c.method="throw",c.arg=o.arg)}})}),u}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var l={};function p(){}function h(){}function d(){}var v={};s(v,i,function(){return this});var _=Object.getPrototypeOf,g=_&&_(_(O([])));g&&g!==e&&r.call(g,i)&&(v=g);var y=d.prototype=p.prototype=Object._ttq_create(v);function m(t){["next","throw","return"].forEach(function(e){s(t,e,function(t){return this._invoke(e,t)})})}function w(t,e){var o;n(this,"_invoke",{value:function(n,i){function a(){return new e(function(o,a){!function n(o,i,a,c){var s=f(t[o],t,i);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(l).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(n,i,o,a)})}return o=o?o.then(a,a):a()}})}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function b(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function O(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:T}}function T(){return{value:void 0,done:!0}}return h.prototype=d,n(y,"constructor",{value:d,configurable:!0}),n(d,"constructor",{value:h,configurable:!0}),h.displayName=s(d,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,s(t,c,"GeneratorFunction")),t.prototype=Object._ttq_create(y),t},t.awrap=function(t){return{__await:t}},m(w.prototype),s(w.prototype,a,function(){return this}),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(u(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then(function(t){return t.done?t.value:a.next()})},m(y),s(y,c,"Generator"),s(y,i,function(){return this}),s(y,"toString",function(){return"[object Generator]"}),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=O,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),b(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;b(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:O(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},t}function rS(t,e,r,n,o,i,a){try{var c=t[i](a),s=c.value}catch(t){r(t);return}c.done?e(s):Promise.resolve(s).then(n,o)}var rP=function(){function t(t,e,r){this.httpService=t,this.bridgeService=e,this.reportPreTasks=r}var e,r,n=t.prototype;return n.pushPreposition=function(t){this.reportPreTasks.push(t)},e=rR().mark(function t(e,r,n,o){return rR().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",Promise.resolve());case 1:case"end":return t.stop()}},t)}),r=function(){var t=this,r=arguments;return new Promise(function(n,o){var i=e.apply(t,r);function a(t){rS(i,n,o,a,c,"next",t)}function c(t){rS(i,n,o,a,c,"throw",t)}a(void 0)})},n.report=function(t,e,n,o){return r.apply(this,arguments)},t}();rP=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er()],rP);var rA=function(t,e){var r=new URL(t);return Object.keys(e).forEach(function(t){var n=e[t].toJSON?e[t].toJSON():String(e[t]);r.searchParams.set(t,n)}),r.toString()},rx=function(t){var e=Array(t.length),r=0;return new Promise(function(n,o){for(var i=function(o){var i=t[o];i&&"function"==typeof i.then?i.then(function(i){e[o]={status:"fulfilled",value:i},++r===t.length&&n(e)}).catch(function(i){e[o]={status:"rejected",reason:i},++r===t.length&&n(e)}):(e[o]={status:"fulfilled",value:i},++r===t.length&&n(e))},a=0;a<t.length;a++)i(a)})};(ae=aW||(aW={}))[ae.PENDING=0]="PENDING",ae[ae.FULFILLED=1]="FULFILLED";var rL=function(){function t(){this.promiseQueue=[],this.status=aW.FULFILLED}var e=t.prototype;return e.push=function(t){this.status=aW.PENDING,this.promiseQueue.push(t)},e.then=function(t){var e,r=this;return this.status===aW.FULFILLED?Promise.resolve(t()):(e=this.promiseQueue,"function"==typeof Promise.allSettled?Promise.allSettled(e):rx(e)).then(function(){return r.status=aW.FULFILLED,t()})},t}();function rC(){rC=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function u(t,e,r,o){var i,a,c,s,u=Object._ttq_create((e&&e.prototype instanceof p?e:p).prototype);return n(u,"_invoke",{value:(i=t,a=r,c=new I(o||[]),s="suspendedStart",function(t,e){if("executing"===s)throw Error("Generator is already running");if("completed"===s){if("throw"===t)throw e;return T()}for(c.method=t,c.arg=e;;){var r=c.delegate;if(r){var n=function t(e,r){var n=r.method,o=e.iterator[n];if(void 0===o)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=void 0,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+n+"' method")),l;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,l;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=void 0),r.delegate=null,l):a:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,l)}(r,c);if(n){if(n===l)continue;return n}}if("next"===c.method)c.sent=c._sent=c.arg;else if("throw"===c.method){if("suspendedStart"===s)throw s="completed",c.arg;c.dispatchException(c.arg)}else"return"===c.method&&c.abrupt("return",c.arg);s="executing";var o=f(i,a,c);if("normal"===o.type){if(s=c.done?"completed":"suspendedYield",o.arg===l)continue;return{value:o.arg,done:c.done}}"throw"===o.type&&(s="completed",c.method="throw",c.arg=o.arg)}})}),u}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var l={};function p(){}function h(){}function d(){}var v={};s(v,i,function(){return this});var _=Object.getPrototypeOf,g=_&&_(_(O([])));g&&g!==e&&r.call(g,i)&&(v=g);var y=d.prototype=p.prototype=Object._ttq_create(v);function m(t){["next","throw","return"].forEach(function(e){s(t,e,function(t){return this._invoke(e,t)})})}function w(t,e){var o;n(this,"_invoke",{value:function(n,i){function a(){return new e(function(o,a){!function n(o,i,a,c){var s=f(t[o],t,i);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(l).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(n,i,o,a)})}return o=o?o.then(a,a):a()}})}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function b(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function O(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:T}}function T(){return{value:void 0,done:!0}}return h.prototype=d,n(y,"constructor",{value:d,configurable:!0}),n(d,"constructor",{value:h,configurable:!0}),h.displayName=s(d,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,s(t,c,"GeneratorFunction")),t.prototype=Object._ttq_create(y),t},t.awrap=function(t){return{__await:t}},m(w.prototype),s(w.prototype,a,function(){return this}),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(u(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then(function(t){return t.done?t.value:a.next()})},m(y),s(y,c,"Generator"),s(y,i,function(){return this}),s(y,"toString",function(){return"[object Generator]"}),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=O,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),b(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;b(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:O(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},t}function rk(t,e,r,n,o,i,a){try{var c=t[i](a),s=c.value}catch(t){r(t);return}c.done?e(s):Promise.resolve(s).then(n,o)}function rD(t){return function(){var e=this,r=arguments;return new Promise(function(n,o){var i=t.apply(e,r);function a(t){rk(i,n,o,a,c,"next",t)}function c(t){rk(i,n,o,a,c,"throw",t)}a(void 0)})}}function rj(t,e){return(rj=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var rM=function(t,e){return function(r,n){e(r,n,t)}},rF=function(t){function e(e,r,n,o){var i;return(i=t.call(this,e,r,new rL)||this).supportSendAnalyticsEvent=!0,i.consentService=n,i.consentService.on("queue",function(t){t.forEach(function(t){var e=t.url,r=t.data,n=t.type;i.report(e,r,n,aY.P0)})}),i.env=o,i}e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,rj(e,t);var r,n,o,i=e.prototype;return r=rD(rC().mark(function t(e,r,n){var o,i,a,c,s,u,f;return rC().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(this.bridgeService.jsbridge){t.next=2;break}return t.abrupt("return");case 2:return u=!!(s=r).context&&(null==(i=null==(o=s.context)?void 0:o.ad)?void 0:i.ad_info_status)==="timeout",f={},t.prev=5,t.next=8,this.bridgeService.send(s,u);case 8:if(!(!(f=t.sent)||1!==f.code)){t.next=11;break}throw Error("[fetch bridge] sendLog error: code "+(f&&f.code)+", data: "+(f&&JSON.stringify(f)));case 11:return Q(s.event)&&P(aI.JSB_SEND,{pixelCode:null==(a=s.context.pixel)?void 0:a.code,app_name:tx()?"ultralite":"",extJSON:{event:s.event,event_id:s.event_id,need_inject_ad_info:u}}),t.abrupt("return",f);case 15:t.prev=15,t.t0=t.catch(5),Q(s.event)&&A(aI.JSB_ERROR,t.t0,{pixelCode:null==(c=s.context.pixel)?void 0:c.code,custom_name:"sendReportData",custom_enum:f&&f.code?""+f.code:"non",app_name:tj()||"",extJSON:{position:"sendReportData"}}),tx()&&tN()&&this.sendHttpReport(e,s,n);case 19:case"end":return t.stop()}},t,this,[[5,15]])})),i.send=function(t,e,n){return r.apply(this,arguments)},n=rD(rC().mark(function t(e,r,n,o,i){var a;return rC().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return void 0===o&&(o=!0),t.next=3,this.httpService.send(e,r,i);case 3:t.sent||this.httpService.sendByImage(e,{analytics_message:n}),o&&P(aI.HTTP_SEND,{pixelCode:null==(a=r.context.pixel)?void 0:a.code,extJSON:{event:r.event,event_id:r.event_id}});case 6:case"end":return t.stop()}},t,this)})),i.sendHttpReport=function(t,e,r,o,i){return n.apply(this,arguments)},i.beforeReport=function(t,e,r){void 0===r&&(r=aB.defaultReport);var n=this.consentService.getConsentMode();return n!==aC.REVOKE&&(n!==aC.HOLD||(this.consentService.cacheReportTask(t,e,r),!1))},o=rD(rC().mark(function t(e,r,n,o){var i,a,c,s;return rC().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===n&&(n=aB.defaultReport),void 0===o&&(o=aY.P0),o===aY.P0){t.next=8;break}return t.next=5,ts(0);case 5:return t.next=7,this.report(e,r,n,aY.P0);case 7:case 15:return t.abrupt("return",t.sent);case 8:if(this.beforeReport(e,r,n)){t.next=11;break}return t.abrupt("return");case 11:if(i=rN(r),!(n===aB.defaultReport&&this.bridgeService.jsbridge)){t.next=16;break}return t.next=15,this.send(e,r,i);case 16:if(!(n===aB.httpReport&&this.bridgeService.jsbridge&&tI(this.env)&&!tA()&&this.supportSendAnalyticsEvent)){t.next=35;break}return a=e,t.prev=18,a=new URL(e).pathname,t.next=22,this.bridgeService.sendAnalyticsEvent({path:a,method:"POST",data:r});case 22:if(s=Error("sendAnalyticsEvent not support: code "+(c=t.sent)+", path: "+a+", data: "+JSON.stringify(r)),null!=c&&-2!==c){t.next=27;break}throw this.supportSendAnalyticsEvent=!1,s;case 27:if(1!==c){t.next=29;break}return t.abrupt("return");case 29:throw s;case 32:t.prev=32,t.t0=t.catch(18),A(aI.CUSTOM_ERROR,t.t0,{custom_name:"sendAnalyticsEvent",custom_enum:String(c)},!0);case 35:this.sendHttpReport(e,r,i,!!(Q(r.event)&&$(r)),r.action?3:void 0);case 36:case"end":return t.stop()}},t,this,[[18,32]])})),i.report=function(t,e,r,n){return o.apply(this,arguments)},i.reportFL=function(t){this.bridgeService.jsbridge&&this.bridgeService.updateWebFlData(t)},e}(rP);function rq(){rq=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function u(t,e,r,o){var i,a,c,s,u=Object._ttq_create((e&&e.prototype instanceof p?e:p).prototype);return n(u,"_invoke",{value:(i=t,a=r,c=new I(o||[]),s="suspendedStart",function(t,e){if("executing"===s)throw Error("Generator is already running");if("completed"===s){if("throw"===t)throw e;return T()}for(c.method=t,c.arg=e;;){var r=c.delegate;if(r){var n=function t(e,r){var n=r.method,o=e.iterator[n];if(void 0===o)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=void 0,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+n+"' method")),l;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,l;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=void 0),r.delegate=null,l):a:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,l)}(r,c);if(n){if(n===l)continue;return n}}if("next"===c.method)c.sent=c._sent=c.arg;else if("throw"===c.method){if("suspendedStart"===s)throw s="completed",c.arg;c.dispatchException(c.arg)}else"return"===c.method&&c.abrupt("return",c.arg);s="executing";var o=f(i,a,c);if("normal"===o.type){if(s=c.done?"completed":"suspendedYield",o.arg===l)continue;return{value:o.arg,done:c.done}}"throw"===o.type&&(s="completed",c.method="throw",c.arg=o.arg)}})}),u}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var l={};function p(){}function h(){}function d(){}var v={};s(v,i,function(){return this});var _=Object.getPrototypeOf,g=_&&_(_(O([])));g&&g!==e&&r.call(g,i)&&(v=g);var y=d.prototype=p.prototype=Object._ttq_create(v);function m(t){["next","throw","return"].forEach(function(e){s(t,e,function(t){return this._invoke(e,t)})})}function w(t,e){var o;n(this,"_invoke",{value:function(n,i){function a(){return new e(function(o,a){!function n(o,i,a,c){var s=f(t[o],t,i);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(l).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(n,i,o,a)})}return o=o?o.then(a,a):a()}})}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function b(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function O(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:T}}function T(){return{value:void 0,done:!0}}return h.prototype=d,n(y,"constructor",{value:d,configurable:!0}),n(d,"constructor",{value:h,configurable:!0}),h.displayName=s(d,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,s(t,c,"GeneratorFunction")),t.prototype=Object._ttq_create(y),t},t.awrap=function(t){return{__await:t}},m(w.prototype),s(w.prototype,a,function(){return this}),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(u(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then(function(t){return t.done?t.value:a.next()})},m(y),s(y,c,"Generator"),s(y,i,function(){return this}),s(y,"toString",function(){return"[object Generator]"}),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=O,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),b(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;b(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:O(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},t}function rU(t,e,r,n,o,i,a){try{var c=t[i](a),s=c.value}catch(t){r(t);return}c.done?e(s):Promise.resolve(s).then(n,o)}rF=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er(),rM(0,en(tK.HTTP_SERVICE)),rM(1,en(tK.BRIDGE_SERVICE)),rM(2,en(tK.CONSENT_SERVICE)),rM(3,en(tK.ENV)),rM(3,eo())],rF);var rG=function(){function t(t){this.webBridgeService=t}var e,r,o=t.prototype;return e=rq().mark(function t(e,r){var o,i,a;return rq().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(o=this.getAdInfoFromURL(e,r),!this.webBridgeService.jsbridge){t.next=7;break}return t.next=4,this.webBridgeService.getAdInfo();case 4:t.t0=t.sent,t.next=8;break;case 7:t.t0={};case 8:return i=t.t0,(a=Object.assign({},o,i))&&(a.creative_id&&a.log_extra||a.callback)&&el(n,a),t.abrupt("return",a);case 12:case"end":return t.stop()}},t,this)}),r=function(){var t=this,r=arguments;return new Promise(function(n,o){var i=e.apply(t,r);function a(t){rU(i,n,o,a,c,"next",t)}function c(t){rU(i,n,o,a,c,"throw",t)}a(void 0)})},o.getAdInfo=function(t,e){return r.apply(this,arguments)},o.getAdInfoFromURL=function(t,e){return eO(t,e)},t}();function rH(){rH=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function u(t,e,r,o){var i,a,c,s,u=Object._ttq_create((e&&e.prototype instanceof p?e:p).prototype);return n(u,"_invoke",{value:(i=t,a=r,c=new I(o||[]),s="suspendedStart",function(t,e){if("executing"===s)throw Error("Generator is already running");if("completed"===s){if("throw"===t)throw e;return T()}for(c.method=t,c.arg=e;;){var r=c.delegate;if(r){var n=function t(e,r){var n=r.method,o=e.iterator[n];if(void 0===o)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=void 0,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+n+"' method")),l;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,l;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=void 0),r.delegate=null,l):a:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,l)}(r,c);if(n){if(n===l)continue;return n}}if("next"===c.method)c.sent=c._sent=c.arg;else if("throw"===c.method){if("suspendedStart"===s)throw s="completed",c.arg;c.dispatchException(c.arg)}else"return"===c.method&&c.abrupt("return",c.arg);s="executing";var o=f(i,a,c);if("normal"===o.type){if(s=c.done?"completed":"suspendedYield",o.arg===l)continue;return{value:o.arg,done:c.done}}"throw"===o.type&&(s="completed",c.method="throw",c.arg=o.arg)}})}),u}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var l={};function p(){}function h(){}function d(){}var v={};s(v,i,function(){return this});var _=Object.getPrototypeOf,g=_&&_(_(O([])));g&&g!==e&&r.call(g,i)&&(v=g);var y=d.prototype=p.prototype=Object._ttq_create(v);function m(t){["next","throw","return"].forEach(function(e){s(t,e,function(t){return this._invoke(e,t)})})}function w(t,e){var o;n(this,"_invoke",{value:function(n,i){function a(){return new e(function(o,a){!function n(o,i,a,c){var s=f(t[o],t,i);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(l).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(n,i,o,a)})}return o=o?o.then(a,a):a()}})}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function b(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function O(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:T}}function T(){return{value:void 0,done:!0}}return h.prototype=d,n(y,"constructor",{value:d,configurable:!0}),n(d,"constructor",{value:h,configurable:!0}),h.displayName=s(d,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,s(t,c,"GeneratorFunction")),t.prototype=Object._ttq_create(y),t},t.awrap=function(t){return{__await:t}},m(w.prototype),s(w.prototype,a,function(){return this}),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(u(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then(function(t){return t.done?t.value:a.next()})},m(y),s(y,c,"Generator"),s(y,i,function(){return this}),s(y,"toString",function(){return"[object Generator]"}),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=O,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),b(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;b(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:O(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},t}function rV(t,e,r,n,o,i,a){try{var c=t[i](a),s=c.value}catch(t){r(t);return}c.done?e(s):Promise.resolve(s).then(n,o)}rG=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er(),(ar=en(tK.BRIDGE_SERVICE),function(t,e){ar(t,e,0)})],rG);var rB=function(){function t(t){this.webBridgeService=t}var e,r,n=t.prototype;return e=rH().mark(function t(e,r){var n,i,a,c;return rH().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if((n=this.getAppInfoFromURL(e,r)).platform=tO(),!tN()){t.next=10;break}return t.next=5,tT();case 5:a=(i=t.sent).model,c=i.platformVersion,n.device_model=a,n.android_version=c;case 10:return"{}"!==JSON.stringify(n)&&el(o,n),t.abrupt("return",n);case 12:case"end":return t.stop()}},t,this)}),r=function(){var t=this,r=arguments;return new Promise(function(n,o){var i=e.apply(t,r);function a(t){rV(i,n,o,a,c,"next",t)}function c(t){rV(i,n,o,a,c,"throw",t)}a(void 0)})},n.getAppInfo=function(t,e){return r.apply(this,arguments)},n.getAppInfoFromURL=function(t,e){try{var r=ea(eI,t,e),n=r&&JSON.parse(r),o=n.device_id,i=n.uid;return{device_id:o,user_id:i}}catch(t){return{}}},t}();rB=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er(),(an=en(tK.BRIDGE_SERVICE),function(t,e){an(t,e,0)})],rB);var rY="ad_analytics_msg";function rK(){rK=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function u(t,e,r,o){var i,a,c,s,u=Object._ttq_create((e&&e.prototype instanceof p?e:p).prototype);return n(u,"_invoke",{value:(i=t,a=r,c=new I(o||[]),s="suspendedStart",function(t,e){if("executing"===s)throw Error("Generator is already running");if("completed"===s){if("throw"===t)throw e;return T()}for(c.method=t,c.arg=e;;){var r=c.delegate;if(r){var n=function t(e,r){var n=r.method,o=e.iterator[n];if(void 0===o)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=void 0,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+n+"' method")),l;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,l;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=void 0),r.delegate=null,l):a:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,l)}(r,c);if(n){if(n===l)continue;return n}}if("next"===c.method)c.sent=c._sent=c.arg;else if("throw"===c.method){if("suspendedStart"===s)throw s="completed",c.arg;c.dispatchException(c.arg)}else"return"===c.method&&c.abrupt("return",c.arg);s="executing";var o=f(i,a,c);if("normal"===o.type){if(s=c.done?"completed":"suspendedYield",o.arg===l)continue;return{value:o.arg,done:c.done}}"throw"===o.type&&(s="completed",c.method="throw",c.arg=o.arg)}})}),u}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var l={};function p(){}function h(){}function d(){}var v={};s(v,i,function(){return this});var _=Object.getPrototypeOf,g=_&&_(_(O([])));g&&g!==e&&r.call(g,i)&&(v=g);var y=d.prototype=p.prototype=Object._ttq_create(v);function m(t){["next","throw","return"].forEach(function(e){s(t,e,function(t){return this._invoke(e,t)})})}function w(t,e){var o;n(this,"_invoke",{value:function(n,i){function a(){return new e(function(o,a){!function n(o,i,a,c){var s=f(t[o],t,i);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(l).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(n,i,o,a)})}return o=o?o.then(a,a):a()}})}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function b(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function O(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:T}}function T(){return{value:void 0,done:!0}}return h.prototype=d,n(y,"constructor",{value:d,configurable:!0}),n(d,"constructor",{value:h,configurable:!0}),h.displayName=s(d,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,s(t,c,"GeneratorFunction")),t.prototype=Object._ttq_create(y),t},t.awrap=function(t){return{__await:t}},m(w.prototype),s(w.prototype,a,function(){return this}),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(u(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then(function(t){return t.done?t.value:a.next()})},m(y),s(y,c,"Generator"),s(y,i,function(){return this}),s(y,"toString",function(){return"[object Generator]"}),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=O,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),b(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;b(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:O(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},t}function rW(t,e,r,n,o,i,a){try{var c=t[i](a),s=c.value}catch(t){r(t);return}c.done?e(s):Promise.resolve(s).then(n,o)}function rJ(t){return function(){var e=this,r=arguments;return new Promise(function(n,o){var i=t.apply(e,r);function a(t){rW(i,n,o,a,c,"next",t)}function c(t){rW(i,n,o,a,c,"throw",t)}a(void 0)})}}var rX=function(t,e){return function(r,n){e(r,n,t)}},rQ=function(){function t(t,e){this.env=t,tb(this.env)&&(this.jsbridge=e),this.bridgeTimeout=400}var e,r,n,o,i,a,c,s=t.prototype;return e=rJ(rK().mark(function t(){var e=this;return rK().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(this.jsbridge){t.next=3;break}return A(aI.JSB_ERROR,Error("tt bridge error when getting ad info"),{extJSON:{position:"getAdInfo"}}),t.abrupt("return",Promise.resolve({}));case 3:return t.abrupt("return",new Promise(function(){var t=rJ(rK().mark(function t(r){var n;return rK().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.callAdInfo();case 3:(n=t.sent).ad_info_from="jsb",n.ad_info_status=n.ad_info_status||"fulfilled",r(n),t.next=13;break;case 9:t.prev=9,t.t0=t.catch(0),r({}),A(aI.JSB_ERROR,t.t0,{extJSON:{position:"getAdInfo"}});case 13:case"end":return t.stop()}},t,null,[[0,9]])}));return function(e){return t.apply(this,arguments)}}()));case 4:case"end":return t.stop()}},t,this)})),s.getAdInfo=function(){return e.apply(this,arguments)},r=rJ(rK().mark(function t(){var e,r;return rK().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.call("adInfo",{},tP()?3500:5e3);case 3:if((e=t.sent).data){t.next=6;break}return t.abrupt("return",Promise.reject("adInfo no data"));case 6:return r={creative_id:e.data.cid,log_extra:e.data.log_extra},t.abrupt("return",r);case 10:if(t.prev=10,t.t0=t.catch(0),"JSBRIDGE TIMEOUT"!==t.t0){t.next=17;break}return P(aI.CUSTOM_INFO,{custom_name:"ad_info_init_timeout"}),t.abrupt("return",{ad_info_status:"timeout"});case 17:return A(aI.JSB_ERROR,t.t0,{extJSON:{position:"getAdInfo"}}),t.abrupt("return",{});case 19:case"end":return t.stop()}},t,this,[[0,10]])})),s.callAdInfo=function(){return r.apply(this,arguments)},n=rJ(rK().mark(function t(){return rK().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",Promise.resolve({}));case 1:case"end":return t.stop()}},t)})),s.getAppInfo=function(){return n.apply(this,arguments)},o=rJ(rK().mark(function t(e,r){var n,o,i,a,c,s,u,f,l,p,h,d;return rK().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(this.jsbridge){t.next=2;break}return t.abrupt("return",Promise.resolve());case 2:return i=(null==(o=null==(n=null==e?void 0:e.context)?void 0:n.ad)?void 0:o.creative_id)||"0",a=rN(e),c=Z(e)?"insight_log_monitor":"insight_log",s={analytics_message:a,trackLogData:JSON.stringify(e),category:"ad_analytics_msg",tag:rY,label:c},tA()&&(s.is_ad_event=1),l={eventName:rY,labelName:c,value:i,extValue:"0",extJson:s},"insight_log_monitor"===c&&tG(new Set([ab.MUSICAL_LY,ab.TRILL,ab.ULTRALITE]))?(f="x.reportAppLog",p={eventName:"insight_log_monitor",params:s},u=this.call("x.reportAppLog",p,this.bridgeTimeout)):tq()||Z(e)?(f="sendLog",u=this.call("sendLog",l,this.bridgeTimeout)):tI(this.env)?tR()&&r?(h={eventName:c,params:s},f="sendLogWithAdInfo",u=this.call("sendLogWithAdInfo",h,this.bridgeTimeout)):(f="sendLog",u=this.call("sendLog",l,this.bridgeTimeout)):(d={event_name:c,version:2,properties:s},f="track_event",u=this.call("track_event",d,400)),setTimeout(function(){P(aI.CUSTOM_INFO,{custom_name:"send_report_data",extJSON:{api_name:f}})}),t.abrupt("return",u);case 11:case"end":return t.stop()}},t,this)})),s.send=function(t,e){return o.apply(this,arguments)},i=rJ(rK().mark(function t(e,r,n,o){var i=this;return rK().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return void 0===r&&(r={}),void 0===n&&(n=400),void 0===o&&(o=!0),t.abrupt("return",new Promise(function(t,a){var c;if(!i.jsbridge){a("JSBRIDGE ERROR"),o&&A(aI.JSB_ERROR,Error("JSBRIDGE ERROR"),{extJSON:{position:"getCallPromise"}});return}n>0&&(c=window.setTimeout(function(){a("JSBRIDGE TIMEOUT"),o&&A(aI.JSB_ERROR,Error("JSBRIDGE TIMEOUT"),{extJSON:{position:"getCallPromise",method:e}})},n)),i.jsbridge&&i.jsbridge.call&&i.jsbridge.call(e,r,function(e){t(function(t,e){void 0===e&&(e=!0);var r={};try{if("string"==typeof t)r.data=JSON.parse(t);else if(t.code&&t.data&&t.ret?0:1)if(void 0!==t.code){var n=Object.assign({},t),o=n.code;r.code=o,delete n.code,n.data?r.data=n.data:r.data=n}else r.data=t;else(r=t).__data&&(r.data=r.__data)}catch(t){e&&A(aI.JSB_ERROR,t,{extJSON:{position:"getCallPromise bridge.call"}})}return r}(e,o)),window.clearTimeout(c)})}));case 4:case"end":return t.stop()}},t)})),s.call=function(t,e,r,n){return i.apply(this,arguments)},a=rJ(rK().mark(function t(e){var r,n,o,i,a;return rK().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.method,n=e.path,o=e.params,i=e.data,t.next=3,this.call("sendAnalyticsEvent",{method:r,path:n,params:o,data:i,header:{"Content-Type":"application/json"}},0,!1);case 3:return a=t.sent,t.abrupt("return",null==a?void 0:a.code);case 5:case"end":return t.stop()}},t,this)})),s.sendAnalyticsEvent=function(t){return a.apply(this,arguments)},c=rJ(rK().mark(function t(e){return rK().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(!(tI(this.env)&&tR())){t.next=2;break}return t.abrupt("return",this.call("updateFLLocalConv",e,this.bridgeTimeout));case 2:case"end":return t.stop()}},t,this)})),s.updateWebFlData=function(t){return c.apply(this,arguments)},t}();function rZ(){rZ=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function u(t,e,r,o){var i,a,c,s,u=Object._ttq_create((e&&e.prototype instanceof p?e:p).prototype);return n(u,"_invoke",{value:(i=t,a=r,c=new I(o||[]),s="suspendedStart",function(t,e){if("executing"===s)throw Error("Generator is already running");if("completed"===s){if("throw"===t)throw e;return T()}for(c.method=t,c.arg=e;;){var r=c.delegate;if(r){var n=function t(e,r){var n=r.method,o=e.iterator[n];if(void 0===o)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=void 0,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+n+"' method")),l;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,l;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=void 0),r.delegate=null,l):a:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,l)}(r,c);if(n){if(n===l)continue;return n}}if("next"===c.method)c.sent=c._sent=c.arg;else if("throw"===c.method){if("suspendedStart"===s)throw s="completed",c.arg;c.dispatchException(c.arg)}else"return"===c.method&&c.abrupt("return",c.arg);s="executing";var o=f(i,a,c);if("normal"===o.type){if(s=c.done?"completed":"suspendedYield",o.arg===l)continue;return{value:o.arg,done:c.done}}"throw"===o.type&&(s="completed",c.method="throw",c.arg=o.arg)}})}),u}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var l={};function p(){}function h(){}function d(){}var v={};s(v,i,function(){return this});var _=Object.getPrototypeOf,g=_&&_(_(O([])));g&&g!==e&&r.call(g,i)&&(v=g);var y=d.prototype=p.prototype=Object._ttq_create(v);function m(t){["next","throw","return"].forEach(function(e){s(t,e,function(t){return this._invoke(e,t)})})}function w(t,e){var o;n(this,"_invoke",{value:function(n,i){function a(){return new e(function(o,a){!function n(o,i,a,c){var s=f(t[o],t,i);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(l).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(n,i,o,a)})}return o=o?o.then(a,a):a()}})}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function b(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function O(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:T}}function T(){return{value:void 0,done:!0}}return h.prototype=d,n(y,"constructor",{value:d,configurable:!0}),n(d,"constructor",{value:h,configurable:!0}),h.displayName=s(d,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,s(t,c,"GeneratorFunction")),t.prototype=Object._ttq_create(y),t},t.awrap=function(t){return{__await:t}},m(w.prototype),s(w.prototype,a,function(){return this}),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(u(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then(function(t){return t.done?t.value:a.next()})},m(y),s(y,c,"Generator"),s(y,i,function(){return this}),s(y,"toString",function(){return"[object Generator]"}),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=O,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),b(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;b(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:O(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},t}function rz(t,e,r,n,o,i,a){try{var c=t[i](a),s=c.value}catch(t){r(t);return}c.done?e(s):Promise.resolve(s).then(n,o)}rQ=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er(),rX(0,en(tK.ENV)),rX(0,eo()),rX(1,en(tK.JS_BRIDGE)),rX(1,eo())],rQ);var r$=function(){function t(){}var e,r,n=t.prototype;return e=rZ().mark(function t(e,r,n){var o;return rZ().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(void 0===n&&(n=0),t.prev=1,!(!navigator||!navigator.sendBeacon)){t.next=4;break}return t.abrupt("return",!1);case 4:if(!(!(o=navigator.sendBeacon(e,JSON.stringify(r)))&&"number"==typeof n&&n>0)){t.next=10;break}return n--,t.next=9,ts(200);case 9:return t.abrupt("return",this.send(e,r,n));case 10:return t.abrupt("return",o);case 13:return t.prev=13,t.t0=t.catch(1),t.abrupt("return",!1);case 16:case"end":return t.stop()}},t,this,[[1,13]])}),r=function(){var t=this,r=arguments;return new Promise(function(n,o){var i=e.apply(t,r);function a(t){rz(i,n,o,a,c,"next",t)}function c(t){rz(i,n,o,a,c,"throw",t)}a(void 0)})},n.send=function(t,e,n){return r.apply(this,arguments)},n.sendByImage=function(t,e){new Image().src=rA(t,e)},t}();function r0(){r0=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function u(t,e,r,o){var i,a,c,s,u=Object._ttq_create((e&&e.prototype instanceof p?e:p).prototype);return n(u,"_invoke",{value:(i=t,a=r,c=new I(o||[]),s="suspendedStart",function(t,e){if("executing"===s)throw Error("Generator is already running");if("completed"===s){if("throw"===t)throw e;return T()}for(c.method=t,c.arg=e;;){var r=c.delegate;if(r){var n=function t(e,r){var n=r.method,o=e.iterator[n];if(void 0===o)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=void 0,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+n+"' method")),l;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,l;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=void 0),r.delegate=null,l):a:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,l)}(r,c);if(n){if(n===l)continue;return n}}if("next"===c.method)c.sent=c._sent=c.arg;else if("throw"===c.method){if("suspendedStart"===s)throw s="completed",c.arg;c.dispatchException(c.arg)}else"return"===c.method&&c.abrupt("return",c.arg);s="executing";var o=f(i,a,c);if("normal"===o.type){if(s=c.done?"completed":"suspendedYield",o.arg===l)continue;return{value:o.arg,done:c.done}}"throw"===o.type&&(s="completed",c.method="throw",c.arg=o.arg)}})}),u}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var l={};function p(){}function h(){}function d(){}var v={};s(v,i,function(){return this});var _=Object.getPrototypeOf,g=_&&_(_(O([])));g&&g!==e&&r.call(g,i)&&(v=g);var y=d.prototype=p.prototype=Object._ttq_create(v);function m(t){["next","throw","return"].forEach(function(e){s(t,e,function(t){return this._invoke(e,t)})})}function w(t,e){var o;n(this,"_invoke",{value:function(n,i){function a(){return new e(function(o,a){!function n(o,i,a,c){var s=f(t[o],t,i);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(l).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(n,i,o,a)})}return o=o?o.then(a,a):a()}})}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function b(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function O(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:T}}function T(){return{value:void 0,done:!0}}return h.prototype=d,n(y,"constructor",{value:d,configurable:!0}),n(d,"constructor",{value:h,configurable:!0}),h.displayName=s(d,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,s(t,c,"GeneratorFunction")),t.prototype=Object._ttq_create(y),t},t.awrap=function(t){return{__await:t}},m(w.prototype),s(w.prototype,a,function(){return this}),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(u(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then(function(t){return t.done?t.value:a.next()})},m(y),s(y,c,"Generator"),s(y,i,function(){return this}),s(y,"toString",function(){return"[object Generator]"}),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=O,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),b(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;b(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:O(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},t}function r1(t,e,r,n,o,i,a){try{var c=t[i](a),s=c.value}catch(t){r(t);return}c.done?e(s):Promise.resolve(s).then(n,o)}r$=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er()],r$);var r2=function(){function t(t){this.cookieExpireOption=eP(t)}var e,r,n=t.prototype;return n.genCookieID=function(){return tr()},e=r0().mark(function t(){return r0().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return ek(i,"1",this.cookieExpireOption),t.abrupt("return",eQ(tS()?"/static/enable_cookie.js":"https://analytics.tiktok.com/i18n/pixel/enable_cookie"));case 2:case"end":return t.stop()}},t,this)}),r=function(){var t=this,r=arguments;return new Promise(function(n,o){var i=e.apply(t,r);function a(t){r1(i,n,o,a,c,"next",t)}function c(t){r1(i,n,o,a,c,"throw",t)}a(void 0)})},n.enableCookie=function(){return r.apply(this,arguments)},n.enableFirstPartyCookie=function(t){if(t){ek(i,"1",this.cookieExpireOption);var e=this.getAnonymousId();this.setAnonymousId(e||this.genCookieID())}},n.disableCookie=function(){ek(i,"0",this.cookieExpireOption),ek(a,"",Object.assign(this.cookieExpireOption,{expires:-1})),eQ(tS()?"/static/disable_cookie.js":"https://analytics.tiktok.com/i18n/pixel/disable_cookie")},n.setAnonymousId=function(t){var e=this.getAnonymousId()||t;e&&ek(a,e.split(".")[0],this.cookieExpireOption)},n.getAnonymousId=function(){return eC(a)||""},n.canUseCookie=function(){try{return"0"!==eC(i)}catch(t){}return!1},n.resetExpires=function(){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var t={taskName:window.ttq._pf_tn,functionName:"resetExpires",start:performance.now()}}catch(t){}var e=eC(i);e&&ek(i,e,this.cookieExpireOption);var r=this.getAnonymousId();r&&this.setAnonymousId(r);try{window.ttq&&window.ttq._ppf&&(t.end=performance.now(),window.ttq._ppf.push(t))}catch(t){}},t}();function r3(t,e){return(r3=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}r2=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er(),(ao=en(tK.TTQ_GLOBAL_OPTIONS),function(t,e){ao(t,e,0)})],r2),(ai=aJ||(aJ={}))[ai.P0=4]="P0",ai[ai.P1=3]="P1",ai[ai.P2=2]="P2",ai[ai.P3=1]="P3";var r6="tt_hold_events",r5=function(t){function e(){var e;return e=t.apply(this,arguments)||this,e.consentMode=aC.UNKNOWN,e.queue=[],e.debounceUpdateCache=tc(function(){e.updateCache()},200,function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(e)),e.handleHistoryQueue=tt(function(){var t=eu(r6);Array.isArray(t)&&(e.queue=e.queue.concat(t),e.changeQueueWithConsent())}),e}e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,r3(e,t);var r=e.prototype;return r.on=function(e,r){t.prototype.on.call(this,e,r),this.handleHistoryQueue()},r.setConsentMode=function(t){this.consentMode=t,this.changeQueueWithConsent()},r.changeQueueWithConsent=function(){switch(this.consentMode){case aC.REVOKE:this.cleanQueue();break;case aC.GRANT:this.releaseQueue(),this.cleanQueue();case aC.HOLD:case aC.UNKNOWN:}},r.getConsentMode=function(){return this.consentMode},r.cacheReportTask=function(t,e,r){void 0===r&&(r=aB.defaultReport),this.queue.push({url:t,data:e,type:r}),this.debounceUpdateCache()},r.cleanQueue=function(){this.queue=[],ef(r6)},r.updateCache=function(){this.queue&&this.queue.length>0&&el(r6,this.queue)},r.releaseQueue=function(){var t=this;this.queue.sort(function(e,r){return t.getEventPriority(r.data)-t.getEventPriority(e.data)}),this.emit("queue",this.queue)},r.getEventPriority=function(t){return t.event&&t.event.length>0?aJ.P0:t.action&&t.action.length>0?aJ.P1:""===t.event?aJ.P2:aJ.P3},e}(e2);r5=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er()],r5);var r8=function(){function t(t){var e=t.name,r=t.context,n=t.reporters;this.reporters=[],this.context=r,this.reporters=n,this.name=e}var e=t.prototype;return e.initStart=function(){},e.initEnd=function(){},e.adInfoInitStart=function(){},e.adInfoInitEnd=function(){},e.contextInitStart=function(){},e.contextInitEnd=function(){},e.pageUrlWillChange=function(t,e){},e.pageUrlDidChange=function(t,e){},e.pageDidLoad=function(){},e.pageWillLeave=function(t){},e.pixelSend=function(t,e,r,n,o){},e.pixelDidMount=function(t){},e.patchEnd=function(){},t}();function r4(t,e){return(r4=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var r9=function(t){function e(){return t.apply(this,arguments)||this}return e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,r4(e,t),e}(r8=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er()],r8));function r7(t,e){return(r7=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var nt=function(t,e){return function(r,n){e(r,n,t)}},ne=function(t){function e(e,r){var n;return(n=t.call(this,{name:"Callback",reporters:r,context:e})||this).ttclidCookieValue="undefined"!=typeof document?eC(eb):"",n}return e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,r7(e,t),e.prototype.pixelDidMount=function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var e={taskName:window.ttq._pf_tn,functionName:"callback_plugin_pixelDidMount",start:performance.now()}}catch(t){}var r=ec(),n=ea(eb,r.url,r.referrer);n&&this.ttclidCookieValue!==n&&eS(eb,n);try{window.ttq&&window.ttq._ppf&&(e.end=performance.now(),window.ttq._ppf.push(e))}catch(t){}},e}(r9);ne=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er(),nt(0,en(tK.CONTEXT)),nt(1,en(tK.TTQ_REPORTERS))],ne);var nr={isHash:function(t){return!1},genIdentifierLabelByUserProperties:function(t){return{}}},nn={validatePhoneNumberLength:function(t){},parsePhoneNumberFromString:function(t){}},no={tryDecodeHashedBase64String:function(t){return null},tryDecodeHashedBase64Hex:function(t){return null}},ni={checkEmailFormat:function(t){return!1},checkMDNEmailFormat:function(t){return!1}},na=function(t){var e=t.parsePhoneNumberFromString,r=t.validatePhoneNumberLength;nn.parsePhoneNumberFromString=e,nn.validatePhoneNumberLength=r},nc=function(t){var e=t.isHash,r=t.genIdentifierLabelByUserProperties;nr.isHash=e,nr.genIdentifierLabelByUserProperties=r},ns=function(t){var e=t.tryDecodeHashedBase64String,r=t.tryDecodeHashedBase64Hex;no.tryDecodeHashedBase64String=e,no.tryDecodeHashedBase64Hex=r},nu=function(t){var e=t.checkEmailFormat,r=t.checkMDNEmailFormat;ni.checkEmailFormat=e,ni.checkMDNEmailFormat=r},nf=function(t){var e=t.parsePhoneNumberFromString,r=t.validatePhoneNumberLength,n=t.isHash,o=t.genIdentifierLabelByUserProperties,i=t.tryDecodeHashedBase64String,a=t.tryDecodeHashedBase64Hex,c=t.checkEmailFormat,s=t.checkMDNEmailFormat,u=t.sha256;nu({checkEmailFormat:c,checkMDNEmailFormat:s}),ns({tryDecodeHashedBase64String:i,tryDecodeHashedBase64Hex:a}),nc({isHash:n,genIdentifierLabelByUserProperties:o}),na({parsePhoneNumberFromString:e,validatePhoneNumberLength:r}),X(u)},nl=function(t,e,r){void 0===e&&(e=""),void 0===r&&(r=nn.parsePhoneNumberFromString);var n=t,o=e?r(t,e):r(t);return o?n="86"===o.countryCallingCode?o.nationalNumber:o.number:t.replace(/[^0-9]/g,"").length>0&&(n=t.replace(/[^0-9]/g,"")),n},np=["(null)","","''\"",void 0,"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855","eb045d78d273107348b0300c01d29b7552d622abbc6faf81b3ec55359aa9950c","not set",null,"6181738008c985a1b5f106b796c98e719efcc3c0ff68ddcd14a049825f4900a8","2a539d6520266b56c3b0c525b9e6128858baeccb5ee9b694a2906e123c8d6dd3","c6e52c372287175a895926604fa738a0ad279538a67371cd56909c7917e69ea1","None","74234e98afe7498fb5daf1f36ac2d78acc339464f950703b8c019892f982b90b","f24f02d3c35894296522abac8c4b2439b1c1b650e1fb4c97c0f3c50b580b0a3c","no","a683c5c5349f6f7fb903ba8a9e7e55d0ba1b8f03579f95be83f4954c33e81098","f18a2548c063c5a2b1560c6f2b9ec44bf9ed9017884404016d74f330119aaefe","449f06574cd639e1826848ff5d70ba95904574be84f34e61baa526d517dfb493","fcbcf165908dd18a9e49f7ff27810176db8e9f63b4352213741664245224f8aa","NA","bc857c49633bbc75644c51f36b16b2f768cc0ee13f65402ec7c32c96308272dd","42cbf37902c6911d7b4e371fe8f8708a0ceda6946249d4a3e23de8d5e60ae8b7"];function nh(t,e){return(nh=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var nd=function(t){function e(e){var r=e.name,n=e.context,o=e.reporters;return t.call(this,{name:r,reporters:o,context:n})||this}e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,nh(e,t);var r=e.prototype;return r.setIdentifyUtils=function(t){var e=t.isHash,r=t.sha256,n=t.genIdentifierLabelByUserProperties,o=t.tryDecodeHashedBase64String,i=t.tryDecodeHashedBase64Hex,a=t.validatePhoneNumberLength,c=t.parsePhoneNumberFromString,s=t.checkEmailFormat,u=t.checkMDNEmailFormat,f=t.getCookieDeprecationLabel,l=t.getAllTopics;nf({isHash:e,sha256:r,genIdentifierLabelByUserProperties:n,tryDecodeHashedBase64String:o,tryDecodeHashedBase64Hex:i,validatePhoneNumberLength:a,parsePhoneNumberFromString:c,checkEmailFormat:s,checkMDNEmailFormat:u}),this.parsePhoneNumberFromString=c,this.checkMDNEmailFormat=u,this.checkEmailFormat=s,this.sha256=r,this.getCookieDeprecationLabel=void 0===f?function(){}:f,this.getAllTopics=void 0===l?function(){}:l},r.baseHandleUserProperties=function(t,e){var r=this;try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var n={taskName:window.ttq._pf_tn||"identify_encryption",functionName:window.ttq._pf_tn&&"identify_encryption",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="identify_encryption")}}catch(t){}if(t){var o=this.identifyParamsFormattedInfo(t),i=this.identifyParamsFormattedInfoV2(t),a=nr.genIdentifierLabelByUserProperties(e);this.handlePIIDiagnostics(i),Object.entries(t).forEach(function(e){var n=e[0],o=e[1],a=void 0===o?"":o;if(a){var c=String(a);if(["email","phone_number","sha256_email","sha256_phone_number"].includes(n)){var s=r.getUserDataFormatInfoV2KeyName(n),u=no.tryDecodeHashedBase64Hex(c);if(null!==u)t[n]=u,null!==s&&(i=r.updateUserDataFormatV2Label(s,aR.BASE64_HEX_HASHED,i));else{var f=no.tryDecodeHashedBase64String(c);f&&(t[n]=f,null!==s&&(i=r.updateUserDataFormatV2Label(s,aR.BASE64_STRING_HASHED,i)))}}switch("zip_code"===n&&c&&(nr.isHash(c)?i=r.updateUserDataFormatV2Label("zip_code",aR.ZIP_CODE_IS_HASHED,i):(i=r.updateUserDataFormatV2Label("zip_code",aR.ZIP_CODE_IS_NOT_HASHED,i),r.isZipFromUs(t)?(t.zip_code=r.sha256(r.truncateString(c,5)),i=r.updateUserDataFormatV2Label("zip_code",aR.ZIP_CODE_IS_US,i)):(t.zip_code=r.sha256(c),i=r.updateUserDataFormatV2Label("zip_code",aR.ZIP_CODE_IS_NOT_US,i)))),n){case"email":t.email=nr.isHash(c)&&!r.checkEmailFormat(c)?c:r.sha256(r.handleEmail(c));break;case"eb_email":t.eb_email=r.sha256(r.handleEmail(c));break;case"phone_number":t.phone_number=nr.isHash(c)?c:r.sha256(r.handlePhoneNumber(c));break;case"eb_phone_number":t.eb_phone_number=r.sha256(r.handlePhoneNumber(c));break;case"auto_email":t.auto_email=r.sha256(r.handleEmail(c));break;case"auto_phone_number":t.auto_phone_number=r.sha256(r.handlePhoneNumber(c));break;case"first_name":t.first_name=nr.isHash(c)?c:r.sha256(c);break;case"last_name":t.last_name=nr.isHash(c)?c:r.sha256(c);break;case"city":t.city=r.truncateString(c,80);break;case"state":t.state=r.truncateString(c,80);break;case"country":t.country=r.truncateString(c,80);break;default:return}}}),t.sha256_email&&(t.email=this.handleCheckHashedEmailValue(String(t.sha256_email),o)),t.sha256_phone_number&&(t.phone_number=this.handleCheckHashedPhoneValue(String(t.sha256_phone_number),o));try{window.ttq&&window.ttq._ppf&&(n.end=performance.now(),window.ttq._ppf.push(n),"identify_encryption"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}return{userProperties:t,userDataFormat:o,userDataFormatV2:i,identifierLabel:a}}},r.identifyParamsFormattedInfo=function(t){var e=this,r={},n=/^sha256_(.*)$/;return Object.entries(t).forEach(function(t){var o=t[0],i=t[1],a=String(void 0===i?"":i),c=o.match(n);switch(o){case"email":e.handleEmailFormat(a,o,r);break;case"phone_number":e.handlePhoneNumberFormat(a,"phone_number",r);break;case"auto_email":e.handleEmailFormat(a,"auto_email",r);break;case"auto_phone_number":e.handlePhoneNumberFormat(a,"auto_phone_number",r);break;case(c||{}).input:var s=null==c?void 0:c.pop();s&&q.includes(s)&&(r[s]=[aN.HASHED]);break;case"first_name":case"last_name":case"city":case"state":case"country":case"zip_code":case"partner_id":e.handleNewPiisFormat(a,o,r);break;default:r[o]=[aN.CORRECT_FORMAT]}}),r},r.identifyParamsFormattedInfoV2=function(t){var e=this,r={};return Object.entries(t).forEach(function(t){var n=t[0],o=t[1],i=String(void 0===o?"":o);switch(n){case"email":e.handlePixelValidation(i,U.EMAIL_IS_HASHED,r);break;case"phone_number":e.handlePixelValidation(i,U.PHONE_IS_HASHED,r);break;case"sha256_email":e.handlePixelValidation(i,U.SHA256_EMAIL,r);break;case"sha256_phone_number":e.handlePixelValidation(i,U.SHA256_PHONE,r);break;case"first_name":case"last_name":case"city":case"state":case"country":case"zip_code":case"partner_id":break;default:r[n]=[aR.UNKNOWN_INVALID]}}),r},r.updateUserDataFormatV2Label=function(t,e,r){var n,o;return(null===r[t]||void 0===r[t]||(null==(n=r[t])?void 0:n.includes(aR.UNKNOWN_INVALID)))&&(r[t]=[]),null==(o=r[t])||o.push(e),r},r.getUserDataFormatInfoV2KeyName=function(t){return({email:"email_is_hashed",phone_number:"phone_is_hashed",sha256_email:"sha256_email",sha256_phone_number:"sha256_phone",zip_code:"zip_code"})[t]||null},r.handlePIIDiagnostics=function(t){},r.handleEmail=function(t){return t.toLowerCase()},r.handlePhoneNumber=function(t,e){return void 0===e&&(e=this.parsePhoneNumberFromString),nl(t,"",e)},r.handleCheckHashedEmailValue=function(t,e,r){return(void 0===r&&(r=this.checkEmailFormat),e.email=e.email||[],nr.isHash(t))?(null==e||e.email.push(aN.HASHED_CORRECT),t):r(t)?(null==e||e.email.push(aN.PLAINTEXT_EMAIL),this.sha256(this.handleEmail(String(t)))):(null==e||e.email.push(aN.HASHED_ERR),this.sha256(t))},r.handleCheckHashedPhoneValue=function(t,e,r){return(void 0===r&&(r=this.parsePhoneNumberFromString),e.phone_number=e.phone_number||[],nr.isHash(t))?(null==e||e.phone_number.push(aN.HASHED_CORRECT),t):r(t)?(e.phone_number.push(aN.PLAINTEXT_PHONE),this.sha256(this.handlePhoneNumber(String(t),r))):(null==e||e.phone_number.push(aN.HASHED_ERR),this.sha256(t))},r.handlePixelValidation=function(t,e,r){r[e]=[],np.includes(t)&&r[e].push(aR.FILTER_EVENTS),t&&nr.isHash(t)&&r[e].push(aR.HASHED),t&&this.checkEmailFormat(t)&&r[e].push(aR.PLAIN_EMAIL),t&&this.checkMDNEmailFormat(t)&&r[e].push(aR.PLAIN_MDN_EMAIL),t&&this.parsePhoneNumberFromString(t)&&r[e].push(aR.PLAIN_PHONE),t&&0===r[e].length&&r[e].push(aR.UNKNOWN_INVALID)},r.isZipFromUs=function(t){var e;return(null==(e=t.country)?void 0:e.toLowerCase())==="us"},r.truncateString=function(t,e){var r=Array.from(t);return r.length<=e?t:r.slice(0,e).join("")},r.handlePhoneNumberFormat=function(t,e,r){var n=this.handleCheckPhoneNumber(String(t),this.parsePhoneNumberFromString);r[e]=n},r.handleCheckPhoneNumber=function(t,e){void 0===e&&(e=this.parsePhoneNumberFromString);var r=[];return t?nr.isHash(t)?r.push(aN.HASHED):e(t)?r.push(aN.CORRECT_FORMAT):r.push(aN.WRONG_FORMAT):r.push(aN.EMPTY_VALUE),r},r.handleCheckEmail=function(t,e){void 0===e&&(e=this.checkEmailFormat);var r=[];return t?nr.isHash(t)?r.push(aN.HASHED):e(t)?r.push(aN.CORRECT_FORMAT):r.push(aN.WRONG_FORMAT):r.push(aN.EMPTY_VALUE),r},r.handleEmailFormat=function(t,e,r){var n=this.handleCheckEmail(String(t),this.checkEmailFormat);r&&r[e]&&(r[e]||[]).includes(aN.HASHED)||(r[e]=n)},r.handleNewPiisFormat=function(t,e,r){t&&(r[e]=[aN.CORRECT_FORMAT])},e}(r9);function nv(){nv=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function u(t,e,r,o){var i,a,c,s,u=Object._ttq_create((e&&e.prototype instanceof p?e:p).prototype);return n(u,"_invoke",{value:(i=t,a=r,c=new I(o||[]),s="suspendedStart",function(t,e){if("executing"===s)throw Error("Generator is already running");if("completed"===s){if("throw"===t)throw e;return T()}for(c.method=t,c.arg=e;;){var r=c.delegate;if(r){var n=function t(e,r){var n=r.method,o=e.iterator[n];if(void 0===o)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=void 0,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+n+"' method")),l;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,l;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=void 0),r.delegate=null,l):a:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,l)}(r,c);if(n){if(n===l)continue;return n}}if("next"===c.method)c.sent=c._sent=c.arg;else if("throw"===c.method){if("suspendedStart"===s)throw s="completed",c.arg;c.dispatchException(c.arg)}else"return"===c.method&&c.abrupt("return",c.arg);s="executing";var o=f(i,a,c);if("normal"===o.type){if(s=c.done?"completed":"suspendedYield",o.arg===l)continue;return{value:o.arg,done:c.done}}"throw"===o.type&&(s="completed",c.method="throw",c.arg=o.arg)}})}),u}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var l={};function p(){}function h(){}function d(){}var v={};s(v,i,function(){return this});var _=Object.getPrototypeOf,g=_&&_(_(O([])));g&&g!==e&&r.call(g,i)&&(v=g);var y=d.prototype=p.prototype=Object._ttq_create(v);function m(t){["next","throw","return"].forEach(function(e){s(t,e,function(t){return this._invoke(e,t)})})}function w(t,e){var o;n(this,"_invoke",{value:function(n,i){function a(){return new e(function(o,a){!function n(o,i,a,c){var s=f(t[o],t,i);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(l).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(n,i,o,a)})}return o=o?o.then(a,a):a()}})}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function b(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function O(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:T}}function T(){return{value:void 0,done:!0}}return h.prototype=d,n(y,"constructor",{value:d,configurable:!0}),n(d,"constructor",{value:h,configurable:!0}),h.displayName=s(d,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,s(t,c,"GeneratorFunction")),t.prototype=Object._ttq_create(y),t},t.awrap=function(t){return{__await:t}},m(w.prototype),s(w.prototype,a,function(){return this}),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(u(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then(function(t){return t.done?t.value:a.next()})},m(y),s(y,c,"Generator"),s(y,i,function(){return this}),s(y,"toString",function(){return"[object Generator]"}),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=O,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),b(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;b(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:O(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},t}function n_(t,e,r,n,o,i,a){try{var c=t[i](a),s=c.value}catch(t){r(t);return}c.done?e(s):Promise.resolve(s).then(n,o)}function ng(t){return function(){var e=this,r=arguments;return new Promise(function(n,o){var i=t.apply(e,r);function a(t){n_(i,n,o,a,c,"next",t)}function c(t){n_(i,n,o,a,c,"throw",t)}a(void 0)})}}function ny(t,e){return(ny=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var nm=function(t,e){return function(r,n){e(r,n,t)}},nw=function(t){function e(e,r){var n;return(n=t.call(this,{name:"Identify",reporters:r,context:e})||this).init(),n}e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,ny(e,t);var r,n,o=e.prototype;return o.init=function(){var t=this;return this.pluginPromise||(P(aI.IDENTIFY_INIT_START),this.pluginPromise=eQ(tS()?"/static/identify.js":"https://analytics.tiktok.com/i18n/pixel/static/identify_ecbed230.js").then(function(){t.detectTopics(),P(aI.IDENTIFY_INIT_END)}).catch(function(){var t=Error("Loading chunk identify failed.\n(error: "+window.location.host+"/static/identify.js)");return t.name="ChunkLoadError",Promise.reject(t)})),this.pluginPromise},r=ng(nv().mark(function t(e,r){return nv().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(e){t.next=2;break}return t.abrupt("return",void 0);case 2:return t.next=4,this.init();case 4:return t.abrupt("return",this.baseHandleUserProperties(e,r));case 5:case"end":return t.stop()}},t,this)})),o.handleUserProperties=function(t,e){return r.apply(this,arguments)},o.handlePIIDiagnostics=function(t){try{var e=t.email_is_hashed,r=void 0===e?[]:e,n=t.sha256_email,o=void 0===n?[]:n,i=t.phone_is_hashed,a=void 0===i?[]:i,c=t.sha256_phone,s=void 0===c?[]:c;if(r.includes(aR.UNKNOWN_INVALID)||o.includes(aR.UNKNOWN_INVALID))return void x(aU.INVALID_EMAIL_FORMAT);if(a.includes(aR.UNKNOWN_INVALID)||s.includes(aR.UNKNOWN_INVALID))return void x(aU.INVALID_PHONE_NUMBER_FORMAT);if(r.includes(aR.FILTER_EVENTS)||o.includes(aR.FILTER_EVENTS))return void x(aU.INVALID_EMAIL_INFORMATION);if(a.includes(aR.FILTER_EVENTS)||s.includes(aR.FILTER_EVENTS))return void x(aU.INVALID_PHONE_NUMBER_INFORMATION)}catch(t){}},n=ng(nv().mark(function t(){var e,r;return nv().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,this.getCookieDeprecationLabel();case 3:return e=t.sent,t.next=6,this.getAllTopics();case 6:(r=t.sent)&&P(aI.CUSTOM_INFO,{custom_name:"topics",extJSON:{cookie_label:String(e),stack:r.toString()}}),t.next=12;break;case 10:t.prev=10,t.t0=t.catch(0);case 12:case"end":return t.stop()}},t,this,[[0,10]])})),o.detectTopics=function(){return n.apply(this,arguments)},e}(nd);nw=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er(),nm(0,en(tK.CONTEXT)),nm(1,en(tK.TTQ_REPORTERS))],nw),(aa=aX||(aX={})).ERROR_FORMAT="error_format",aa.OVER_LENGTH="over_length_3e4",aa.FILTER_SENSITIVE_FIELDS="filter_sensitive_fields";var nE="form_detail_error";(ac=aQ||(aQ={})).GET_ELEMENTS_ERROR="get_elements_error",ac.INIT_ERROR="init_error",ac.ASSEMBLE_FORM_DETAIL_ERROR="assemble_form_detail_error",ac.DETECT_FORM_ELEMENT_ERROR="detect_form_element_error",ac.GET_OVERALL_FORM_DETAIL_ERROR="get_overall_form_detail_error",ac.FORM_OBSERVER_ERROR="form_observer_error",ac.OVER_LENGTH="over_length_3e4",(as=aZ||(aZ={})).METADATA="Metadata",as.CLICK="Click",(au=az||(az={})).AUTO_COLLECTION="AUTO_COLLECTION",au.AUTO_FORM="AUTO_FORM",au.AUTO_CLICK="AUTO_CLICK",au.AUTO_VC="AUTO_VC",au.AUTO_VC_REVERSE="AUTO_VC_REVERSE",(af=a$||(a$={})).AUTO_FORM="form_rules",af.AUTO_VC="vc_rules",af.AUTO_VC_REVERSE="vc_rules_reverse",(al=a0||(a0={})).PAGE_LEAVE="PageLeave",al.PAGE_VIEW="PageView",al.DOM_CHANGE="DomChange",al.URL_CHANGE="UrlChange",al.CLICK="Click",al.SCROLL="Scroll";var nb=["AnatomicalStructure","AnatomicalSystem","ApprovedIndication","ArriveAction","Artery","BioChemEntity","BloodTest","Bone","BorrowAction","BrainStructure","BrokerageAccount","CDCPMDRecord","ChemicalSubstance","CovidTestingFacility","DDxElement","DepartAction","DepositAccount","DiagnosticLab","DiagnosticProcedure","Diet","DietarySupplement","DoseSchedule","ElementarySchool","HighSchool","ExercisePlan","Gene","GovernmentBenefitsType","GovernmentService","HealthAspectEnumeration","HealthInsurancePlan","HealthPlanCostSharingSpecification","HealthTopicContent","Hospital","ImagingTest","InfectiousAgentClass","InvestmentFund","InvestmentOrDeposit","Invoice","Joint","LendAction","LifestyleModification","Ligament","LoanOrCredit","LymphaticVessel","MaximumDoseSchedule","MedicalAudience","MedicalAudienceType","MedicalCause","MedicalCode","MedicalCondition","MedicalConditionStage","MedicalContraindication","MedicalDevice","MedicalEntity","MedicalEvidenceLevel","MedicalGuidelineContraindication","MedicalIndication","MedicalIntangible","MedicalObservationalStudy","MedicalOrganization","MedicalProcedure","MedicalProcedureType","MedicalRiskCalculator","MedicalRiskFactor","MedicalRiskScore","MedicalSign","MedicalSignOrSymptom","MedicalStudy","MedicalSymptom","MedicalTest","MedicalTestPanel","MedicalTherapy","MedicalTrial","MiddleSchool","MoneyTransfer","Muscle","Nerve","OccupationalTherapy","Order","PalliativeProcedure","ParentAudience","PathologyTest","Patient","PeopleAudience","Person","Pharmacy","PhysicalActivity","PhysicalTherapy","Physician","PoliticalParty","Preschool","PreventionIndication","Protein","PsychologicalTreatment","RadiationTherapy","RecommendedDoseSchedule","ReportedDoseSchedule","School","Substance","SuperficialAnatomy","SurgicalProcedure","Text","TherapeuticProcedure","TreatmentIndication","URL","Vein","Vessel","VitalSign","WorkersUnion"];function nI(t){return/([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9._-]+)/gi.test(t)||/(\+?0?86-?)?1[3-9]\d{9}/g.test(t)||/(\+\d{1,2}\s?)?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}/g.test(t)||/^[\-!$><-==&_\/\?\.,0-9:; \]\[%~\"\{\}\)\(\+\@\^\`]/g.test(t)||nb.some(function(e){return t.toLowerCase().indexOf(e.toLowerCase())>-1})}var nO=function t(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return"";if(e===document.documentElement)return"/HTML";for(var r=1,n=e.previousSibling;n;)n.nodeType===Node.ELEMENT_NODE&&n.tagName===e.tagName&&r++,n=n.previousSibling;var o=e.tagName.toLowerCase();return t(e.parentNode)+"/"+o+"["+r+"]"};function nT(t,e){return function(){for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];window.requestIdleCallback&&window.requestIdleCallback(t.bind.apply(t,[e].concat(n))),t.apply(e,n)}}function nN(t){var e=t.options,r=t.plugins;return e&&!1!==e.autoConfig&&r&&r[oW]}function nR(t,e){if(!a$[e])return!0;var r=t.plugins;return e===az.AUTO_VC_REVERSE?nN(t)&&r[oW]&&!r[oW][a$.AUTO_VC]:nN(t)&&r[oW]&&r[oW][a$[e]]}var nS=function(t){var e=t.parentNode,r=0,n=!1;try{n=tp(t)}catch(e){A(aI.CUSTOM_ERROR,e,{custom_name:"button_check_error",custom_enum:"auto_click",extJSON:{element:t}}),n=!1}if(n)return t;for(;r<5&&e&&e!==document;){var o=!1;try{o=tp(e)}catch(e){A(aI.CUSTOM_ERROR,e,{custom_name:"button_check_error",custom_enum:"auto_click",extJSON:{element:t}}),o=!1}if(o)return e;e=e.parentNode,r++}return t},nP=function t(e){for(var r=0,n=e.children,o=0;o<n.length;o++){var i=n[o],a=!1;try{a=tp(i)}catch(t){A(aI.CUSTOM_ERROR,t,{custom_name:"button_check_error",custom_enum:"auto_click",extJSON:{element:i}}),a=!1}a&&r++,r+=t(i)}return r},nA=function(t){var e,r,n,o,i="";if("A"===t.tagName.toUpperCase())i=null!=(e=t.getAttribute("href"))?e:"";else if("BUTTON"===t.tagName.toUpperCase()){var a=null!=(r=t.getAttribute("onclick"))?r:"",c=null!=(n=t.getAttribute("formaction"))?n:"",s=a.match(/^.*=(['"])(.*)\1.*$/);c?i=c:s&&(i=s[2])}else"FORM"===t.tagName.toUpperCase()&&(i=null!=(o=t.getAttribute("action"))?o:"");return i},nx=function(t,e){void 0===e&&(e=!0);for(var r,n,o,i,a,c,s,u=t.attributes,f={type:"",value:"",name:"",class:"",dataset:"",id:"",tag:"",destination:"",xpath:"",inner_text:"",image_url:"",num_child_buttons:0},l=0;l<u.length;l++){var p=u[l];f[p.name]=p.value}f.dataset=JSON.stringify(t.dataset),f.id=null!=(r=t.id)?r:"",f.class=null!=(n=t.className)?n:"",f.tag=t.tagName,f.destination=nA(t),f.name=null!=(o=t.getAttribute("name"))?o:"",f.value=null!=(i=t.getAttribute("value"))?i:"",f.type=null!=(a=t.getAttribute("type"))?a:"",f.rect=t.getBoundingClientRect();try{f.xpath=nO(t)}catch(e){f.xpath="",A(aI.CUSTOM_ERROR,e,{custom_name:"button_check_error",custom_enum:"auto_click",extJSON:{element:t}})}return f.inner_text=null!=(c=t.innerText)?c:"",f.image_url=null!=(s=t.getAttribute("src"))?s:"",f.num_child_buttons=e?nP(t):0,f},nL=function(t){var e=t.tag,r=t.class,n=t.destination,o=t.id,i=t.name,a=t.type,c=t.value,s=t.rect,u=t.xpath,f=t.inner_text,l=t.image_url,p={tag:e,attributes:{},inner_text:f,xpath:u,num_child_buttons:t.num_child_buttons,timestamp:new Date().toISOString(),position:s?{x:s.x,y:s.y}:{x:"",y:""}};return r&&(p.attributes.class=r),n&&(p.attributes.destination=n),o&&(p.attributes.id=o),i&&(p.attributes.name=i),a&&(p.attributes.type=a),c&&(p.attributes.value=c),l&&(p.image_url=l),p},nC={getMicroData:!0,getJSONLD:!0,getOpenGraph:!0,getMetaData:!0};function nk(t,e){return(void 0===e&&(e=500),"string"!=typeof t)?"":(t=t.trim()).length<e?t:t.slice(0,500)}function nD(t){return void 0===t&&(t=[]),{items:t,has:function(t){return this.items.some(function(e){return e===t})},add:function(t){this.has(t)||this.items.push(t)}}}function nj(t,e){if("object"!=typeof t)return t;if(Array.isArray(t))return t.map(function(t){return nj(t,e)});var r={};for(var n in t)!function(t,e){return!!e&&e.length>0&&e.some(function(e){return t.toLowerCase()===e.toLowerCase()})}(n,e)&&(r[n]=nj(t[n],e));return r}function nM(t){return Array.isArray(t)?t.some(function(t){return nM(t)}):"string"==typeof t&&(t=t.toLowerCase().replace(/https?:\/\/schema\.org\//,""),nb.some(function(e){return t===e.toLowerCase()}))}var nF=["form","[id*=form], [class*=form]"],nq=["label"],nU=["input,select,textarea"],nG=["radio","checkbox"],nH=["hidden","reset","submit","password"];function nV(t,e){try{for(var r=0;r<t.length;r++){var n=e.querySelectorAll(t[r]);if(n&&n.length>0)return Array.from(n)}return[]}catch(t){return A(aI.CUSTOM_ERROR,t,{custom_name:nE,custom_enum:aQ.GET_ELEMENTS_ERROR}),[]}}function nB(t){var e="";return!function t(r){for(;r;)r.nodeType===Node.TEXT_NODE?e+=r.textContent:"SELECT"!==r.nodeName&&r.firstChild&&t(r.firstChild),r=r.nextSibling}(t.firstChild),e.replace(/[\t\n]/g,"").trim()}function nY(t){if(!t)return!1;var e=window.getComputedStyle(t);return!("none"===e.display||"visible"!==e.visibility||function t(e){return!(!e||e.isSameNode(document.body)||e.isSameNode(document))&&("0"==window.getComputedStyle(e).opacity||t(e.parentElement))}(t))&&(0!==t.offsetWidth||0!==t.offsetHeight)}function nK(t){var e=t.getAttribute("type");return!!e&&nH.indexOf(e)>-1}function nW(t){return t&&nI(t)?"__Text__":t}(ap=a1||(a1={}))[ap.CONTAIN=0]="CONTAIN",ap[ap.ID=1]="ID",ap[ap.SELECTOR=2]="SELECTOR";var nJ=function(){function t(t){var e=this;this.formUpdateHandlers=[],this.answerMap={},t.find(function(t){var r=e.getRules(t);return!!r&&(e.rules=r,!0)}),this.init()}var e=t.prototype;return e.getRules=function(t){var e=t.plugins&&t.plugins.AutoConfig;return e&&e[a$.AUTO_FORM]},e.init=function(){var t=this;try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var e={taskName:window.ttq._pf_tn,functionName:"initAutoForm_init",start:performance.now()}}catch(t){}try{this.forms=this.detectFormElement(),this.forms&&this.forms.forEach(function(e){e.formDetail=t.assembleFormDetail(e),t.startFormObserver(e,t.formUpdateHandlers)})}catch(t){A(aI.CUSTOM_ERROR,t,{custom_name:nE,custom_enum:aQ.INIT_ERROR})}try{window.ttq&&window.ttq._ppf&&(e.end=performance.now(),window.ttq._ppf.push(e))}catch(t){}},e.getOverallFormDetail=function(){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var t={taskName:window.ttq._pf_tn,functionName:"initAutoForm_getOverallFormDetail",start:performance.now()}}catch(t){}var e="[]";try{this.forms&&this.forms.length>0&&(this.forms.some(function(t){var e=t.el;return!document.body.contains(e)})&&this.init(),e=JSON.stringify(this.forms.map(function(t){return t.formDetail}).filter(function(t){return t})))}catch(t){A(aI.CUSTOM_ERROR,t,{custom_name:nE,custom_enum:aQ.GET_OVERALL_FORM_DETAIL_ERROR})}try{window.ttq&&window.ttq._ppf&&(t.end=performance.now(),window.ttq._ppf.push(t))}catch(t){}return e},e.addFormUpdateHandler=function(t){this.formUpdateHandlers.push(t)},e.startFormObserver=function(t,e){var r=this;try{var n=tc(function(){var n,o=r.assembleFormDetail(t);(!t.formDetail||o&&(n=t.formDetail,Object.keys(function t(e,r){var n={};for(var o in e)if(e.hasOwnProperty(o)&&!r.hasOwnProperty(o))n[o]=e[o];else if(e.hasOwnProperty(o)&&r.hasOwnProperty(o)&&e[o]!==r[o])if("object"==typeof e[o]&&"object"==typeof r[o]){var i=t(e[o],r[o]);Object.keys(i).length>0&&(n[o]=i)}else n[o]=e[o];for(var a in r)r.hasOwnProperty(a)&&!e.hasOwnProperty(a)&&(n[a]=r[a]);return n}(o,n)).length>0))&&(t.formDetail=o,e.forEach(function(e){return e.call(r,t.formDetail)}))},2e3,this);if(t.el.parentNode){var o=t.el.parentNode;this.observer&&this.observer.disconnect(),this.observer=new MutationObserver(n),this.observer.observe(o,{attributes:!0,childList:!0,subtree:!0}),o.addEventListener("click",n,{capture:!0})}}catch(t){A(aI.CUSTOM_ERROR,t,{custom_name:nE,custom_enum:aQ.FORM_OBSERVER_ERROR})}},e.detectFormElement=function(){try{var t,e,r=[0,0,0],n=(t=this.rules,(e=nV(t||nF,document)).filter(function(t){return!e.some(function(e){return e.contains(t)&&e!==t})}));if(!n)return[];var o=n.map(function(t){return{el:t,questions:[]}});return o.forEach(function(t){var e,n,o=(e=t.el,nV(nq,e)),i=new Set([]);o.forEach(function(e){var n=function(t,e){var r=nV(nU,t);if(r&&r.length)return{els:r,from:a1.CONTAIN};var n=t.getAttribute("for");if(n&&(r=nV(["input[id='"+n+"'],select[id='"+n+"'],textarea[id='"+n+"']"],e)))return{els:r,from:a1.ID};return!1}(e,t.el);if(n){var o=n.els,a=n.from,c=o.filter(function(t){return!nK(t)}).map(function(t){return i.add(t),{el:t,from:a}});c&&c.length&&(r[a]=1,t.questions.push({el:e,answers:c}))}}),(n=t.el,nV(nU,n)).filter(function(t){return!nK(t)}).forEach(function(e){if(!i.has(e)){r[a1.SELECTOR]=1;var n,o=(n=t.el,function t(e){return null==e||e.isSameNode(n)?n:nB(e).length>0?e:t(e.parentNode)}(e.parentNode));t.questions.push({el:o,answers:[{el:e,from:a1.SELECTOR}]})}})}),P(aI.CUSTOM_INFO,{custom_name:"form_detail_answer_from",custom_enum:r.join("")}),o}catch(t){return A(aI.CUSTOM_ERROR,t,{custom_name:nE,custom_enum:aQ.DETECT_FORM_ELEMENT_ERROR}),[]}},e.calculateQuestionFilledTime=function(t){var e=t.el,r=t.answers,n=nO(e),o=r.reduce(function(t,e){var r=e.el,n=r.getAttribute("type");return n&&nG.indexOf(n.toLowerCase())>-1?t+","+r.checked:t+","+r.value},"");this.answerMap[n]||(this.answerMap[n]={defaultValue:o,value:o});var i=this.answerMap[n],a=i.defaultValue,c=i.filledTime;return(this.answerMap[n].value=o,a===o)?void delete this.answerMap[n].filledTime:c||(this.answerMap[n].filledTime=+new Date)},e.assembleFormDetail=function(t){var e=this,r=t.el,n=t.questions;try{var o={xpath:nO(r),id:r.id,name:nW(r.getAttribute("name")),tag:r.tagName.toLowerCase(),class_name:r.className,questions:[],width:r.offsetWidth,height:r.offsetHeight,is_visible:nY(r)};return o.questions=n.map(function(t){var r=t.el,n=t.answers,o={xpath:nO(r),id:r.id,name:nW(nB(r)),tag:r.tagName.toLowerCase(),class_name:r.className,filled_time:e.calculateQuestionFilledTime(t),answers:[],width:r.offsetWidth,height:r.offsetHeight,is_visible:nY(r)};return n.forEach(function(t){var e=t.el,r=t.from;e&&"SELECT"===e.tagName.toUpperCase()?o.answers=o.answers.concat(Array.from(e.querySelectorAll("option")).map(function(t){return{xpath:nO(t),id:t.id,name:nW(t.value||t.innerText),tag:t.tagName.toLowerCase(),class_name:t.className,from:r,width:t.offsetWidth,height:t.offsetHeight,is_visible:nY(e)}})):o.answers.push({xpath:nO(e),id:e.id,name:nW(e.getAttribute("name")),tag:e.tagName.toLowerCase(),class_name:e.className,input_type:e.getAttribute("type"),placeholder:nW(e.getAttribute("placeholder")),from:r,width:e.offsetWidth,height:e.offsetHeight,is_visible:nY(e)})}),o}),o}catch(t){A(aI.CUSTOM_ERROR,t,{custom_name:nE,custom_enum:aQ.ASSEMBLE_FORM_DETAIL_ERROR});return}},t}(),nX=["United States","US","Canada","CA","Australia","AU","Mexico","MX","Argentina","AR","Chile","CL","Colombia","CO","Fiji","FJ","Liberia","LR","Namibia","NA","New Zealand","NZ","Singapore","SG","Solomon Islands","SB","Suriname","SR","South Africa","ZA","Barbados","BB","Belize","BZ","Cuba","CU","Dominican Republic","DO","Guyana","GY","Jamaica","JM","Cayman Islands","KY","Trinidad and Tobago","TT","Tuvalu","TV","Zimbabwe","ZW","United Kingdom","GB","Egypt","EG","Falkland Islands","FK","Gibraltar","GI","Guernsey","GG","Isle of Man","IM","Jersey","JE","Lebanon","LB","Saint Helena","SH","Syria","SY","Sudan","SD","Japan","JP","China","CN","Japan","JP","CN","South Korea","KR","Philippines","PH","Cuba","CU","Sweden","SE","Norway","NO","Denmark","DK","Iceland","IS","Costa Rica","CR","El Salvador","SV","Bolivia","BO","Venezuela","VE","Bahamas","BS","Brunei","BN","Ethiopia","ET","Eritrea","ER","Iran","IR","Oman","OM","Qatar","QA","Saudi Arabia","SA","Yemen","YE","Bulgaria","BG","Kyrgyzstan","KG","Central African CFA franc zone","XAF","West African CFA franc zone","XOF"].map(function(t){return t.toUpperCase()}),nQ=["AED","AFN","ALL","AMD","ANG","AOA","ARS","AUD","AWG","AZN","BAM","BBD","BDT","BGN","BHD","BIF","BMD","BND","BOB","BOV","BRL","BSD","BTC","BTN","BWP","BYN","BYR","BZD","CAD","CDF","CHE","CHF","CHW","CLF","CLP","CNH","CNY","COP","COU","CRC","CUC","CUP","CVE","CZK","DJF","DKK","DOP","DZD","EEK","EGP","ERN","ETB","ETH","EUR","FJD","FKP","GBP","GEL","GGP","GHC","GHS","GIP","GMD","GNF","GTQ","GYD","HKD","HNL","HRK","HTG","HUF","IDR","ILS","IMP","INR","IQD","IRR","ISK","JEP","JMD","JOD","JPY","KES","KGS","KHR","KMF","KPW","KRW","KWD","KYD","KZT","LAK","LBP","LKR","LRD","LSL","LTC","LTL","LVL","LYD","MAD","MDL","MGA","MKD","MMK","MNT","MOP","MRO","MRU","MUR","MVR","MWK","MXN","MXV","MYR","MZN","NAD","NGN","NIO","NOK","NPR","NZD","OMR","PAB","PEN","PGK","PHP","PKR","PLN","PYG","QAR","RMB","RON","RSD","RUB","RWF","SAR","SBD","SCR","SDG","SEK","SGD","SHP","SLL","SOS","SRD","SSP","STD","STN","SVC","SYP","SZL","THB","TJS","TMT","TND","TOP","TRL","TRY","TTD","TVD","TWD","TZS","UAH","UGX","USD","UYI","UYU","UYW","UZS","VEF","VES","VND","VUV","WST","XAF","XBT","XCD","XOF","XPF","XSU","XUA","YER","ZAR","ZMW","ZWD","ZWL"];function nZ(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(r)return(r=r.call(t)).next.bind(r);if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return nz(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nz(t,e)}}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0;return function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function nz(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var n$=["firstidxpath","absolutexpath","threeleveltagclassxpath"],n0=["modelxpath"],n1=function(t,e){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var r={taskName:window.ttq._pf_tn,functionName:"updateParameterInferenceData",start:performance.now()}}catch(t){}try{var n=t.getPageInfo(),o=n9(n.url,e);n.url.includes("checkout")&&o&&on(o,n.url,e),o&&n4(o)}catch(t){A(aI.CUSTOM_ERROR,t,{custom_name:"updateParameterInferenceData"})}try{window.ttq&&window.ttq._ppf&&(r.end=performance.now(),window.ttq._ppf.push(r))}catch(t){}},n2=function(t){try{var e=t.plugins&&t.plugins.AutoConfig;return e&&e.vc_rules}catch(t){return}},n3=function(t){try{if(!t)return null;for(var e,r=nZ(t);!(e=r()).done;){var n=e.value;if(n.parameter_enhancement)return n.parameter_enhancement}}catch(t){t(aI.CUSTOM_ERROR,t,{custom_name:"updateParameterInferenceData",custom_enum:"getEnhancementRules"})}return null},n6=function(t){var e;try{if(!t)return null;var r=null,n=document.evaluate(t,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null).singleNodeValue;if(n){var o=(null==(e=n.textContent)?void 0:e.trim())||null;o&&(r=o)}return r}catch(t){return A(aI.CUSTOM_ERROR,t,{custom_name:"updateParameterInferenceData",custom_enum:"getEnhancementRuleDataByXPath"}),null}},n5=function(t){if(!t)return null;var e=t.split("|||").filter(function(t){return""!==t});if(e.length<2)return null;var r=e[0],n=e[1],o=e[2];return{pattern:r,replacement:n,flags:void 0===o?"":o}},n8=function(t,e){if(!t)return null;for(var r,n=null,o=nZ(e);!(r=o()).done&&!(n=n6(t[r.value])););if(!n)return null;var i=n,a=(null==t?void 0:t.format_method)&&n5(t.format_method);if(a)try{var c=a.pattern,s=a.replacement,u=a.flags,f=new RegExp(c,u);i=n.replace(f,s)}catch(t){A(aI.CUSTOM_ERROR,t,{custom_name:"updateParameterInferenceData",custom_enum:"parseFormatMethod"})}var l=Object.assign({},t,{parameterOriValue:n,parameterValue:i});return n$.forEach(function(t){delete l[t]}),n0.forEach(function(t){delete l[t]}),l},n4=function(t){try{var e=n3(t);if(!e)return null;var r={};for(var n in e)if(e.hasOwnProperty(n)){var o=e[n];if(o){var i=void 0;(i=(i=n8(o.helper_rule,n$))&&i.parameterValue?i:n8(o.model_rule,n0))&&(r[n]=Object.assign({},i),P(aI.CUSTOM_INFO,{custom_name:"updateParameterInferenceData",custom_enum:"enhancement_rule_result",extJSON:{message:JSON.stringify(o)+"||"+JSON.stringify(i)}}))}}return oo(os,r),P(aI.CUSTOM_INFO,{custom_name:"updateParameterInferenceData",custom_enum:"enhancement_properties",extJSON:{message:""+JSON.stringify(r)}}),Object.keys(r).length>0?r:null}catch(t){return A(aI.CUSTOM_ERROR,t,{custom_name:"updateParameterInferenceData",custom_enum:"enhancement_parameter_inference"}),null}},n9=function(t,e){var r=null;for(var n in e)if(t.includes(n))return e[n];return null},n7=function(t,e){var r=function(t,e){try{for(var r,n=document.evaluate(t,document,null,XPathResult.ORDERED_NODE_ITERATOR_TYPE,null),o=null;r=n.iterateNext();)/\d/.test(r.innerText)&&(o=r);if(!o&&e)for(var i=document.getElementsByClassName(e),a=0;a<i.length;a++){var c=i[a];c instanceof HTMLElement&&/\d/.test(c.innerText)&&(o=c)}return o}catch(t){return A(aI.CUSTOM_ERROR,t,{custom_name:"updateParameterInferenceData",custom_enum:"getLastElementWithNumber"}),null}}(t,e),n=null==r?void 0:r.textContent;return{priceNumberString:n?function(t){var e=t.match(/(?:\d[\d\s,.]*\d|\d)/g);if(e){for(var r,n,o,i,a=e[0],c=nZ(e);!(i=c()).done;)if(i.value!==a)return null;return r=a.replace(/[\s,\.]/g,""),n=Math.max(a.lastIndexOf("."),a.lastIndexOf(",")),o=!1,-1!==n&&n>=a.length-3&&(o=!0),o&&(r=r.slice(0,n-(a.length-1))+"."+r.slice(n-(a.length-1))),r}return null}(n):null,textContent:n}},ot=function(t){if(!t)return{currencyCode:null,currencyCodeFromeXpath:null};var e,r=null==(e=document.evaluate(t,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null).singleNodeValue)?void 0:e.textContent,n=((null==r?void 0:r.trim())?nQ.includes(r.toUpperCase().trim()):null)?null==r?void 0:r.toUpperCase().trim():null;return{currencyCode:r,currencyCodeFromeXpath:n}},oe=function(t){if(!t)return{countryCode:null,countryCodeFromXpath:null};var e,r=null==(e=document.evaluate(t,document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null).singleNodeValue)?void 0:e.textContent,n=((null==r?void 0:r.trim())?nX.includes(r.toUpperCase().trim()):null)?null==r?void 0:r.toUpperCase().trim():null;return{countryCode:r,countryCodeFromXpath:n}},or=function(t){try{var e=new URL(t).hostname.split(".");for(var r in e)if(nX.includes(e[r].toUpperCase()))return e[r].toUpperCase()}catch(t){}return null},on=function(t,e,r){try{if(t)for(var n,o=nZ(t);!(n=o()).done;){var i=n.value;if(i.valueXpath&&i.currency&&i.currency.val){var a=i.currency.val,c=n7(i.valueXpath,i.currency.val),s=c.textContent,u=c.priceNumberString;if(u&&s){var f=ot(i.currency.xpath),l=f.currencyCode,p=f.currencyCodeFromeXpath;l&&P(aI.CUSTOM_INFO,{custom_name:"auto_value_currency_currency_code_form_xpath",extJSON:{url:e,currencyCode:l,vcConfig:r}});var h=oe(i.countryCodeXpath),d=h.countryCode,v=h.countryCodeFromXpath;d&&P(aI.CUSTOM_INFO,{custom_name:"auto_value_currency_country_form_xpath",extJSON:{url:e,country:d,vcConfig:r}});var _={value:u,currency:p||a,ori_value:s,rule_key:i.rule_key,country_code:v};return v||(_.country_code=or(e)),P(aI.CUSTOM_INFO,{custom_name:"auto_value_currency_update_info",extJSON:{url:e,autoProperties:_,vcConfig:r}}),oo(oc,_),_}}}return null}catch(t){return A(aI.CUSTOM_ERROR,t,{custom_name:"updateParameterInferenceData",custom_enum:"updateAutoVCData"}),null}},oo=function(t,e){try{sessionStorage.setItem(t,JSON.stringify(e))}catch(t){}},oi=function(t){try{var e=sessionStorage.getItem(t);return e?JSON.parse(e):null}catch(t){return null}},oa=function(t){try{sessionStorage.removeItem(t)}catch(t){}},oc="value_currency_rule",os="parameter_inference_rule",ou=[a0.CLICK,a0.SCROLL],of=function(){function t(t){var e=this;this.handlerArray=t,ou.forEach(function(t){window.addEventListener(t.toLowerCase(),tc(function(){e.interactionHandler(t)},2e3,e),{capture:!0,passive:!0})})}var e=t.prototype;return e.iterateHandlerArray=function(t){this.handlerArray.forEach(function(e){return e(t)})},e.interactionHandler=function(t){var e=this;this.timeoutId&&clearTimeout(this.timeoutId),this.iterateHandlerArray(t),this.timeoutId=setTimeout(function(){e.iterateHandlerArray(t)},2e3)},t}(),ol=function(){function t(){this.history={}}var e=t.prototype;return e.hasReport=function(t,e,r){var n=this.genHistoryKey(t,e);return this.history[n]&&this.history[n].indexOf(r)>-1},e.addHistory=function(t,e,r){var n=this.genHistoryKey(t,e);this.history[n]||(this.history[n]=[]),this.history[n].push(r)},e.clearHistory=function(){this.history={}},e.genHistoryKey=function(t,e){return t+":"+e},t}(),op=function(){function t(t,e,r,n){this.context=t,this.reportHistory=new ol,this.reporters=e,this.reportService=r,this.ttqOptions=n}var e=t.prototype;return e.report=function(t,e,r){var n=this,o=e3(aH.AUTO_CONFIG),i=this.getReportPixelList(e,r),a=this.assemblyReportData(t,r,i);a&&o&&this.reportService.reportPreTasks.then(function(){n.reportService.report(o,a,aB.defaultReport,aY.P0)})},e.clearHistory=function(){this.reportHistory.clearHistory()},e.getReportPixelList=function(t,e){var r=this,n=JSON.stringify(Object.assign({},e,{page_trigger:void 0}));return this.reporters.filter(function(e){return!!nN(e)&&nR(e,t)}).filter(function(e){var o=e.getReporterId();return!([az.AUTO_COLLECTION,az.AUTO_FORM].indexOf(t)>-1&&r.reportHistory.hasReport(t,o,n))&&(r.reportHistory.addHistory(t,o,n),e)})},e.getCookieBasedSessions=function(t){var e=[],r=eV(document.cookie),n=t.filter(function(t){var e;return!!(null==(e=null==t?void 0:t.reporterInfo)?void 0:e.firstPartyCookieEnabled)});if(0===n.length)return e;var o=this.context.getPageCookieBasedSession();return n.forEach(function(t){var n=t.getReporterId();t.setCookieBasedSession(r[n]),e.push(eY(t.getCookieBasedSession(),o,n))}),e},e.assemblyReportData=function(t,e,r){if(0!==r.length){var n,o,i,a=r.map(function(t){return t.getReporterId()}),c=this.context.getPageSign(),s=(null==(o=null==(n=this.ttqOptions)?void 0:n.cookieBasedSessionConfig)?void 0:o.enable)?this.getCookieBasedSessions(r):[],u=r[0],f=u.assemblyData(u.getReporterId(),"",{},{},aH.AUTO_CONFIG);return delete f.event,f.action=t,f.auto_collected_properties=e,f.context.pixel||(f.context.pixel={}),f.context.pixel.code=a[0],f.context.pixel.codes=a.join("|"),f.context.index=null==(i=c.pageIndex)?void 0:i.index,f.context.session_id=c.sessionId,f.context.pageview_id=B(this.context.getPageViewId(),u.reporterInfo.loadId,"::"),f.context.sessions=s,f.message_id=f.message_id.replace(/-[^-]*$/,""),f}},t}(),oh=new Set([",","?",";","!","、","，","。","？","；","！",":","："]),od=[{triggerField:"class",fragmentType:"attribute",key:"class"},{triggerField:"id",fragmentType:"attribute",key:"id"},{triggerField:"name",fragmentType:"attribute",key:"name"},{triggerField:"type",fragmentType:"attribute",key:"type"},{triggerField:"value",fragmentType:"attribute",key:"value"},{triggerField:"destination",fragmentType:"attribute",key:"destination"},{triggerField:"inner_text",fragmentType:"text",key:"inner_text"},{triggerField:"image_url",fragmentType:"text",key:"image_url"}];(ah=a3||(a3={})).ID_SSN="ID_SSN",ah.PASSPORT="PASSPORT",ah.DRIVING_LICENSE="DRIVING_LICENSE",ah.PHONE="PHONE",ah.EMAIL="EMAIL",ah.CREDIT_CARD="CREDIT_CARD",ah.RACE_ETHNIC="RACE_ETHNIC",ah.RELIGION="RELIGION",ah.SEXUAL_ORIENTATION="SEXUAL_ORIENTATION",ah.POLITICAL_AFFILIATION="POLITICAL_AFFILIATION",ah.UNION_MEMBERSHIP="UNION_MEMBERSHIP",ah.CRIMINAL_RECORD="CRIMINAL_RECORD",ah.DEFAULT="***";var ov=((a2={})[a3.RACE_ETHNIC]=["asian","black","white","caucasian","hispanic","latino","arab","middle eastern","african american","native american","indigenous","pacific islander","jewish","afro","mulatto","mestizo","racial","ethnicity","ethnic group"],a2[a3.RELIGION]=["christian","catholic","protestant","baptist","orthodox","muslim","islam","islamic","sunni","shia","jewish","judaism","synagogue","hebrew","hindu","hinduism","vedic","bhagavad","buddhist","buddhism","taoism","shinto","atheist","agnostic","secular","religious belief"],a2[a3.SEXUAL_ORIENTATION]=["heterosexual","homosexual","bisexual","pansexual","gay","lesbian","lgbt","lgbtq","queer","asexual","sexual orientation","gender identity","trans","transgender","nonbinary","non-binary"],a2[a3.POLITICAL_AFFILIATION]=["democrat","democratic","republican","libertarian","conservative","liberal","progressive","nationalist","communist","socialism","socialist","marxist","left-wing","right-wing","maga","antifa","alt-right","green party","political party","political ideology"],a2[a3.UNION_MEMBERSHIP]=["labor union","trade union","union member","collective bargaining","strike","unionized","organized labor","workers' union","teamsters","AFL-CIO","IBEW","union dues","union card"],a2[a3.CRIMINAL_RECORD]=["arrest","arrested","convicted","conviction","felony","felon","misdemeanor","parole","probation","criminal record","jail","prison","inmate","suspect","indictment","charged with","mugshot"],a2),o_=Object.values(a3).reduce(function(t,e){return t[e]="<#"+e+"#>",t},{}),og={prefix:"(?<!<#)",suffix:"(?!(?:[^<]*>|[^>]*<))"},oy=oS([/(?:\+?\d{1,3}[\s-]?|\(\+?\d{1,3}\)[\s-]?)?(?:\d{2,4}[\s-]?|\(\d{2,4}\)[\s-]?)?(?:\d[\s-]?){5,}/g],og),om=oS([/([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9._-]+)/gi],og),ow=oS([/(?:(?:4[0-9]{3}|5[1-5][0-9]{2}|6011|65[0-9]{2}|3[47][0-9]{2}|3(?:0[0-5]|[68][0-9])|2131|1800|35\d{2})[-\s]?(?:[0-9]{4}[-\s]?){2}[0-9]{3,4}|62[0-9]{3}[-\s]?(?:[0-9]{4}[-\s]?){2,3}[0-9]{3,4})$(?![\d])/g],og),oE=oS([/\d{3}-\d{2}-\d{4}|\d{9}/g,/\d{17}(\d|X|x)$/g,/[A-CEGHJ-PR-TW-Z]{2}\d{6}[A-D]?/g,/(?:[A-Z]\d{10}|\d{10})/g,/[A-Z0-9]{12}/g],og),ob=oS([/[EDS]\d{8}/g,/[A-HJ-NP-Z][A-HJ-NP-Z0-9]{8}/g,/[A-Z]{2}\d{6}/g],og),oI=oS([/\d{17}[\dXx]/g,/[A-Z0-9]{16}/g,/[A-Z0-9]{10,12}/g,/[A-Z0-9]{12}/g],og),oO=function(t,e){if("string"!=typeof t)throw TypeError("text must be a non-empty string");if("number"!=typeof e||e<=0)throw TypeError("targetLength must be a positive integer");for(var r=[],n=0,o=t.length;n<o;){var i=Math.min(n+e,o);if(i===o){var a=t.slice(n).trim();a&&r.push(a);break}if(oT(t[i]||"")){var c=oN(t,i),s=c.leftBound,u=c.rightBound;if(i-s<=u-i){var f=t.slice(n,s+1).trim();f&&r.push(f);var l=t.slice(s+1,u+1).trim();l&&r.push(l),n=u+1}else{var p=t.slice(n,u+1).trim();p&&r.push(p),n=u+1}}else{var h=t.slice(n,i).trim();h&&r.push(h),n=i}}return r},oT=function(t){if(1!==t.length)throw TypeError("char must be a single character");return!oh.has(t)},oN=function(t,e){for(var r=e,n=e;r>0&&oT(t[r]);)r--;for(;n<t.length&&oT(t[n]);)n++;return{leftBound:r,rightBound:n}},oR=function(t){return o_[t]||o_[a3.DEFAULT]};function oS(t,e){return void 0===e&&(e={prefix:"",suffix:""}),t.map(function(t){var r,n;return t.source.startsWith("^")&&t.source.endsWith("$")?t:(r=e.prefix,n=e.suffix,void 0===r&&(r=""),void 0===n&&(n=""),RegExp(""+r+t.source+n,t.flags))})}var oP=function(){function t(){}var e=t.prototype;return e.decompose=function(t){for(var e=[],r=0,n=0,o=t.length,i="text",a="",c="";n<o;){var s=t[n];"text"===i?"<"===s?(r=this.processTextBuffer(c,e,r),c="",i="tag",a="<"):c+=s:(a+=s,">"===s&&(r=this.parseTag(a,e,r),i="text",a="")),n++}return r=this.processTextBuffer(c,e,r),a&&(r=this.parseTag(a,e,r)),e},e.processTextBuffer=function(t,e,r){var n=t.trim();if(!n)return r;if(n.length<=30){var o={type:"text",value:n,position:r++};return e.push(o),r}return oO(n,30).forEach(function(t){var n={type:"text",value:t,position:r++};e.push(n)}),r},e.parseTag=function(t,e,r){var n=r;if(!t)return n;if(t.startsWith("</")){var o={type:"tagEnd",name:t.slice(2,-1).trim(),position:n++};return e.push(o),n}var i=t.slice(1,-1).trim(),a=i.indexOf(" ");if(-1===a){var c={type:"tagStart",name:i,position:n++},s={type:"tagStartClose",name:i,position:n++};return e.push(c,s),n}var u=i.slice(0,a).trim(),f=i.slice(a+1).trim(),l=this.parseAttributes(f),p={type:"tagStart",name:u,position:n++};e.push(p),l.forEach(function(t){var r={type:"attribute",key:t[0],value:t[1],position:n++};e.push(r)});var h={type:"tagStartClose",name:u,position:n++};return e.push(h),n},e.parseAttributes=function(t){for(var e,r,n,o,i=[],a=/([\w-]+)(?:\s*=\s*(?:"([^"]*)"|'([^']*)'|([^\s'"]+)))?/g;null!==(o=a.exec(t));){var c=o[1].trim(),s=null!=(n=null!=(r=null!=(e=o[2])?e:o[3])?r:o[4])?n:"";i.push([c,s])}return i},t}();function oA(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(r)return(r=r.call(t)).next.bind(r);if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return ox(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ox(t,e)}}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0;return function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ox(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var oL=function(){function t(){}return t.prototype.decompose=function(t){for(var e,r,n=[],o=0,i=oA(od);!(r=i()).done;){var a=r.value,c=a.triggerField,s=a.fragmentType,u=a.key,f=void 0;if(void 0!==(f="attribute"===s?null==(e=t.attributes)?void 0:e[c]:t[c]))if(f.length>=30)for(var l,p=oO(f,30),h=oA(p);!(l=h()).done;){var d=l.value;n.push({type:s,key:u,value:d,position:o++})}else n.push({type:s,key:u,value:f,position:o++})}return n},t}();function oC(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var ok=function(){function t(){}return t.prototype.recompose=function(t){for(var e,r="",n=function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(r)return(r=r.call(t)).next.bind(r);if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return oC(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return oC(t,e)}}(t))){r&&(t=r);var n=0;return function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(t);!(e=n()).done;){var o=e.value;switch(o.type){case"tagStart":r+="<"+o.name;break;case"attribute":r+=" "+o.key+'="'+o.redactedValue+'"';break;case"tagStartClose":r+=">";break;case"text":r+=o.redactedValue;break;case"tagEnd":r+="</"+o.name+">"}}return r},t}();function oD(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var oj=function(){function t(){}return t.prototype.recompose=function(t,e){var r=Object.assign({},e);r.attributes=Object.assign({},e.attributes||{});for(var n,o=new window[window.TiktokAnalyticsObject || "ttq"]._ttq_map,i=function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(r)return(r=r.call(t)).next.bind(r);if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return oD(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return oD(t,e)}}(t))){r&&(t=r);var n=0;return function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(t);!(n=i()).done;){var a=n.value;if(a.redactedValue)switch(a.type){case"attribute":o.has(a.key)?r.attributes[a.key]+=a.redactedValue:(o.set(a.key,!0),r.attributes[a.key]=a.redactedValue);break;case"text":o.has(a.key)?r[a.key]+=a.redactedValue:(o.set(a.key,!0),r[a.key]=a.redactedValue)}}return r},t}();function oM(){oM=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function u(t,e,r,o){var i,a,c,s,u=Object._ttq_create((e&&e.prototype instanceof p?e:p).prototype);return n(u,"_invoke",{value:(i=t,a=r,c=new I(o||[]),s="suspendedStart",function(t,e){if("executing"===s)throw Error("Generator is already running");if("completed"===s){if("throw"===t)throw e;return T()}for(c.method=t,c.arg=e;;){var r=c.delegate;if(r){var n=function t(e,r){var n=r.method,o=e.iterator[n];if(void 0===o)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=void 0,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+n+"' method")),l;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,l;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=void 0),r.delegate=null,l):a:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,l)}(r,c);if(n){if(n===l)continue;return n}}if("next"===c.method)c.sent=c._sent=c.arg;else if("throw"===c.method){if("suspendedStart"===s)throw s="completed",c.arg;c.dispatchException(c.arg)}else"return"===c.method&&c.abrupt("return",c.arg);s="executing";var o=f(i,a,c);if("normal"===o.type){if(s=c.done?"completed":"suspendedYield",o.arg===l)continue;return{value:o.arg,done:c.done}}"throw"===o.type&&(s="completed",c.method="throw",c.arg=o.arg)}})}),u}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var l={};function p(){}function h(){}function d(){}var v={};s(v,i,function(){return this});var _=Object.getPrototypeOf,g=_&&_(_(O([])));g&&g!==e&&r.call(g,i)&&(v=g);var y=d.prototype=p.prototype=Object._ttq_create(v);function m(t){["next","throw","return"].forEach(function(e){s(t,e,function(t){return this._invoke(e,t)})})}function w(t,e){var o;n(this,"_invoke",{value:function(n,i){function a(){return new e(function(o,a){!function n(o,i,a,c){var s=f(t[o],t,i);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(l).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(n,i,o,a)})}return o=o?o.then(a,a):a()}})}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function b(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function O(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:T}}function T(){return{value:void 0,done:!0}}return h.prototype=d,n(y,"constructor",{value:d,configurable:!0}),n(d,"constructor",{value:h,configurable:!0}),h.displayName=s(d,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,s(t,c,"GeneratorFunction")),t.prototype=Object._ttq_create(y),t},t.awrap=function(t){return{__await:t}},m(w.prototype),s(w.prototype,a,function(){return this}),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(u(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then(function(t){return t.done?t.value:a.next()})},m(y),s(y,c,"Generator"),s(y,i,function(){return this}),s(y,"toString",function(){return"[object Generator]"}),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=O,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),b(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;b(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:O(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},t}var oF=function(){function t(t,e,r,n){this.replaceTag=r||a3.DEFAULT,this.regexArray=[].concat(t),this.type=n||"DEFAULT_TYPE",this.keyWords=e}return t.prototype.redact=function(t,e,r){var n,o,i,a;return n=this,o=void 0,i=void 0,a=oM().mark(function n(){var o,i,a,c,s=this;return oM().wrap(function(n){for(;;)switch(n.prev=n.next){case 0:if(!(t&&t.startsWith("<KEYWORD_"))){n.next=2;break}return n.abrupt("return",t);case 2:if(o=t,!(this.keyWords&&this.keyWords.length&&Array.isArray(this.keyWords))){n.next=7;break}this.keyWords.forEach(function(t){var e=RegExp(t,"gi");o=o.replaceAll(e,oR(s.replaceTag))}),n.next=15;break;case 7:if(!(this.keyWords&&"object"==typeof this.keyWords)){n.next=15;break}i=oM().mark(function t(){var e,r;return oM().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:r=(e=c[a])[0],e[1].forEach(function(t){var e=RegExp(t,"gi");o=o.replaceAll(e,oR(r))});case 2:case"end":return t.stop()}},t)}),a=0,c=Object.entries(this.keyWords);case 10:if(!(a<c.length)){n.next=15;break}return n.delegateYield(i(),"t0",12);case 12:a++,n.next=10;break;case 15:return this.regexArray.length&&(o=this.regexArray.reduce(function(t,e){return t.replaceAll(e,oR(s.replaceTag))},o)),n.next=18,e(o,r);case 18:return o=n.sent,n.abrupt("return",o);case 20:case"end":return n.stop()}},n,this)}),new(i||(i=Promise))(function(t,e){function r(t){try{s(a.next(t))}catch(t){e(t)}}function c(t){try{s(a.throw(t))}catch(t){e(t)}}function s(e){var n;e.done?t(e.value):((n=e.value)instanceof i?n:new i(function(t){t(n)})).then(r,c)}s((a=a.apply(n,o||[])).next())})},t}();function oq(){oq=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function u(t,e,r,o){var i,a,c,s,u=Object._ttq_create((e&&e.prototype instanceof p?e:p).prototype);return n(u,"_invoke",{value:(i=t,a=r,c=new I(o||[]),s="suspendedStart",function(t,e){if("executing"===s)throw Error("Generator is already running");if("completed"===s){if("throw"===t)throw e;return T()}for(c.method=t,c.arg=e;;){var r=c.delegate;if(r){var n=function t(e,r){var n=r.method,o=e.iterator[n];if(void 0===o)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=void 0,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+n+"' method")),l;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,l;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=void 0),r.delegate=null,l):a:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,l)}(r,c);if(n){if(n===l)continue;return n}}if("next"===c.method)c.sent=c._sent=c.arg;else if("throw"===c.method){if("suspendedStart"===s)throw s="completed",c.arg;c.dispatchException(c.arg)}else"return"===c.method&&c.abrupt("return",c.arg);s="executing";var o=f(i,a,c);if("normal"===o.type){if(s=c.done?"completed":"suspendedYield",o.arg===l)continue;return{value:o.arg,done:c.done}}"throw"===o.type&&(s="completed",c.method="throw",c.arg=o.arg)}})}),u}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var l={};function p(){}function h(){}function d(){}var v={};s(v,i,function(){return this});var _=Object.getPrototypeOf,g=_&&_(_(O([])));g&&g!==e&&r.call(g,i)&&(v=g);var y=d.prototype=p.prototype=Object._ttq_create(v);function m(t){["next","throw","return"].forEach(function(e){s(t,e,function(t){return this._invoke(e,t)})})}function w(t,e){var o;n(this,"_invoke",{value:function(n,i){function a(){return new e(function(o,a){!function n(o,i,a,c){var s=f(t[o],t,i);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(l).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(n,i,o,a)})}return o=o?o.then(a,a):a()}})}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function b(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function O(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:T}}function T(){return{value:void 0,done:!0}}return h.prototype=d,n(y,"constructor",{value:d,configurable:!0}),n(d,"constructor",{value:h,configurable:!0}),h.displayName=s(d,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,s(t,c,"GeneratorFunction")),t.prototype=Object._ttq_create(y),t},t.awrap=function(t){return{__await:t}},m(w.prototype),s(w.prototype,a,function(){return this}),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(u(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then(function(t){return t.done?t.value:a.next()})},m(y),s(y,c,"Generator"),s(y,i,function(){return this}),s(y,"toString",function(){return"[object Generator]"}),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=O,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),b(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;b(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:O(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},t}var oU=function(t,e,r,n){return new(r||(r=Promise))(function(o,i){function a(t){try{s(n.next(t))}catch(t){i(t)}}function c(t){try{s(n.throw(t))}catch(t){i(t)}}function s(t){var e;t.done?o(t.value):((e=t.value)instanceof r?e:new r(function(t){t(e)})).then(a,c)}s((n=n.apply(t,e||[])).next())})},oG=function(){function t(){this.middlewareRedactors=[],this.sensitive_data_found=!1,this.htmlDecomposer=new oP,this.elementDecomposer=new oL,this.htmlRecomposer=new ok,this.elementRecomposer=new oj}var e=t.prototype;return e.registerRedactor=function(t){if("function"!=typeof(null==t?void 0:t.redact))throw Error("Middleware must implement `redact(value: string, next: NextFunction): Promise<string>` method");this.middlewareRedactors.push(t)},e.process=function(t){return oU(this,void 0,void 0,oq().mark(function e(){var r,n,o,i,a,c=this;return oq().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(this.sensitive_data_found=!1,"string"==typeof t||"object"==typeof t){e.next=3;break}throw Error("输入格式错误：必须为字符串或对象");case 3:if("object"!=typeof t||null!==t){e.next=5;break}throw Error("输入格式错误：对象不能为null");case 5:return"string"==typeof t?(r=this.htmlDecomposer,n=this.htmlRecomposer):(r=this.elementDecomposer,n=this.elementRecomposer),e.prev=6,o=r.decompose(t),e.next=10,Promise.all(o.map(function(t){return oU(c,void 0,void 0,oq().mark(function e(){var r,n,o;return oq().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!(null==(r=t.type)?void 0:r.includes("tag"))){e.next=2;break}return e.abrupt("return",Object.assign(Object.assign({},t),{redactedValue:""}));case 2:return n=t.value||"",e.next=5,this.dispatch(0,n);case 5:return(o=e.sent)!==n&&(this.sensitive_data_found=!0),e.abrupt("return",Object.assign(Object.assign({},t),{redactedValue:o}));case 8:case"end":return e.stop()}},e,this)}))}));case 10:return i=e.sent,a=n.recompose(i,t),e.abrupt("return",a);case 15:throw e.prev=15,e.t0=e.catch(6),e.t0;case 18:case"end":return e.stop()}},e,this,[[6,15]])}))},e.createRedactor=function(t,e,r,n){void 0===t&&(t=[]),void 0===e&&(e=[]);var o=new oF(t,e,r);return n&&(o.redact=n),o},e.dispatch=function(t,e){return oU(this,void 0,void 0,oq().mark(function r(){var n,o,i,a=this;return oq().wrap(function(r){for(;;)switch(r.prev=r.next){case 0:if(n=this.middlewareRedactors[t]){r.next=3;break}return r.abrupt("return",e);case 3:return o=function(r){return void 0===r&&(r=e),oU(a,void 0,void 0,oq().mark(function e(){return oq().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.abrupt("return",this.dispatch(t+1,r));case 1:case"end":return e.stop()}},e,this)}))},r.prev=4,r.next=7,n.redact(e,o);case 7:return i=r.sent,r.abrupt("return",i);case 11:return r.prev=11,r.t0=r.catch(4),r.abrupt("return",o(e));case 14:case"end":return r.stop()}},r,this,[[4,11]])}))},e.isHasSensitiveData=function(){return this.sensitive_data_found},t}();function oH(){oH=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function u(t,e,r,o){var i,a,c,s,u=Object._ttq_create((e&&e.prototype instanceof p?e:p).prototype);return n(u,"_invoke",{value:(i=t,a=r,c=new I(o||[]),s="suspendedStart",function(t,e){if("executing"===s)throw Error("Generator is already running");if("completed"===s){if("throw"===t)throw e;return T()}for(c.method=t,c.arg=e;;){var r=c.delegate;if(r){var n=function t(e,r){var n=r.method,o=e.iterator[n];if(void 0===o)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=void 0,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+n+"' method")),l;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,l;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=void 0),r.delegate=null,l):a:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,l)}(r,c);if(n){if(n===l)continue;return n}}if("next"===c.method)c.sent=c._sent=c.arg;else if("throw"===c.method){if("suspendedStart"===s)throw s="completed",c.arg;c.dispatchException(c.arg)}else"return"===c.method&&c.abrupt("return",c.arg);s="executing";var o=f(i,a,c);if("normal"===o.type){if(s=c.done?"completed":"suspendedYield",o.arg===l)continue;return{value:o.arg,done:c.done}}"throw"===o.type&&(s="completed",c.method="throw",c.arg=o.arg)}})}),u}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var l={};function p(){}function h(){}function d(){}var v={};s(v,i,function(){return this});var _=Object.getPrototypeOf,g=_&&_(_(O([])));g&&g!==e&&r.call(g,i)&&(v=g);var y=d.prototype=p.prototype=Object._ttq_create(v);function m(t){["next","throw","return"].forEach(function(e){s(t,e,function(t){return this._invoke(e,t)})})}function w(t,e){var o;n(this,"_invoke",{value:function(n,i){function a(){return new e(function(o,a){!function n(o,i,a,c){var s=f(t[o],t,i);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(l).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(n,i,o,a)})}return o=o?o.then(a,a):a()}})}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function b(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function O(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:T}}function T(){return{value:void 0,done:!0}}return h.prototype=d,n(y,"constructor",{value:d,configurable:!0}),n(d,"constructor",{value:h,configurable:!0}),h.displayName=s(d,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,s(t,c,"GeneratorFunction")),t.prototype=Object._ttq_create(y),t},t.awrap=function(t){return{__await:t}},m(w.prototype),s(w.prototype,a,function(){return this}),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(u(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then(function(t){return t.done?t.value:a.next()})},m(y),s(y,c,"Generator"),s(y,i,function(){return this}),s(y,"toString",function(){return"[object Generator]"}),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=O,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),b(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;b(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:O(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},t}function oV(t,e,r,n,o,i,a){try{var c=t[i](a),s=c.value}catch(t){r(t);return}c.done?e(s):Promise.resolve(s).then(n,o)}function oB(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function oY(t,e){return(oY=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}(ad=a6||(a6={})).V1="v1",ad.V2="v2";var oK=function(t,e){return function(r,n){e(r,n,t)}},oW="AutoConfig",oJ=function(t){function e(e,r,n,o){var i,a,c;return(i=t.call(this,{name:oW,reporters:r,context:e})||this).autoCollectedMetadata={},i.initialize=!1,i.autoFormUpdateHandler=tc(function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var e={taskName:window.ttq._pf_tn||"auto_config_form_handler",functionName:window.ttq._pf_tn&&"auto_config_form_handler",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="auto_config_form_handler")}}catch(t){}if(i.autoForm){if(i.autoCollectedFormDetail=i.autoForm.getOverallFormDetail(),i.autoCollectedFormDetail.length>3e4)return void A(aI.CUSTOM_ERROR,{message:""+String(i.autoCollectedFormDetail.length)},{custom_name:nE,custom_enum:aQ.OVER_LENGTH});i.actTracker.report(aZ.METADATA,az.AUTO_FORM,{page_trigger:t,form_details:i.autoCollectedFormDetail})}try{window.ttq&&window.ttq._ppf&&(e.end=performance.now(),window.ttq._ppf.push(e),"auto_config_form_handler"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}},200,oB(i)),i.autoCollectionUpdateHandler=tc(function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var e={taskName:window.ttq._pf_tn||"auto_config_metadata_handler",functionName:window.ttq._pf_tn&&"auto_config_metadata_handler",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="auto_config_metadata_handler")}}catch(t){}i.autoCollectedMetadata=function(t){var e={open_graph:"{}",microdata:"[]",json_ld:"[]",meta:"{}"};try{e.microdata=function(){if(!nC.getMetaData)return"[]";for(var t=document.querySelectorAll("[itemscope]"),e=[],r=nD(),n=0;n<t.length;n++)r.add(t[n]);for(var o=t.length-1;o>=0;o--){var i=t[o],a=i.getAttribute("itemtype");if("string"==typeof a&&""!==a){for(var c={},s=i.querySelectorAll("[itemprop]"),u=0;u<s.length;u++){var f=s[u];if(!r.has(f)){r.add(f);var l=f.getAttribute("itemprop");if("string"==typeof l&&""!==l){var p=function(t){var e;switch(t.tagName.toLowerCase()){case"meta":e=t.getAttribute("content");break;case"audio":case"embed":case"iframe":case"img":case"source":case"track":case"video":e=t.getAttribute("src");break;case"a":case"area":case"link":e=t.getAttribute("href");break;case"object":e=t.getAttribute("data");break;case"data":case"meter":e=t.getAttribute("value");break;case"time":e=t.getAttribute("datetime");break;default:e=function(t){if(t){if(t.innerText&&t.innerText.length>0)return t.innerText;if(t.textContent&&t.textContent.length>0)return t.textContent}return""}(t)||""}return"string"==typeof e?nk(e):""}(f);if(null!=p){var h=c[l];null!=h&&function(t,e){return null!=[{property:"image",type:"Product"}].filter(function(r){return(t==="https://schema.org/"+r.type||t==="http://schema.org/"+r.type)&&r.property===e})[0]}(a,l)?Array.isArray(h)?c[l].push(p):c[l]=[h,p]:c[l]=p}}}}e.unshift({schema:{dimensions:{h:i.clientHeight,w:i.clientWidth},properties:nM(a)?{}:c,subscopes:[],type:a},scope:i})}}for(var d=[],v=[],_=0;_<e.length;_++){for(var g=e[_],y=g.scope,m=g.schema,w=d.length-1;w>=0;w--)if(d[w].scope.contains(y)){d[w].schema.subscopes.push(m);break}else d.pop();0===d.length&&v.push(m),d.push({schema:m,scope:y})}var E=JSON.stringify(v);return E.length>3e4&&(E="[]",nC.getMicroData=!1),E}()}catch(t){A(aI.CUSTOM_ERROR,t,{custom_name:"assemble_auto_config_failed",custom_enum:"microdata"})}try{var r=function(){if(!nC.getJSONLD)return{data:"[]",errors:[]};for(var t=[],e=[],r=document.querySelectorAll('script[type="application/ld+json"]'),n=0,o=0;o<r.length;o++){var i=r[o].innerText;if(null!=i&&""!==i){if((n+=i.length)>3e4)return nC.getJSONLD=!1,{data:JSON.stringify([]),errors:[{name:aX.OVER_LENGTH,message:""+String(n)}]};var a=void 0;try{a=JSON.parse(i.replace(/[\n\r\t]+/g," "))}catch(t){e.push({name:aX.ERROR_FORMAT,message:t.message})}try{a=function t(e){if("object"!=typeof e)return e;if(Array.isArray(e))return e.map(function(e){return t(e)});var r=Object.assign({},e),n=r["@type"];for(var o in r)"@type"!==o&&"@context"!==o&&("object"==typeof r[o]?r[o]=t(r[o]):n&&nM(n)&&delete r[o]);return r}(a)}catch(t){return{data:JSON.stringify([]),errors:[{name:aX.FILTER_SENSITIVE_FIELDS,message:t.message}]}}a&&t.push(a)}}return{data:JSON.stringify(t),errors:e}}(),n=r.data,o=r.errors;e.json_ld=n,o&&o.forEach(function(t){var e=t.name,r=t.message;A(aI.CUSTOM_ERROR,{message:r},{custom_name:"parse_json_ld_failed",custom_enum:e})})}catch(t){A(aI.CUSTOM_ERROR,t,{custom_name:"assemble_auto_config_failed",custom_enum:"json_ld"})}try{e.open_graph=function(t){if(!nC.getOpenGraph)return"{}";for(var e=nD(["og","product","music","video","article","book","profile","website","twitter"]),r={},n=document.querySelectorAll("meta[property],meta[name]"),o=0;o<n.length;o++){var i=n[o],a=i.getAttribute("property")||i.getAttribute("name"),c=nk(i.getAttribute("content"));if("string"==typeof a&&-1!==a.indexOf(":")&&"string"==typeof c&&e.has(a.split(":")[0])){var s=r[a];null!=s&&function(t){return null!=["og:image"].filter(function(e){return e===t})[0]}(a)?Array.isArray(s)?r[a].push(c):r[a]=[s,c]:r[a]=c}}return JSON.stringify(nj(r,t))}(t.open_graph)}catch(t){A(aI.CUSTOM_ERROR,t,{custom_name:"assemble_auto_config_failed",custom_enum:"open_graph"})}try{e.meta=function(t){if(!nC.getMetaData)return"{}";var e={},r=nD(["description","keywords","keyword"]),n=document.querySelector("title");n&&(e.title=nk(n.innerText));for(var o=document.querySelectorAll("meta[property],meta[name]"),i=0;i<o.length;i++){var a=o[i],c=a.getAttribute("name")||a.getAttribute("property"),s=nk(a.getAttribute("content"));"string"==typeof c&&"string"==typeof s&&r.has(c)&&(e[c]=s)}return JSON.stringify(nj({title:e.title,"meta:description":e.description,"meta:keywords":e.keywords||e.keyword},t))}(t.meta)}catch(t){A(aI.CUSTOM_ERROR,t,{custom_name:"assemble_auto_config_failed",custom_enum:"meta"})}return e}(i.filter),i.actTracker.report(aZ.METADATA,az.AUTO_COLLECTION,{page_trigger:t,content_data:i.autoCollectedMetadata});try{window.ttq&&window.ttq._ppf&&(e.end=performance.now(),window.ttq._ppf.push(e),"auto_config_metadata_handler"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}},200,oB(i)),a=oH().mark(function t(e){var r,n,o;return oH().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(t.prev=0,i.signal_insights_config&&n1(i.context,i.signal_insights_config),!(r=nS(e.target))){t.next=11;break}return n=nL(nx(r)),t.next=7,i.sensitiveFilterRedactor.process(n);case 7:o=t.sent,function(t){var e,r,n=(null==(e=t.inner_text)?void 0:e.toString().toLowerCase())||"",o=(null==(r=t.image_url)?void 0:r.toString().toLowerCase())||"",i=[];if(t.attributes){var a=t.attributes;i=[a.class,a.id,a.name,a.type,a.value,a.destination].map(function(t){return(null==t?void 0:t.toString().toLowerCase())||""})}return[n,o].concat(i).some(nI)}(n)&&(o.filter_version=a6.V1),i.sensitiveFilterRedactor.isHasSensitiveData()&&(o.filter_version=(o.filter_version?o.filter_version+",":"")+a6.V2),i.reportButtonClick(o);case 11:t.next=16;break;case 13:t.prev=13,t.t0=t.catch(0),A(aI.CUSTOM_ERROR,t.t0,{custom_name:"auto_click_callback_error",custom_enum:"auto_click"});case 16:case"end":return t.stop()}},t,null,[[0,13]])}),c=function(){var t=this,e=arguments;return new Promise(function(r,n){var o=a.apply(t,e);function i(t){oV(o,r,n,i,c,"next",t)}function c(t){oV(o,r,n,i,c,"throw",t)}i(void 0)})},i.autoClickCallback=function(t){return c.apply(this,arguments)},i.filter=n.auto_config||{open_graph:[],microdata:[],json_ld:[],meta:[]},i.actTracker=new op(e,r,o,n),i}e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,oY(e,t);var r=e.prototype;return r.initBaseFeature=function(){!this.initialize&&this.reporters.some(function(t){return nN(t)})&&(this.initAutoClick(),this.initAutoCollection(),this.initInteractionListener(),this.initialize=!0)},r.initExtraFeature=function(){!this.autoForm&&this.initialize&&this.reporters.some(function(t){return nR(t,az.AUTO_FORM)})&&this.initAutoForm(),this.initAutoVC()},r.initInteractionListener=function(){var t=this;this.reporters.some(function(t){var e=t.options;return e&&!1!==e.autoConfigListener})&&new of([function(e){nT(t.autoCollectionUpdateHandler,t)(e)},function(e){nT(t.autoFormUpdateHandler,t)(e)}])},r.initAutoClick=function(){t=this.autoClickCallback,e=Y(function(e){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var r={taskName:window.ttq._pf_tn||"auto_config_click_handler",functionName:window.ttq._pf_tn&&"auto_config_click_handler",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="auto_config_click_handler")}}catch(t){}t(e);try{window.ttq&&window.ttq._ppf&&(r.end=performance.now(),window.ttq._ppf.push(r),"auto_config_click_handler"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}},1e3),document.querySelectorAll(tf.join(", ")).forEach(function(t){tl.some(function(e){return t.matches(e)})||t.addEventListener("click",e,{capture:!0})});try{this.sensitiveFilterRedactor=new oG;var t,e,r=this.sensitiveFilterRedactor.createRedactor(om,[],a3.EMAIL),n=this.sensitiveFilterRedactor.createRedactor(ow,[],a3.CREDIT_CARD),o=this.sensitiveFilterRedactor.createRedactor(oE,[],a3.ID_SSN),i=this.sensitiveFilterRedactor.createRedactor(ob,[],a3.PASSPORT),a=this.sensitiveFilterRedactor.createRedactor(oI,[],a3.DRIVING_LICENSE),c=this.sensitiveFilterRedactor.createRedactor(oy,[],a3.PHONE),s=this.sensitiveFilterRedactor.createRedactor([],ov);this.sensitiveFilterRedactor.registerRedactor(s),this.sensitiveFilterRedactor.registerRedactor(r),this.sensitiveFilterRedactor.registerRedactor(n),this.sensitiveFilterRedactor.registerRedactor(o),this.sensitiveFilterRedactor.registerRedactor(i),this.sensitiveFilterRedactor.registerRedactor(a),this.sensitiveFilterRedactor.registerRedactor(c)}catch(t){A(aI.CUSTOM_ERROR,t,{custom_name:"auto_click_init_error",custom_enum:"sensitive_init_error"})}},r.initAutoVC=function(){var t=this;this.reporters.find(function(e){var r=n2(e);return!!r&&(t.signal_insights_config=r,!0)})},r.initAutoCollection=function(){},r.initAutoForm=function(){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var t={taskName:window.ttq._pf_tn,functionName:"initAutoForm",start:performance.now()}}catch(t){}this.autoForm=new nJ(this.reporters),this.autoForm.addFormUpdateHandler(this.autoFormUpdateHandler.bind(this)),this.autoCollectedFormDetail=this.autoForm.getOverallFormDetail();try{window.ttq&&window.ttq._ppf&&(t.end=performance.now(),window.ttq._ppf.push(t))}catch(t){}},r.pixelDidMount=function(){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var t={taskName:window.ttq._pf_tn,functionName:"auto_config_plugin_pixelDidMount",start:performance.now()}}catch(t){}this.initBaseFeature(),this.initExtraFeature();try{window.ttq&&window.ttq._ppf&&(t.end=performance.now(),window.ttq._ppf.push(t))}catch(t){}},r.patchEnd=function(){this.initBaseFeature(),this.initExtraFeature()},r.pixelSend=function(t,e,r){var n=this.reporters.find(function(e){return e.getReporterId()===t});if(this.signal_insights_config&&n1(this.context,this.signal_insights_config),n&&n2(n)&&(oi(oc)||oi(os))){(z(e)||"Pageview"===e||"InitiateCheckout"===e||"PlaceAnOrder"===e)&&(o=oi(oc));var o,i=oi(os);r.auto_collected_properties={vc_properties:Object.assign(Object.assign({},o&&{value:o.value,currency:o.currency,ori_value:o.ori_value,rule_key:o.rule_key}),i&&{parameter_inference:i})}}z(e)&&setTimeout(function(){oa(oc)},500),"Pageview"===e&&(!n||nN(n))&&(nT(this.autoCollectionUpdateHandler,this)(a0.PAGE_VIEW),nT(this.autoFormUpdateHandler,this)(a0.PAGE_VIEW))},r.pageUrlDidChange=function(t,e){null!=e&&""!=e&&(this.autoCollectionUpdateHandler(a0.URL_CHANGE),this.autoFormUpdateHandler(a0.URL_CHANGE))},r.pageWillLeave=function(){this.autoCollectionUpdateHandler(a0.PAGE_LEAVE),this.autoFormUpdateHandler(a0.PAGE_LEAVE)},r.reportButtonClick=function(t){this.actTracker.report(aZ.CLICK,az.AUTO_VC,{page_trigger:a0.CLICK,trigger_element:t,vc_properties:oi(oc)?oi(oc):void 0}),this.actTracker.report(aZ.CLICK,az.AUTO_VC_REVERSE,{page_trigger:a0.CLICK,trigger_element:t})},e}(r9);oJ=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er(),oK(0,en(tK.CONTEXT)),oK(1,en(tK.TTQ_REPORTERS)),oK(2,en(tK.TTQ_GLOBAL_OPTIONS)),oK(3,en(tK.REPORT_SERVICE))],oJ);var oX=r(278),oQ=r.n(oX),oZ={EMPTY_EVENT_TYPE_NAME:{title:"Missing Event Name",desc:"The event name for one or more of your events is empty. This can affect the accuracy of reporting for your conversions.",suggestion:"Go to your source code and add a name that follows our format requirements and TikTok policies.",link:"https://ads.tiktok.com/marketing_api/docs?rid=5ipocbxyw8v&id=1701890973258754"},INVALID_CONTENT_ID:{title:"Missing value for content ID",desc:"Include a value for your 'content_id' parameter. This is required for Video Shopping Ads (VSA).",suggestion:"If you are or plan to run Video Shopping Ads (VSA), go to your source code and include a value for the 'content_id' parameter.",link:"https://ads.tiktok.com/help/article/standard-events-parameters?redirected=2"},INVALID_CONTENT_TYPE:{title:"Invalid content type",desc:'The content type for one or more of your events is not valid. Content type must be either "product", "product_group", "destination", "hotel", "flight" or "vehicle".',suggestion:"Go to your source code and update the content type.",link:"https://ads.tiktok.com/help/article/standard-events-parameters?redirected=2"},INVALID_CURRENCY_CODE:{title:"Invalid currency code",desc:"The currency code for one or more of your events isn't supported. This can affect the accuracy of reporting for your ROAS.",suggestion:"Go to your source code and update the 'currency' parameters with a supported currency code.",link:"https://ads.tiktok.com/help/article/standard-events-parameters?redirected=2"},INVALID_EMAIL_FORMAT:{title:"Incorrect email format",desc:"The email format for your events does not match the format supported. This can impact Advanced Matching and your ad performance.",suggestion:"Go to your source code and update the format of your shared emails. It should follow '<EMAIL>' format.",link:"https://ads.tiktok.com/marketing_api/docs?id=1739585700402178"},INVALID_EMAIL_INFORMATION:{title:"Invalid email information",desc:"The emails shared with your events were invalid.",suggestion:'Go to your source code to double check shared emails. Leave your string empty when customer information isn\'t available. Avoid spaces, "undefined", or other hardcoded values.',link:"https://ads.tiktok.com/marketing_api/docs?id=1739585700402178"},INVALID_EVENT_PARAMETER_VALUE:{title:"Invalid value parameter",desc:"The 'value' parameter for one or more of your events is invalid. This is used calculate ROAS for people and the bid for your highest value customers. Parameters must be an integer or in the decimal format (e.g. 9.99). Also, they can't contain currency symbols, special characters, letters, or commas.",suggestion:"Go to your source code and update the 'value' parameter. It can only include numbers greater than or equal to zero (e.g. 9.99). Do not include currency symbols, special characters, letters, or commas.",link:"https://ads.tiktok.com/help/article/standard-events-parameters?redirected=2"},INVALID_PHONE_NUMBER_FORMAT:{title:"Incorrect phone number format",desc:"The phone number format for your events doesn't follow the E.164 format. This can affect Advanced Matching and your ad performance.",suggestion:"Go to your source code and update your shared phone numbers. It should follow the E.164 format.",link:"https://ads.tiktok.com/marketing_api/docs?id=1739585700402178"},INVALID_PHONE_NUMBER_INFORMATION:{title:"Invalid phone number information",desc:"The phone numbers shared with your events were invalid.",suggestion:'Go to your source code to double check shared phone numbers. Leave your string empty when customer information isn\'t available. Avoid spaces, "undefined", or other hardcoded values.',link:"https://ads.tiktok.com/marketing_api/docs?id=1739585700402178"},LONG_EVENT_TYPE_NAME:{title:"Event Name Too Long",desc:"1 event type exceeds the 50 character limit.",suggestion:"Go to your source code and make these event names 50 characters or less.",link:"https://ads.tiktok.com/help/article/custom-events?lang=en"},MISMATCHED_EVENT_TYPE_NAME_FOR_CUSTOM_EVENT:{title:"Invalid Event Name Format",desc:"1 event name was rejected for not following TikTok format requirements.",suggestion:"Go to your source code and update these event types according to TikTok format requirements.",link:"https://ads.tiktok.com/help/article/custom-events?lang=en"},MISSING_CONTENT_ID:{title:"Missing 'content_id' paramter",desc:"The 'content_id' parameter isn't being received. This is required for Video Shopping Ads (VSA).",suggestion:"Include the 'content_id' parameter in your source code. This is required for Video Shopping Ads (VSA).",link:"https://ads.tiktok.com/help/article/standard-events-parameters?redirected=2"},MISSING_CURRENCY_PARAMETER:{title:'Missing "currency" parameter',desc:"Events shared are missing a 'currency' parameter. This impacts our ability to receive the value amount correctly, which can affect the accuracy of reporting for your return on ad spend.",suggestion:'Go to your source code and include the "currency" parameter. You can check supported currency codes. {{learn_more}}',link:"https://ads.tiktok.com/help/article/standard-events-parameters?redirected=2"},MISSING_EMAIL_AND_PHONE:{title:"Missing email and phone number",desc:"Email and phone number info isn't being received. This information is required for Complete Payment events.",suggestion:"Improve your email and phone coverage. This allows you to attribute more conversions and reach more people with your ads.",link:"https://ads.tiktok.com/marketing_api/docs?rid=5ipocbxyw8v&id=1701890972946433"},MISSING_VALUE_PARAMETER:{title:'Missing "value" parameter',desc:"Events shared are missing a 'value' parameter'. This is used calculate ROAS for people and the bid for your highest value customers. ",suggestion:'Go to your source code and include the "value" parameter.',link:"https://ads.tiktok.com/help/article/standard-events-parameters?redirected=2"},DUPLICATE_PIXEL_CODE:{title:"Duplicate Pixel ID",desc:"The pixel ID is duplicate. This could impact the pixel data accuracy.",suggestion:"Please double check and delete any unnecessary pixel code.",link:""},MISSING_PIXEL_CODE:{title:"Missing pixel ID",desc:"Some of the events sent to your TikTok account are missing a pixel ID.",suggestion:"Go to your source code and double check that the 20-character pixel ID has been added to the ttq.load() function. Don't send null values or spaces. If you edited the base code, ensure the event.js has the 'sdkid' in the Chrome network panel.",link:""},INVALID_PIXEL_CODE:{title:"Invalid pixel ID",desc:"The pixel ID is invalid. This could prevent your pixel from receiving data.",suggestion:"Please go to Events Manager and find the correct pixel ID.",link:""}},oz=/^[a-zA-Z\d]([a-zA-Z_\-\d ]{0,}[a-zA-Z_\-\d])?$/,o$=["product","product_group","destination","hotel","flight","vehicle"],o0=["email_is_hashed","phone_is_hashed","sha256_email","sha256_phone"],o1=["Purchase","CompletePayment","InitiateCheckout","AddToCart","PlaceAnOrder","ViewContent","AddToWishlist"],o2=function(t){return void 0===t},o3=function(t,e){if(aU[t]){var r=_(),n=oZ[t],o="[TikTok Pixel] - "+n.title;n.desc&&(o+="\nIssue: "+n.desc),n.suggestion&&(o+="\nSuggestion: "+n.suggestion),e&&Object.keys(e).forEach(function(t){o=o.split("{{"+t+"}}").join(e[t])}),o=o.trim(),n.link&&(o+=" See "+n.link+" for more information."),r&&r.console&&r.console.warn&&r.console.warn(o)}};(av=a5||(a5={}))[av.Live=0]="Live",av[av.NoRecord=1]="NoRecord";var o6=function(t){var e=t.event,r=void 0===e?"":e;return!!(["null","undefined"].includes(r)||/^\s*$/.test(r))||!r},o5=function(t){var e=t.event;return!!o6(t)||oz.test(void 0===e?"":e)},o8=function(t){var e=t.event;return(void 0===e?"":e).length<=50},o4=function(t){var e=t.event,r=t._inspection;if(z(e)){var n=(void 0===r?{}:r).identity_params,o=void 0===n?{}:n;return 0===Object.keys(o).length||o0.every(function(t){return(o[t]||[]).includes(aR.EMPTY_VALUE)})}return!1},o9=function(t){var e=t._inspection,r=void 0===e?{}:e;return!!r&&!!r.identity_params&&((r.identity_params.email_is_hashed||[]).includes(aR.FILTER_EVENTS)||(r.identity_params.sha256_email||[]).includes(aR.FILTER_EVENTS))},o7=function(t){var e=t._inspection,r=void 0===e?{}:e;return!!r&&!!r.identity_params&&((r.identity_params.email_is_hashed||[]).includes(aR.UNKNOWN_INVALID)||(r.identity_params.sha256_email||[]).includes(aR.UNKNOWN_INVALID))},it=function(t){var e=t._inspection,r=void 0===e?{}:e;return!!r&&!!r.identity_params&&((r.identity_params.phone_is_hashed||[]).includes(aR.FILTER_EVENTS)||(r.identity_params.sha256_phone||[]).includes(aR.FILTER_EVENTS))},ie=function(t){var e=t._inspection,r=void 0===e?{}:e;return!!r&&!!r.identity_params&&((r.identity_params.phone_is_hashed||[]).includes(aR.UNKNOWN_INVALID)||(r.identity_params.sha256_phone||[]).includes(aR.UNKNOWN_INVALID))},ir=function(t){var e=t.event,r=t.properties,n=void 0===r?{}:r;if(o1.includes(void 0===e?"":e)){if(o2(n.contents)&&o2(n.content_id))return!0;if(!o2(n.contents))return!Array.isArray(n.contents)||n.contents.length<1||!n.contents.every(function(t){return t&&!o2(t.content_id)})}return!1},io=function(t){var e=t.properties,r=void 0===e?{}:e,n=r.content_id,o=r.contents;return!(!o2(n)&&/^\s*$/.test(n))&&(!(!o2(o)&&Array.isArray(o))||o.every(function(t){return t&&!o2(t.content_id)&&!/^\s*$/.test(t.content_id)}))},ii=function(t){var e=t.properties.content_type;return!e||o$.includes(e)},ia=function(t){var e=t.properties.value;return!e||!!("number"==typeof e||"string"==typeof e&&(/^\d+(\.\d+)?$/.test(e)||/^\d+$/.test(e)))},ic=function(t){var e=t.event,r=t.properties,n=void 0===r?{}:r;return!!(z(void 0===e?"":e)&&o2(n.value)||!o2(n.currency)&&o2(n.value))},is=function(t){var e=t.properties.currency;return!e||e1.includes(e)},iu=function(t){var e=t.event,r=t.properties,n=void 0===r?{}:r;return!!(z(void 0===e?"":e)&&o2(n.currency)||!o2(n.value)&&o2(n.currency))};function il(t,e){return(il=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var ip=function(t,e){return function(r,n){e(r,n,t)}},ih=function(t){function e(e,r,n){var o,i;return(o=t.call(this,{name:"DiagnosticsConsole",reporters:r,context:e})||this).isEnableDiagnosticsConsole=!1,o.isEnableDiagnosticsConsole=!!(null==(i=null==n?void 0:n.plugins)?void 0:i.DiagnosticsConsole),o}e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,il(e,t);var r=e.prototype;return r.isDisableDiagnosticsConsole=function(){try{if(this.isEnableDiagnosticsConsole)return!!Object.values(this.reporters).some(function(t){var e,r;return void 0!==(null==(e=null==t?void 0:t.options)?void 0:e.diagnostics)&&!(null==(r=null==t?void 0:t.options)?void 0:r.diagnostics)});return!0}catch(t){return!1}},r.warn=function(t,e){try{if(this.isDisableDiagnosticsConsole())return;o3(t,e)}catch(e){A(aI.CUSTOM_ERROR,e,{custom_name:"diagnostics_console",custom_enum:t})}},r.pixelDidMount=function(t){var e=this;try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var r={taskName:window.ttq._pf_tn,functionName:"console_plugin_pixelDidMount",start:performance.now()}}catch(t){}t.getParameterInfo().then(function(t){e.handlePixelInfoValidate(t)}).catch(function(t){A(aI.CUSTOM_ERROR,t,{custom_name:"diagnostics_console",custom_enum:"pixel"})});try{window.ttq&&window.ttq._ppf&&(r.end=performance.now(),window.ttq._ppf.push(r))}catch(t){}},r.pixelSend=function(t,e,r,n,o){void 0===n&&(n={});try{n&&n._i||o!==aH.TRACK||e===e$||this.handleEventPayloadValidate(oQ()(r||{}))}catch(t){A(aI.CUSTOM_ERROR,t,{custom_name:"diagnostics_console",custom_enum:"track"})}},r.handlePixelInfoValidate=function(t){if(t.status!==a5.Live)return void this.warn(aU.INVALID_PIXEL_CODE)},r.handleEventPayloadValidate=function(t){t.properties||(t.properties={}),o6(t)&&this.warn(aU.EMPTY_EVENT_TYPE_NAME),o5(t)||this.warn(aU.MISMATCHED_EVENT_TYPE_NAME_FOR_CUSTOM_EVENT),o8(t)||this.warn(aU.LONG_EVENT_TYPE_NAME),o4(t)&&this.warn(aU.MISSING_EMAIL_AND_PHONE),o9(t)&&this.warn(aU.INVALID_EMAIL_INFORMATION),o7(t)&&this.warn(aU.INVALID_EMAIL_FORMAT),it(t)&&this.warn(aU.INVALID_PHONE_NUMBER_INFORMATION),ie(t)&&this.warn(aU.INVALID_PHONE_NUMBER_FORMAT),ir(t)&&this.warn(aU.MISSING_CONTENT_ID),io(t)||this.warn(aU.INVALID_CONTENT_ID),ii(t)||this.warn(aU.INVALID_CONTENT_TYPE),ia(t)||this.warn(aU.INVALID_EVENT_PARAMETER_VALUE),ic(t)&&this.warn(aU.MISSING_VALUE_PARAMETER),is(t)||this.warn(aU.INVALID_CURRENCY_CODE),iu(t)&&this.warn(aU.MISSING_CURRENCY_PARAMETER,{learn_more:""})},e}(r9);function id(t,e){return(id=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}ih=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er(),ip(0,en(tK.CONTEXT)),ip(1,en(tK.TTQ_REPORTERS)),ip(2,en(tK.TTQ_GLOBAL_OPTIONS))],ih);var iv=function(t,e){return function(r,n){e(r,n,t)}},i_=function(t){function e(e,r,n,o){var i;return(i=t.call(this,{name:"PangleCookieMatching",reporters:r,context:e})||this).hasReport=!1,i.reportService=n,i.env=o,i}e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,id(e,t);var r=e.prototype;return r.isPixelPangleCookieMatching=function(t){void 0===t&&(t="");var e=this.reporters;if(t){var r=e.find(function(e){return e.getReporterId()===t});if(r&&r.plugins.PangleCookieMatching)return!0}else if(e.some(function(t){return!!t.plugins.PangleCookieMatching}))return!0;return!1},r.disablePangleCookie=function(){this.isPixelPangleCookieMatching()&&eQ("https://analytics.pangle-ads.com/api/v2/pangle_disable_cookie")},r.pixelSend=function(t,e,r,n,o){void 0===n&&(n={});try{var i;if((null==(i=this.context.getPageSign().pageIndex)?void 0:i.index)===0&&!tb(this.env)&&r&&r.message_id&&this.isPixelPangleCookieMatching(t)&&!this.hasReport){var a=r.event,c=r.message_id,s=r.context.library,u={event:a,message_id:c,context:{library:s},timestamp:new Date().toJSON()};this.hasReport=!0,this.reportService.report("https://analytics.pangle-ads.com/api/v2/pangle_pixel",u,aB.httpReport)}}catch(t){A(aI.CUSTOM_ERROR,t,{custom_name:"pangle_report"})}},e}(r9);i_=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er(),iv(0,en(tK.CONTEXT)),iv(1,en(tK.TTQ_REPORTERS)),iv(2,en(tK.REPORT_SERVICE)),iv(3,en(tK.ENV))],i_);var ig="https://analytics.tiktok.com/i18n/pixel/eb.js",iy="_tt_event_builder";function im(t,e){return(im=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}(a_=a8||(a8={})).EVENT_BUILD_BOOTSTRAP_ACK="event_builder_bootstrap_ack",a_.EVENT_BUILD_WRONG_CODE="event_builder_wrong_code",a_.EVENT_BUILD_BOOTSTRAP="event_builder_bootstrap";var iw=function(t,e){return function(r,n){e(r,n,t)}},iE=function(t){function e(e,r){var n;return(n=t.call(this,{name:"EventBuilder",reporters:r,context:e})||this).pluginMounted=!1,n}return e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,im(e,t),e.prototype.pixelDidMount=function(t){var e=this;try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var r={taskName:window.ttq._pf_tn,functionName:"event_builder_plugin_pixelDidMount",start:performance.now()}}catch(t){}if(!this.pluginMounted){this.pluginMounted=!0;var n=y(),o=function(t){t.data.type===a8.EVENT_BUILD_BOOTSTRAP&&!n._event_builder_pickup_sdk_loaded&&(e.reporters.find(function(e){return e.getReporterId()===t.data.pixelCode})?(n._event_builder_pickup_sdk_loaded=!0,el(iy,t.data),eQ(ig).then(function(){window.opener.window.postMessage({type:a8.EVENT_BUILD_BOOTSTRAP_ACK},"*")}).catch(function(t){A(aI.CUSTOM_ERROR,t,{custom_name:"event_builder_load_error",custom_enum:"load_ebjs"})})):n._event_builder_pickup_sdk_verify_flag||(setTimeout(function(){e.reporters.find(function(e){return e.getReporterId()===t.data.pixelCode})||window.opener.window.postMessage({type:a8.EVENT_BUILD_WRONG_CODE},"*")},5e3),n._event_builder_pickup_sdk_verify_flag=!0))};!n._event_builder_pickup_sdk_loaded&&(eu(iy)?eQ(ig).then(function(){n._event_builder_pickup_sdk_loaded=!0}):window.opener&&(window.addEventListener("message",o),setTimeout(function(){window.removeEventListener("message",o)},8e3)));try{window.ttq&&window.ttq._ppf&&(r.end=performance.now(),window.ttq._ppf.push(r))}catch(t){}}},e}(r9);function ib(t,e){return(ib=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}iE=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er(),iw(0,en(tK.CONTEXT)),iw(1,en(tK.TTQ_REPORTERS))],iE);var iI=function(t,e){return function(r,n){e(r,n,t)}},iO="tt_pixel_is_enrich_ipv6_triggered_by_enrich_am",iT=function(t){function e(e,r,n,o){var i;return(i=t.call(this,{name:"EnrichIpv6",reporters:r,context:e})||this).hasReported=!1,i.shouldReportAfterEnrichAM=!1,i.reportService=n,i.env=o,i}e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,ib(e,t);var r=e.prototype;return r.isPixelEnrichIpv6=function(){var t=this.reporters;return!!t&&0!==t.length&&t.every(function(t){return t&&t.plugins&&!0===t.plugins.EnrichIpv6})},r.isEnrichIpv6V2SwitchOn=function(){var t="EnrichIpv6V2";try{var e=y()._plugins||{};if(null!=e[t])return!!e[t];return!1}catch(t){return!1}},r.buildEnrichIpv6Data=function(t){var e=this.isEnrichIpv6V2SwitchOn()?"#source=2":"#source=1";return Object.assign(Object.assign({},t),{event:"EnrichIpv6",trigger_event:t.event,message_id:t.message_id+e})},r.pixelSend=function(t,e,r,n,o){void 0===n&&(n={});try{var i;if(o!==aH.TRACK||!("Shopify"===O(t)||this.isEnrichIpv6V2SwitchOn())||tb(this.env)||!(r&&r.message_id)||((null==(i=this.context.getPageSign().pageIndex)?void 0:i.index)===0&&!this.hasReported&&this.isPixelEnrichIpv6()&&(this.hasReported=!0,this.reportService.report(eE,this.buildEnrichIpv6Data(r),aB.htmlHttpReport)),"true"===sessionStorage.getItem(iO)))return;"EnrichAM"===e&&(this.shouldReportAfterEnrichAM=!0),this.shouldReportAfterEnrichAM&&this.isPixelEnrichIpv6()&&(this.shouldReportAfterEnrichAM=!1,sessionStorage.setItem(iO,"true"),this.reportService.report(eE,this.buildEnrichIpv6Data(r),aB.htmlHttpReport))}catch(t){A(aI.CUSTOM_ERROR,t,{custom_name:"enrich_ipv6_report"})}},e}(r9);function iN(t){return performance&&performance.timing?t?Date.now()-t:Date.now()-performance.timing.navigationStart:-1}function iR(t,e){return function(){for(var r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];window.requestIdleCallback?window.requestIdleCallback(t.bind.apply(t,[e].concat(n))):t.apply(e,n)}}function iS(t,e){void 0===e&&(e=4);try{if(Number.isInteger(t))return t;return parseFloat(t.toFixed(e))}catch(t){return -1}}iT=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er(),iI(0,en(tK.CONTEXT)),iI(1,en(tK.TTQ_REPORTERS)),iI(2,en(tK.REPORT_SERVICE)),iI(3,en(tK.ENV))],iT),(ag=a4||(a4={})).FIRST_CONTENTFUL_PAINT="fcp",ag.LARGEST_CONTENTFUL_PAINT="lcp",ag.FIRST_INPUT_DELAY="fid",ag.TIME_TO_FIRST_BYTE="ttfb",ag.PAGE_LEAVE="pl",ag.LOAD_FINISH="lf",ag.TIME_WINDOW_TRACKER="twt",ag.TIME_TO_INTERACTIVE="tti";var iP=function(){function t(t,e,r,n){n?(this.reportService=r,this.context=t,this.reporters=e,this.reportUrl=n):A(aI.CUSTOM_ERROR,Error("empty reportUrl"),{custom_name:"empty_reportUrl",custom_enum:"page_perf_monitor"})}var e=t.prototype;return e.getResult=function(t){return{action_event:t}},e.report=function(t){var e=this;if(void 0!==t){var r=this.getReportPixelList(),n=this.assemblyReportData(aH.PAGE,t,r);n&&this.reportService.reportPreTasks.then(function(){e.reportService.report(e.reportUrl,n,aB.defaultReport,aY.P0)}),this.resetAfterReport()}},e.getReportPixelList=function(){return this.reporters},e.assemblyReportData=function(t,e,r){if(0!==r.length){var n,o=r.map(function(t){return t.getReporterId()}),i=this.context.getPageSign(),a=r[0],c=a.assemblyData(a.getReporterId(),"",{},{},aH.PAGE);return delete c.event,c.action=t,c.auto_collected_properties=e,c.context.pixel||(c.context.pixel={}),c.context.pixel.code=o[0],c.context.pixel.codes=o.join("|"),c.context.index=null==(n=i.pageIndex)?void 0:n.index,c.context.session_id=i.sessionId,c.context.pageview_id=B(this.context.getPageViewId(),a.reporterInfo.loadId,"::"),c.message_id=c.message_id.replace(/-[^-]*$/,""),c}},t}();function iA(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function ix(t,e){return(ix=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var iL,iC,ik,iD,ij,iM,iF,iq,iU,iG,iH,iV,iB,iY,iK,iW,iJ,iX,iQ,iZ,iz,i$,i0,i1,i2,i3,i6,i5,i8,i4,i9,i7,at,ae,ar,an,ao,ai,aa,ac,as,au,af,al,ap,ah,ad,av,a_,ag,ay,am,aw,aE,ab,aI,aO,aT,aN,aR,aS,aP,aA,ax,aL,aC,ak,aD,aj,aM,aF,aq,aU,aG,aH,aV,aB,aY,aK,aW,aJ,aX,aQ,aZ,az,a$,a0,a1,a2,a3,a6,a5,a8,a4,a9,a7,ct,ce,cr=function(t){function e(e,r,n,o,i){var a;return void 0===i&&(i=[]),(a=t.call(this,e,r,n,o)||this).clickTimes=0,a.clickTimesTotal=0,a.scrollTimes=0,a.scrollTimesTotal=0,a.stayTime=0,a.stayTimeReported=!1,a.rules=[],a.rules=i,a.init(),a}e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,ix(e,t);var r=e.prototype;return r.init=function(){var t,e,r,n,o=this;t=iR(this.updateClickTimes,this),e=Y(function(e){t()},100),window.addEventListener("click",e,{capture:!0}),r=iR(this.updateScrollTimes,this),n=Y(function(){r()},500),window.addEventListener("scroll",n,{passive:!0}),setInterval(function(){o.reportInteraction()},1e4)},r.reportInteraction=function(){this.isUpdated()&&(this.report(this.getResult(a4.TIME_WINDOW_TRACKER)),this.resetAfterReport())},r.getResult=function(t){return this.getResultOnWindowTracker(t)},r.getResultOnPageLeave=function(t){var e;if(this.updateStayTime(),this.shouldReport()&&!this.stayTimeReported)return this.stayTimeReported=!0,e={metrics_type:"ct|st|pl",ct:this.clickTimes,st:this.scrollTimes,pl:this.stayTime},{action_event:t,inter:{ct:this.clickTimes,st:this.scrollTimes,pl:this.stayTime},page_inter:e}},r.getResultOnWindowTracker=function(t){var e;return e={metrics_type:"ct|st",ct:this.clickTimes,st:this.scrollTimes},{action_event:t,inter:{ct:this.clickTimes,st:this.scrollTimes},page_inter:e}},r.updateClickTimes=function(){this.clickTimes+=1,this.clickTimesTotal+=1},r.updateScrollTimes=function(){this.scrollTimes+=1,this.scrollTimesTotal+=1},r.updateStayTime=function(){this.stayTime=Math.floor(iN())},r.shouldReport=function(){for(var t,e=function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(r)return(r=r.call(t)).next.bind(r);if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return iA(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return iA(t,e)}}(t))){r&&(t=r);var n=0;return function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(this.rules);!(t=e()).done;){var r=t.value;try{if(!r({clickTimes:this.clickTimesTotal,scrollTimes:this.scrollTimesTotal,stayTime:this.stayTime}))return!1}catch(t){return!1}}return!0},r.isUpdated=function(){return 0!=this.clickTimes||0!=this.scrollTimes},r.resetAfterReport=function(){this.clickTimes=0,this.scrollTimes=0},e}(iP),cn=function(t){setTimeout(function(){t(a4.TIME_TO_INTERACTIVE,-1)},0)},co=function(t){var e=function(){new Promise(function(t,e){setTimeout(function(){var r=performance.getEntriesByType("navigation");if(r.length>0){var n=r[0];t(n.loadEventEnd-n.startTime)}else window.performance.timing?t(window.performance.timing.loadEventEnd-window.performance.timing.navigationStart||-1):e("Performance timing not supported")},0)}).then(function(e){t(a4.LOAD_FINISH,e)}).catch(function(e){t(a4.LOAD_FINISH,-1)})};"complete"===window.document.readyState?e():window.addEventListener("load",e)},ci=-1,ca=function(t){addEventListener("pageshow",function(e){e.persisted&&(ci=e.timeStamp,t(e))},!0)},cc=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},cs=function(){var t=cc();return t&&t.activationStart||0},cu=function(t,e){var r=cc(),n="navigate";return ci>=0?n="back-forward-cache":r&&(document.prerendering||cs()>0?n="prerender":document.wasDiscarded?n="restore":r.type&&(n=r.type.replace(/_/g,"-"))),{name:t,value:void 0===e?-1:e,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(0x82f79cd8fff*Math.random())+1e12),navigationType:n}},cf=function(t,e,r){try{if(PerformanceObserver.supportedEntryTypes.includes(t)){var n=new PerformanceObserver(function(t){Promise.resolve().then(function(){e(t.getEntries())})});return n.observe(Object.assign({type:t,buffered:!0},r||{})),n}}catch(t){}},cl=function(t,e,r,n){var o,i;return function(a){var c;e.value>=0&&(a||n)&&((i=e.value-(o||0))||void 0===o)&&(o=e.value,e.delta=i,c=e.value,e.rating=c>r[1]?"poor":c>r[0]?"needs-improvement":"good",t(e))}},cp=function(t){requestAnimationFrame(function(){return requestAnimationFrame(function(){return t()})})},ch=function(t){var e=function(e){"pagehide"!==e.type&&"hidden"!==document.visibilityState||t(e)};addEventListener("visibilitychange",e,!0),addEventListener("pagehide",e,!0)},cd=function(t){var e=!1;return function(r){e||(t(r),e=!0)}},cv=-1,c_=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},cg=function(t){"hidden"===document.visibilityState&&cv>-1&&(cv="visibilitychange"===t.type?t.timeStamp:0,cm())},cy=function(){addEventListener("visibilitychange",cg,!0),addEventListener("prerenderingchange",cg,!0)},cm=function(){removeEventListener("visibilitychange",cg,!0),removeEventListener("prerenderingchange",cg,!0)},cw=function(){return cv<0&&(cv=c_(),cy(),ca(function(){setTimeout(function(){cv=c_(),cy()},0)})),{get firstHiddenTime(){return cv}}},cE=function(t){document.prerendering?addEventListener("prerenderingchange",function(){return t()},!0):t()},cb=[1800,3e3],cI=function(t,e){e=e||{},cE(function(){var r,n=cw(),o=cu("FCP"),i=cf("paint",function(t){t.forEach(function(t){"first-contentful-paint"===t.name&&(i.disconnect(),t.startTime<n.firstHiddenTime&&(o.value=Math.max(t.startTime-cs(),0),o.entries.push(t),r(!0)))})});i&&(r=cl(t,o,cb,e.reportAllChanges),ca(function(n){r=cl(t,o=cu("FCP"),cb,e.reportAllChanges),cp(function(){o.value=performance.now()-n.timeStamp,r(!0)})}))})},cO=[.1,.25],cT=function(t,e){e=e||{},cI(cd(function(){var r,n=cu("CLS",0),o=0,i=[],a=function(t){t.forEach(function(t){if(!t.hadRecentInput){var e=i[0],r=i[i.length-1];o&&t.startTime-r.startTime<1e3&&t.startTime-e.startTime<5e3?(o+=t.value,i.push(t)):(o=t.value,i=[t])}}),o>n.value&&(n.value=o,n.entries=i,r())},c=cf("layout-shift",a);c&&(r=cl(t,n,cO,e.reportAllChanges),ch(function(){a(c.takeRecords()),r(!0)}),ca(function(){o=0,r=cl(t,n=cu("CLS",0),cO,e.reportAllChanges),cp(function(){return r()})}),setTimeout(r,0))}))},cN={passive:!0,capture:!0},cR=new Date,cS=function(t,e){a9||(a9=e,a7=t,ct=new Date,cx(removeEventListener),cP())},cP=function(){if(a7>=0&&a7<ct-cR){var t={entryType:"first-input",name:a9.type,target:a9.target,cancelable:a9.cancelable,startTime:a9.timeStamp,processingStart:a9.timeStamp+a7};ce.forEach(function(e){e(t)}),ce=[]}},cA=function(t){if(t.cancelable){var e,r,n,o=(t.timeStamp>1e12?new Date:performance.now())-t.timeStamp;"pointerdown"==t.type?(e=function(){cS(o,t),n()},r=function(){n()},n=function(){removeEventListener("pointerup",e,cN),removeEventListener("pointercancel",r,cN)},addEventListener("pointerup",e,cN),addEventListener("pointercancel",r,cN)):cS(o,t)}},cx=function(t){["mousedown","keydown","touchstart","pointerdown"].forEach(function(e){return t(e,cA,cN)})},cL=[100,300],cC=function(t,e){e=e||{},cE(function(){var r,n=cw(),o=cu("FID"),i=function(t){t.startTime<n.firstHiddenTime&&(o.value=t.processingStart-t.startTime,o.entries.push(t),r(!0))},a=function(t){t.forEach(i)},c=cf("first-input",a);r=cl(t,o,cL,e.reportAllChanges),c&&ch(cd(function(){a(c.takeRecords()),c.disconnect()})),c&&ca(function(){r=cl(t,o=cu("FID"),cL,e.reportAllChanges),ce=[],a7=-1,a9=null,cx(addEventListener),ce.push(i),cP()})})},ck=[2500,4e3],cD={},cj=function(t,e){e=e||{},cE(function(){var r,n=cw(),o=cu("LCP"),i=function(t){var e=t[t.length-1];e&&e.startTime<n.firstHiddenTime&&(o.value=Math.max(e.startTime-cs(),0),o.entries=[e],r())},a=cf("largest-contentful-paint",i);if(a){r=cl(t,o,ck,e.reportAllChanges);var c=cd(function(){cD[o.id]||(i(a.takeRecords()),a.disconnect(),cD[o.id]=!0,r(!0))});["keydown","click"].forEach(function(t){addEventListener(t,function(){return setTimeout(c,0)},!0)}),ch(c),ca(function(n){r=cl(t,o=cu("LCP"),ck,e.reportAllChanges),cp(function(){o.value=performance.now()-n.timeStamp,cD[o.id]=!0,r(!0)})})}})},cM=[800,1800],cF=function t(e){document.prerendering?cE(function(){return t(e)}):"complete"!==document.readyState?addEventListener("load",function(){return t(e)},!0):setTimeout(e,0)},cq=function(t,e){e=e||{};var r=cu("TTFB"),n=cl(t,r,cM,e.reportAllChanges);cF(function(){var o=cc();if(o){var i=o.responseStart;if(i<=0||i>performance.now())return;r.value=Math.max(i-cs(),0),r.entries=[o],n(!0),ca(function(){(n=cl(t,r=cu("TTFB",0),cM,e.reportAllChanges))(!0)})}})};function cU(t,e){return(cU=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var cG=function(t,e){return function(r,n){e(r,n,t)}},cH=function(t){function e(e,r,n,o){var i;return(i=t.call(this,e,r,n,o)||this).cls=-1,i.init(),i}e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,cU(e,t);var r=e.prototype;return r.init=function(){cT(this.clsHandler.bind(this),{reportAllChanges:!0}),cq(this.webVitalHandler.bind(this)),cI(this.webVitalHandler.bind(this)),cj(this.webVitalHandler.bind(this),{reportAllChanges:!0}),cC(this.webVitalHandler.bind(this)),cn(this.baseHandler.bind(this)),co(this.baseHandler.bind(this))},r.getResult=function(t){var e=Math.floor(iN()),r={ttns:e,cls:iS(this.cls),idx:this.getSessionIndex(),pep:iS(this.getPep())},n=["cls","idx","pep"];n.unshift(t);var o={metrics_type:n.join("|"),cls:r.cls,idx:r.idx,pep:r.pep};return o[t]=e,{action_event:t,perf:r,page_perf:o}},r.resetAfterReport=function(){},r.clsHandler=function(t){this.cls=t.value||-1},r.webVitalHandler=function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var e,r={taskName:window.ttq._pf_tn||"page_data_web_vital_handler",functionName:window.ttq._pf_tn&&"page_data_web_vital_handler",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="page_data_web_vital_handler")}}catch(t){}var n=this.getResult(t.name.toLocaleLowerCase());n.perf&&(n.perf.ttns=(null==t?void 0:t.value)?Math.floor(t.value):-1),n.page_perf&&(n.page_perf[t.name.toLocaleLowerCase()]=null==(e=null==n?void 0:n.perf)?void 0:e.ttns),this.report(n),this.resetAfterReport();try{window.ttq&&window.ttq._ppf&&(r.end=performance.now(),window.ttq._ppf.push(r),"page_data_web_vital_handler"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}},r.baseHandler=function(t,e){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var r,n={taskName:window.ttq._pf_tn||"page_data_base_handler",functionName:window.ttq._pf_tn&&"page_data_base_handler",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="page_data_base_handler")}}catch(t){}var o=this.getResult(t);o.perf&&(o.perf.ttns=e?Math.floor(e):-1),o.page_perf&&(o.page_perf[t]=null==(r=o.perf)?void 0:r.ttns),this.report(o),this.resetAfterReport();try{window.ttq&&window.ttq._ppf&&(n.end=performance.now(),window.ttq._ppf.push(n),"page_data_base_handler"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}},r.getSessionIndex=function(){var t,e=null==(t=this.context.getPageSign().pageIndex)?void 0:t.main;return null==e?-1:e},r.getCurrScrollPosition=function(){return document.documentElement.scrollTop||document.body.scrollTop},r.getViewportHeight=function(){return window.innerHeight||document.documentElement.clientHeight},r.getMaxHeight=function(){var t=document.body,e=document.documentElement;return Math.max(t.scrollHeight,t.offsetHeight,e.clientHeight,e.scrollHeight,e.offsetHeight)},r.getPep=function(){return(this.getCurrScrollPosition()+this.getViewportHeight())/this.getMaxHeight()},e}(iP);cH=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([cG(0,en(tK.CONTEXT)),cG(1,en(tK.TTQ_REPORTERS)),cG(2,en(tK.REPORT_SERVICE))],cH);var cV=[function(t){var e=t.clickTimes,r=t.scrollTimes;return e>=1||r>=1},function(t){return t.stayTime>1e3}];function cB(t,e){return(cB=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var cY=function(t,e){return function(r,n){e(r,n,t)}},cK=function(t){function e(e,r,n,o){var i;return(i=t.call(this,{name:"PageData",reporters:r,context:e})||this).monitors=[],i.ttqOptions={},i.reportService=n,i.context=e,i.reporters=r,i.ttqOptions=o,i.isPluginInit=!1,i}e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,cB(e,t);var r=e.prototype;return r.init=function(){var t=this.isPageInterEnabled(),e=this.isPagePerfEnabled();(t||e)&&(t&&this.initInteraction(),e&&this.initPerformance())},r.initInteraction=function(){this.interactionMonitor=new cr(this.context,this.reporters,this.reportService,e3(aH.PAGE_INTERACTION),cV),this.monitors.push(this.interactionMonitor)},r.initPerformance=function(){this.performanceMonitor=new cH(this.context,this.reporters,this.reportService,e3(aH.PAGE_PERFORMANCE)),this.monitors.push(this.performanceMonitor)},r.isPagePerfEnabled=function(){var t,e;return null==(e=null==(t=this.ttqOptions)?void 0:t.plugins)?void 0:e.PageData},r.isPageInterEnabled=function(){var t,e;return null==(e=null==(t=this.ttqOptions)?void 0:t.plugins)?void 0:e.PageData},r.report=function(t){var e=this.interactionMonitor.getResultOnPageLeave(t);void 0!==e&&(this.interactionMonitor.report(e),this.interactionMonitor.resetAfterReport())},r.pageWillLeave=function(t){this.report(a4.PAGE_LEAVE)},r.pixelDidMount=function(t){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var e={taskName:window.ttq._pf_tn,functionName:"page_plugin_pixelDidMount",start:performance.now()}}catch(t){}this.isPluginInit||(this.init(),this.isPluginInit=!0);try{window.ttq&&window.ttq._ppf&&(e.end=performance.now(),window.ttq._ppf.push(e))}catch(t){}},e}(r9);cK=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er(),cY(0,en(tK.CONTEXT)),cY(1,en(tK.TTQ_REPORTERS)),cY(2,en(tK.REPORT_SERVICE)),cY(3,en(tK.TTQ_GLOBAL_OPTIONS))],cK);var cW={initAllModule:1,webTtqFactory:2,handleCache:3,webReporterFactory:4,initAutoForm:5,auto_config_plugin_pixelDidMount:6,callback_plugin_pixelDidMount:7,console_plugin_pixelDidMount:8,event_builder_plugin_pixelDidMount:9,shopify_plugin_pixelDidMount:10,page_plugin_pixelDidMount:11,competitor_insight_plugin_init:12,getPixelInstalledPosition:13,getPixelScriptByPixelCode:14,resetExpires:15,freezeAPI:16,handlePixelRules:17,mergeWebGlobalTtq:18,handleGlobalCache:22,getPixelDetail:19,basettq_init_context_info:20,initAutoForm_getOverallFormDetail:21,web_track_handler:23,identify_api_handler:24,updateParameterInferenceData:25},cJ={identify_api_handler:{id:1,fn:[]},identify_encryption:{id:2,fn:[]},identify_after_encryption:{id:3,fn:[]},track_api_handler:{id:4,fn:[]},track_after_reporter_init:{id:5,fn:[]},track_after_report_preposition:{id:6,fn:[]},init:{id:7,fn:[cW.initAllModule,cW.webTtqFactory,cW.handleCache,cW.webReporterFactory,cW.initAutoForm,cW.freezeAPI,cW.handlePixelRules,cW.resetExpires,cW.mergeWebGlobalTtq,cW.auto_config_plugin_pixelDidMount,cW.callback_plugin_pixelDidMount,cW.console_plugin_pixelDidMount,cW.event_builder_plugin_pixelDidMount,cW.shopify_plugin_pixelDidMount,cW.page_plugin_pixelDidMount,cW.competitor_insight_plugin_init,cW.getPixelInstalledPosition,cW.getPixelScriptByPixelCode,cW.handleGlobalCache,cW.basettq_init_context_info,cW.initAutoForm_getOverallFormDetail,cW.web_track_handler,cW.identify_api_handler,cW.updateParameterInferenceData]},page_api_handler:{id:8,fn:[]},auto_advanced_matching_handler:{id:9,fn:[]},auto_config_metadata_handler:{id:10,fn:[]},auto_config_click_handler:{id:11,fn:[]},auto_config_form_handler:{id:12,fn:[]},event_builder_dispatcher:{id:13,fn:[]},page_data_web_vital_handler:{id:14,fn:[]},page_data_base_handler:{id:15,fn:[]}},cX=function(){function t(){this.queue=[],this.currentTaskMap={}}var e=t.prototype;return e.handleCache=function(t){var e=this;t.forEach(function(t){e.push(t)})},e.push=function(t){var e=t.taskName,r=t.functionName,n=t.start,o=Math.round((t.end-n)*1e3),i=e&&this.getTaskIdFromName(e);if(i){var a=this.currentTaskMap[i];if(a||(this.currentTaskMap[i]=a={id:i,d:-1}),r){var c=this.getFunctionIdFromName(r);c&&(a.f=[].concat(a.f||[],[{id:c,d:o}]))}else a.d=o,this.queue.push(a),delete this.currentTaskMap[i]}},e.print=function(){return this.queue},e.printAndClear=function(){var t=this.print();return this.clear(),t},e.clear=function(){this.queue=[]},e.getTaskIdFromName=function(t){var e;return null==(e=cJ[t])?void 0:e.id},e.getFunctionIdFromName=function(t){return cW[t]},t}();function cQ(t,e){return(cQ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var cZ=function(t,e){return function(r,n){e(r,n,t)}},cz=function(t){function e(e,r){var n;return(n=t.call(this,{name:"RuntimeMeasurement",reporters:r,context:e})||this).initialize=!1,n.init(),n}return e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,cQ(e,t),e.prototype.init=function(){if(!this.initialize){this.performanceDataQueue=new cX;var t=y();t&&t._ppf&&(this.performanceDataQueue.handleCache(t._ppf),t._ppf=this.performanceDataQueue),this.initialize=!0}},e}(r9);cz=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er(),cZ(0,en(tK.CONTEXT)),cZ(1,en(tK.TTQ_REPORTERS))],cz),(t=iL||(iL={}))[t.OTHER=0]="OTHER",t[t.ANDROID=1]="ANDROID",t[t.IOS=2]="IOS";var c$=[aI.LOAD_START,aI.LOAD_END,aI.INIT_START,aI.INIT_END,aI.JSB_INIT_START,aI.JSB_INIT_END,aI.AD_INFO_INIT_START,aI.AD_INFO_INIT_END,aI.IDENTIFY_INIT_START,aI.IDENTIFY_INIT_END,aI.PLUGIN_INIT_START,aI.PLUGIN_INIT_END],c0=[aI.BEFORE_INIT,aI.INIT_START,aI.INIT_END,aI.HTTP_SEND,aI.AD_INFO_INIT_END];function c1(t){try{if(!performance||!performance.timing)return -1;if(t)return Date.now()-t;return Date.now()-performance.timing.navigationStart}catch(t){}return -1}var c2=function(t,e){void 0===e&&(e="");try{var r=[],n=performance.getEntriesByType("resource");if(n&&Array.isArray(n)){var o=[RegExp("/i18n/pixel/events\\.js\\?sdkid="+t),RegExp("/i18n/pixel/sdk\\.js\\?sdkid="+t)];n.forEach(function(t){var e=t.name,n=t.duration;o.some(function(t){return t.test(e)})&&r.push({name:e,duration:n})});var i=document&&document.currentScript,a=i&&i.src;if(a||!a&&e){var c=n.find(function(t){return t.name.indexOf(a||e)>-1});c&&r.push({name:c.name,duration:c.duration})}}return r}catch(t){return[]}};function c3(){c3=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",c=o.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function u(t,e,r,o){var i,a,c,s,u=Object._ttq_create((e&&e.prototype instanceof p?e:p).prototype);return n(u,"_invoke",{value:(i=t,a=r,c=new I(o||[]),s="suspendedStart",function(t,e){if("executing"===s)throw Error("Generator is already running");if("completed"===s){if("throw"===t)throw e;return T()}for(c.method=t,c.arg=e;;){var r=c.delegate;if(r){var n=function t(e,r){var n=r.method,o=e.iterator[n];if(void 0===o)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=void 0,t(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=TypeError("The iterator does not provide a '"+n+"' method")),l;var i=f(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,l;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=void 0),r.delegate=null,l):a:(r.method="throw",r.arg=TypeError("iterator result is not an object"),r.delegate=null,l)}(r,c);if(n){if(n===l)continue;return n}}if("next"===c.method)c.sent=c._sent=c.arg;else if("throw"===c.method){if("suspendedStart"===s)throw s="completed",c.arg;c.dispatchException(c.arg)}else"return"===c.method&&c.abrupt("return",c.arg);s="executing";var o=f(i,a,c);if("normal"===o.type){if(s=c.done?"completed":"suspendedYield",o.arg===l)continue;return{value:o.arg,done:c.done}}"throw"===o.type&&(s="completed",c.method="throw",c.arg=o.arg)}})}),u}function f(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var l={};function p(){}function h(){}function d(){}var v={};s(v,i,function(){return this});var _=Object.getPrototypeOf,g=_&&_(_(O([])));g&&g!==e&&r.call(g,i)&&(v=g);var y=d.prototype=p.prototype=Object._ttq_create(v);function m(t){["next","throw","return"].forEach(function(e){s(t,e,function(t){return this._invoke(e,t)})})}function w(t,e){var o;n(this,"_invoke",{value:function(n,i){function a(){return new e(function(o,a){!function n(o,i,a,c){var s=f(t[o],t,i);if("throw"!==s.type){var u=s.arg,l=u.value;return l&&"object"==typeof l&&r.call(l,"__await")?e.resolve(l.__await).then(function(t){n("next",t,a,c)},function(t){n("throw",t,a,c)}):e.resolve(l).then(function(t){u.value=t,a(u)},function(t){return n("throw",t,a,c)})}c(s.arg)}(n,i,o,a)})}return o=o?o.then(a,a):a()}})}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function b(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function I(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function O(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:T}}function T(){return{value:void 0,done:!0}}return h.prototype=d,n(y,"constructor",{value:d,configurable:!0}),n(d,"constructor",{value:h,configurable:!0}),h.displayName=s(d,c,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===h||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,d):(t.__proto__=d,s(t,c,"GeneratorFunction")),t.prototype=Object._ttq_create(y),t},t.awrap=function(t){return{__await:t}},m(w.prototype),s(w.prototype,a,function(){return this}),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(u(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then(function(t){return t.done?t.value:a.next()})},m(y),s(y,c,"Generator"),s(y,i,function(){return this}),s(y,"toString",function(){return"[object Generator]"}),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=O,I.prototype={constructor:I,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(b),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),s=r.call(i,"finallyLoc");if(c&&s){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),b(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;b(r)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:O(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},t}function c6(t,e,r,n,o,i,a){try{var c=t[i](a),s=c.value}catch(t){r(t);return}c.done?e(s):Promise.resolve(s).then(n,o)}function c5(t,e){return(c5=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}(e=iC||(iC={}))[e.NOT_DETERMINED=0]="NOT_DETERMINED",e[e.RESTRICTED=1]="RESTRICTED",e[e.DENIED=2]="DENIED",e[e.AUTHORIZED=3]="AUTHORIZED";var c8=function(t){function e(e){var r,n=e.pixelDetails,o=e.context,i=e.reporters,a=e.reportService,c=e.httpService,s=e.env,u=e.ttqOptions;return(r=t.call(this,{name:"Monitor",reporters:i,context:o})||this).hasReportMap={},r.cachedData=[],r.contextInitEndStatus=!1,r.legacy=[],r.env=s,r.lib=n.lib,r.pixelCode=n.pixelCode,r.reportService=a,r.httpService=c,r.contextInitEndStatus=!1,r.platform=tN()?iL.ANDROID:tR()?iL.IOS:iL.OTHER,r.partner=u.partner||"",u.legacy&&(r.legacy=u.legacy),r.ttqOptions=u,r}e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,c5(e,t);var r,n,o=e.prototype;return o.reportSwitch=function(){return!this.reporters.some(function(t){var e;return(null==(e=t.options)?void 0:e.monitor)===!1})},o.recordReportLog=function(t){var e=t.pixel_code;this.hasReportMap[e]||(this.hasReportMap[e]=[]),this.hasReportMap[e].push(t)},r=c3().mark(function t(e,r){return c3().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.httpService.send(e,r);case 2:!t.sent&&this.httpService.sendByImage&&this.httpService.sendByImage(e,{log_message:rN(r)});case 4:case"end":return t.stop()}},t,this)}),n=function(){var t=this,e=arguments;return new Promise(function(n,o){var i=r.apply(t,e);function a(t){c6(i,n,o,a,c,"next",t)}function c(t){c6(i,n,o,a,c,"throw",t)}a(void 0)})},o.send=function(t,e){return n.apply(this,arguments)},o.sendByReportService=function(t){var e,r=(e=t.metric_name,c0.indexOf(e)>-1);this.reportService.report(ew,t,r?aB.defaultReport:aB.htmlHttpReport,r?aY.P0:aY.P1)},o.report=function(t,e){return(void 0===e&&(e=!1),this.recordReportLog(t),e)?void this.send(ew,t):this.contextInitEndStatus?void this.sendByReportService(t):void this.cachedData.push(t)},o.contextInitEnd=function(){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var t={taskName:window.ttq._pf_tn,functionName:"monitor_plogin_context_init_end",start:performance.now()}}catch(t){}for(this.contextInitEndStatus=!0;this.cachedData.length;){var e=this.cachedData.shift();e&&null!=e.metric_name&&this.sendByReportService(e)}try{window.ttq&&window.ttq._ppf&&(t.end=performance.now(),window.ttq._ppf.push(t))}catch(t){}},o.info=function(t,e,r){if(void 0===e&&(e={}),void 0===r&&(r=!1),this.reportSwitch())try{var n=this.assemblyBaseData(t,e);if(this.isRepeatLog(n))return;this.report(n,r)}catch(t){}},o.error=function(t,e,r,n){if(void 0===r&&(r={}),void 0===n&&(n=!1),this.reportSwitch())try{var o=this.assemblyBaseData(t,r);if(Object.assign(o.ext_json||{},{message:e.message,stack:e.stack}),this.isRepeatLog(o))return;this.report(o,n)}catch(t){}},o.isRepeatLog=function(t){var e=t.pixel_code,r=t.metric_name;if(!e||!r)return!1;var n=c$.some(function(t){return r.indexOf(t)>-1}),o=this.hasReportMap[e],i=o&&o.some(function(t){return t.metric_name===r});return n&&i},o.getReportInfo=function(t){var e=this.reporters.find(function(e){return e.getReporterId()===t});if(e)return e.reporterInfo;var r=y()._i[t];if(r)return r.info},o.assemblyBaseData=function(t,e){var r=e||{},n=r.pixelCode,o=r.extJSON,i=r.custom_name,a=r.custom_enum,c=r.app_name,s=r.latency,u=y(),f=this.env,l=this.platform,p=this.context,h=this.reporters,d=p.getAllData(),v=d.libraryInfo,_=d.pageInfo,g=d.adInfo,m=d.pageSign,w=d.enableAdTracking,b=p.getVariationId(),I=p.getVids(),T=this.legacy||[],N=g&&g.log_extra&&function(t){try{return JSON.parse(t)}catch(t){return{}}}(g.log_extra)||{},R=h.map(function(t){return t.getReporterId()}),S=n||L().pixelCode||this.pixelCode||this.reporters[0]&&this.reporters[0].getReporterId()||Object.keys(u._i)[0]||"",P=this.getReportInfo(S),A=T.length?T.includes(S)?"legacy-ttq":"ttq":this.lib,x=O(S)||this.partner||"non",k=w?iC.AUTHORIZED:iC.DENIED,D={metric_name:t,pixel_code:S,platform:l,app_name:(void 0===c?"":c)||(void 0===f?"":f),net_type:function(){try{var t=navigator&&(navigator.connection||navigator.mozConnection||navigator.webkitConnection);return t?t.effectiveType:""}catch(t){}return""}(),placement:N.placement||"non",lib:{lib_version:v&&v.version||"",ttq_name:A},in_iframe:Number(tD()),time_to_last_start:-1,time_to_navigation_start:-1,latency:void 0===s?0:s,page_url:_&&_.url||ec().url,att_status:k,pageview_id:this.context.getPageViewId()||"",session_id:m&&m.sessionId||"",variation_id:b,vids:I,load_id:P&&P.loadId||"",ad_id:String(g.ad_id),creative_id:String(g.creative_id||N.creative_id),user_agent:E(),pixel_monitor_version:"v2",custom_name:i,custom_enum:a,partner:x,ext_json:{performance:[],pixel_list:R.toString(),ad_info_from:g.ad_info_from,pixel_api_conflict_flag:C()}};return o&&Object.assign(D.ext_json,o),D},o.initStart=function(t){this.info(aI.INIT_START,t)},o.initEnd=function(t){this.info(aI.INIT_END,t)},o.jsbInitStart=function(){this.info(aI.JSB_INIT_START)},o.jsbInitEnd=function(){this.info(aI.JSB_INIT_END)},o.beforeAdInfoInitStart=function(t){this.info(aI.BEFORE_AD_INFO_INIT_START,t)},o.adInfoInitStart=function(t){this.info(aI.AD_INFO_INIT_START,t)},o.adInfoInitEnd=function(t){this.info(aI.AD_INFO_INIT_END,t)},o.pluginInitStart=function(t){this.info(t+aI.PLUGIN_INIT_START)},o.pluginInitEnd=function(t){this.info(t+aI.PLUGIN_INIT_END)},e}(r9);function c4(t,e){return(c4=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var c9=function(t,e){return function(r,n){e(r,n,t)}},c7=function(t){function e(e,r,n,o,i,a){var c,s=L();return(c=t.call(this,{pixelDetails:s,context:e,reporters:r,reportService:n,httpService:o,env:i,ttqOptions:a})||this).statusTimeline={},c}e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,c4(e,t);var r=e.prototype;return r.getDurationTime=function(t){try{var e,r=(e=t.match(/_(start|end)$/))&&e[1];if(!r)return 0;var n=t.split("_").slice(0,-1).join("_"),o=this.statusTimeline[n];switch(!o&&(this.statusTimeline[n]={},o=this.statusTimeline[n]),r){case"start":o.start=o.start||Date.now();break;case"end":if(!o.start)return -1;return o.end=o.end||Date.now(),o.end-o.start}}catch(t){}return 0},r.assemblyBaseData=function(e,r){var n=t.prototype.assemblyBaseData.call(this,e,r),o=n.pixel_code,i=n.latency,a=this.getReportInfo(o),c=a&&a.loadStart;return Object.assign(n,{time_to_last_start:c?c1(c):-1,time_to_navigation_start:c1(),latency:i=i||this.getDurationTime(e)}),[aI.BEFORE_INIT,aI.IDENTIFY_INIT_END].includes(e)&&(n.ext_json=n.ext_json||{},n.ext_json.performance=c2(o,e===aI.IDENTIFY_INIT_END?"identify":"")),n},e}(c8);c7=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er(),c9(0,en(tK.CONTEXT)),c9(1,en(tK.TTQ_REPORTERS)),c9(2,en(tK.REPORT_SERVICE)),c9(3,en(tK.HTTP_SERVICE)),c9(4,en(tK.ENV)),c9(5,en(tK.TTQ_GLOBAL_OPTIONS))],c7);var st=[].concat([{identifier:tK.MONITOR_PLUGIN,to:c7,name:"Monitor",required:!1},{identifier:tK.AUTO_CONFIG_PLUGIN,to:oJ,name:"AutoConfig",required:!1},{identifier:tK.DIAGNOSTICS_CONSOLE_PLUGIN,to:ih,name:"DiagnosticsConsole",required:!1},{identifier:tK.RUNTIME_MEASUREMENT_PLUGIN,to:cz,name:"RuntimeMeasurement",required:!1}],[{identifier:tK.CALLBACK_PLUGIN,to:ne,name:"Callback"},{identifier:tK.IDENTIFY_PLUGIN,to:nw,name:"Identify",required:!0},{identifier:tK.PANGLE_COOKIE_MATCHING_PLUGIN,to:i_,name:"PangleCookieMatching"},{identifier:tK.EVENT_BUILDER_PLUGIN,to:iE,name:"EventBuilder"},{identifier:tK.ENRICH_IPV6_PLUGIN,to:iT,name:"EnrichIpv6"},{identifier:tK.PAGEDATA_PLUGIN,to:cK,name:"PageData"}]),se=function(t,e){};function sr(t,e){return(sr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var sn=function(t){function e(e){var r;return(r=t.call(this,e)||this).observers=new Set([]),r}e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,sr(e,t);var r=e.prototype;return r.addObserver=function(t){this.observers.has(t)||this.observers.add(t)},r.removeObserver=function(t){this.observers.delete(t)},r.notifyObservers=function(t,e){this.observers.forEach(function(r){return r.call(e,t)})},e}(r8),so="HistoryObserver";(ik||(ik={})).DYNAMIC_WEB_PAGEVIEW="dynamic_web_pageview";var si=["GoogleTagManagerClient","GoogleTagManagerServer"];function sa(t,e){var r=history[t];return function(){var t=Array.prototype.slice.call(arguments);r.apply(history,t),e()}}function sc(t){var e=t.options,r=t.plugins;return e&&!1!==e.historyObserver&&r&&r[so]}function ss(t,e){var r=ec().url,n=t.context.getPageInfo().url;if(r!==n){var o,i=t.context.setPageInfo(r,n);i&&i.pageIndex&&eD(i.pageIndex),t.reporters.filter((o=ik.DYNAMIC_WEB_PAGEVIEW,function(t){var e=t.plugins;return!!(sc(t)&&e[so]&&e[so][o])})).forEach(function(t){setTimeout(function(){t.page(e)})})}}function su(t,e){return(su=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}var sf=function(t,e){return function(r,n){e(r,n,t)}},sl=function(t){function e(e,r){var n;return(n=t.call(this,{name:so,reporters:r,context:e})||this).enableListenSPAHistoryChange=!1,n.listenSPAHistoryChange=tt(function(){var t=function(){n.enableListenSPAHistoryChange&&n.notifyObservers({tf:aq.HISTORY_CHANGE})},e=function(){setTimeout(t)};window.addEventListener("popstate",e),history.pushState=sa("pushState",t),history.replaceState=sa("replaceState",t)}),n.dynamicWebPageviewHandler=ss.bind(null,function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(n)),n}e.prototype=Object._ttq_create(t.prototype),e.prototype.constructor=e,su(e,t);var r=e.prototype;return r.initListener=function(t){this.enableListenSPAHistoryChange=function(t){if(!t)return!1;var e=O(),r=t.getReporterPartner();return!(e&&!si.includes(e)||r&&t.isPartnerReporter()&&!si.includes(r))}(t),this.enableListenSPAHistoryChange&&this.listenSPAHistoryChange()},r.pixelSend=function(t,e){var r=this.reporters.find(function(e){return e.getReporterId()===t});r&&sc(r)&&(e&&"pageview"===e.toLowerCase()&&this.addObserver(this.dynamicWebPageviewHandler),this.initListener(r))},r.pageUrlWillChange=function(){if(this.enableListenSPAHistoryChange)return void this.notifyObservers({tf:aq.HISTORY_CHANGE});this.notifyObservers({tf:aq.URL_CHANGE,event_experiment:"pageview"})},e}(sn);sl=function(t,e,r,n){var o,i=arguments.length,a=i<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,r,n);else for(var c=t.length-1;c>=0;c--)(o=t[c])&&(a=(i<3?o(a):i>3?o(e,r,a):o(e,r))||a);return i>3&&a&&Object.defineProperty(e,r,a),a}([er(),sf(0,en(tK.CONTEXT)),sf(1,en(tK.TTQ_REPORTERS))],sl);var sp=[{identifier:tK.HISTORY_OBSERVER,to:sl,name:"HistoryObserver"}],sh=y(),sd=(null==sh?void 0:sh._container)||new ee,sv=(null==sh?void 0:sh._container)?aG.REBIND:aG.BIND;tw();var s_=sd[sv](tK.ENV),sg=sd[sv](tm.SignalType);sd[sv](tm.ID),sd[sv](tm.Type),sd[sv](tm.Options),sd[sv](tm.Plugins),sd[sv](tm.Rules),sd[sv](tm.Info);var sy=sd[sv](tm.WebLibraryInfo),sm=sd[sv](tK.TTQ_GLOBAL_OPTIONS);try{if(!sd.get(tK.TTQ_GLOBAL_OPTIONS))throw Error("")}catch(t){sm.toConstantValue({})}var sw=function(t,e){var r=tE();sy.toConstantValue({name:"pixel.js",version:"2.2.1",options:void 0}),s_.toConstantValue(e),sg.toConstantValue(r)},sE=function(t){(null==t?void 0:t._container)||(sd.bind(tK.TTQ).to(rv).inSingletonScope(),sd.bind(tK.CONTEXT).to(rw).inSingletonScope(),sd.bind(tK.REPORTER).to(ra),sd.bind(tK.TTQ_REPORTERS).toConstantValue([]),sd.bind(tK.REPORT_SERVICE).to(rF).inSingletonScope(),sd.bind(tK.AD_SERVICE).to(rG).inSingletonScope(),sd.bind(tK.APP_SERVICE).to(rB).inSingletonScope(),sd.bind(tK.BRIDGE_SERVICE).to(rQ).inSingletonScope(),sd.bind(tK.HTTP_SERVICE).to(r$).inSingletonScope(),sd.bind(tm.IsOnsitePage).toConstantValue({value:!1}),sd.bind(tK.COOKIE_SERVICE).to(r2).inSingletonScope(),sd.bind(tK.CONSENT_SERVICE).to(r5).inSingletonScope()),t&&!t._container&&(t._container=sd)},sb=function(){st.forEach(function(t){var e=t.to,r=t.name,n=void 0===r?"":r,o=t.required,i=t.identifier;(void 0!==o&&o||N(n))&&!sd.isBound(i)&&sd.bind(i).to(e).inSingletonScope(),sh&&sh._mounted&&!sh.getPlugin(n)&&sd.isBound(i)&&sh.usePlugin(sd.get(i))})},sI=function(){sp.forEach(function(t){var e=t.to,r=t.name,n=t.identifier;N(void 0===r?"":r)&&!sd.isBound(n)&&sd.bind(n).to(e).inSingletonScope()})},sO=function(t,e,r){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var n={taskName:window.ttq._pf_tn,functionName:"initAllModule",start:performance.now()}}catch(t){}sw(t,r),t3(e,t),sI(),sE(t),sb(),se(e,t);try{window.ttq&&window.ttq._ppf&&(n.end=performance.now(),window.ttq._ppf.push(n))}catch(t){}},sT=function(){try{var t=sd.get(tK.MONITOR_PLUGIN);if(t)return t;return null}catch(t){return null}},sN=function(t){var e=this.constructor;return this.then(function(r){return e.resolve(t()).then(function(){return r})},function(r){return e.resolve(t()).then(function(){return e.reject(r)})})},sR=function(t){return new this(function(e,r){if(!(t&&void 0!==t.length))return r(TypeError(typeof t+" "+t+" is not iterable(cannot read property Symbol(Symbol.iterator))"));var n=Array.prototype.slice.call(t);if(0===n.length)return e([]);for(var o=n.length,i=0;i<n.length;i++)!function t(r,i){if(i&&("object"==typeof i||"function"==typeof i)){var a=i.then;if("function"==typeof a)return void a.call(i,function(e){t(r,e)},function(t){n[r]={status:"rejected",reason:t},0==--o&&e(n)})}n[r]={status:"fulfilled",value:i},0==--o&&e(n)}(i,n[i])})};function sS(t,e){this.name="AggregateError",this.errors=t,this.message=e||""}sS.prototype=Error.prototype;var sP=function(t){var e=this;return new e(function(r,n){if(!(t&&void 0!==t.length))return n(TypeError("Promise.any accepts an array"));var o=Array.prototype.slice.call(t);if(0===o.length)return n();for(var i=[],a=0;a<o.length;a++)try{e.resolve(o[a]).then(r).catch(function(t){i.push(t),i.length===o.length&&n(new sS(i,"All promises were rejected"))})}catch(t){n(t)}})},sA=setTimeout;function sx(t){return!!(t&&void 0!==t.length)}function sL(){}function sC(t){if(!(this instanceof sC))throw TypeError("Promises must be constructed via new");if("function"!=typeof t)throw TypeError("not a function");this._state=0,this._handled=!1,this._value=void 0,this._deferreds=[],sq(t,this)}function sk(t,e){for(;3===t._state;)t=t._value;if(0===t._state)return void t._deferreds.push(e);t._handled=!0,sC._immediateFn(function(){var r,n=1===t._state?e.onFulfilled:e.onRejected;if(null===n)return void(1===t._state?sD:sj)(e.promise,t._value);try{r=n(t._value)}catch(t){sj(e.promise,t);return}sD(e.promise,r)})}function sD(t,e){try{if(e===t)throw TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var r=e.then;if(e instanceof sC){t._state=3,t._value=e,sM(t);return}if("function"==typeof r)return void sq(function(){r.apply(e,arguments)},t)}t._state=1,t._value=e,sM(t)}catch(e){sj(t,e)}}function sj(t,e){t._state=2,t._value=e,sM(t)}function sM(t){2===t._state&&0===t._deferreds.length&&sC._immediateFn(function(){t._handled||sC._unhandledRejectionFn(t._value)});for(var e=0,r=t._deferreds.length;e<r;e++)sk(t,t._deferreds[e]);t._deferreds=null}function sF(t,e,r){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof e?e:null,this.promise=r}function sq(t,e){var r=!1;try{t(function(t){r||(r=!0,sD(e,t))},function(t){r||(r=!0,sj(e,t))})}catch(t){if(r)return;r=!0,sj(e,t)}}sC.prototype.catch=function(t){return this.then(null,t)},sC.prototype.then=function(t,e){var r=new this.constructor(sL);return sk(this,new sF(t,e,r)),r},sC.prototype.finally=sN,sC.all=function(t){return new sC(function(e,r){if(!sx(t))return r(TypeError("Promise.all accepts an array"));var n=Array.prototype.slice.call(t);if(0===n.length)return e([]);for(var o=n.length,i=0;i<n.length;i++)!function t(i,a){try{if(a&&("object"==typeof a||"function"==typeof a)){var c=a.then;if("function"==typeof c)return void c.call(a,function(e){t(i,e)},r)}n[i]=a,0==--o&&e(n)}catch(t){r(t)}}(i,n[i])})},sC.any=sP,sC.allSettled=sR,sC.resolve=function(t){return t&&"object"==typeof t&&t.constructor===sC?t:new sC(function(e){e(t)})},sC.reject=function(t){return new sC(function(e,r){r(t)})},sC.race=function(t){return new sC(function(e,r){if(!sx(t))return r(TypeError("Promise.race accepts an array"));for(var n=0,o=t.length;n<o;n++)sC.resolve(t[n]).then(e,r)})},sC._immediateFn="function"==typeof setImmediate&&function(t){setImmediate(t)}||function(t){sA(t,0)},sC._unhandledRejectionFn=function(t){};var sU=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r.g)return r.g;throw Error("unable to locate global object")}();"function"!=typeof sU.Promise?sU.Promise=sC:(sU.Promise.prototype.finally||(sU.Promise.prototype.finally=sN),sU.Promise.allSettled||(sU.Promise.allSettled=sR),sU.Promise.any||(sU.Promise.any=sP));var sG=g();try{!function(){try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf){var t={taskName:window.ttq._pf_tn||"init",functionName:window.ttq._pf_tn&&"init",start:performance.now()};window.ttq._pf_tn||(window.ttq._pf_tn="init")}}catch(t){}var e=L().pixelCode,r=y(),n=tw(),o=tE();if(sO(r,sd,n),N("Monitor")){var i=sT();null==i||i.info(aI.BEFORE_INIT,{pixelCode:e,extJSON:{stack:tv(e)}})}if(r){r._mounted?(P(aI.HANDLE_CACHE,{pixelCode:e}),t5(sd,r),sd.get(tK.TTQ).dispatch(aM.PATCH_END)):(iD=t$(sd,n,o),window[sG]=t1(r,iD),r.resetCookieExpires&&r.resetCookieExpires(),t5(sd,iD),t6(iD));var a=sd.get(tm.IsOnsitePage);a.value=o===aD.ONSITE||r.reporters.every(function(t){return t.isOnsite()}),sd.rebind(tm.IsOnsitePage).toConstantValue(a);try{if(window.ttq&&!window.ttq._ppf&&(window.ttq._ppf=[]),window.ttq&&window.ttq._ppf)var c={taskName:window.ttq._pf_tn,functionName:"handlePixelRules",start:performance.now()}}catch(t){}r.reporters.forEach(function(t){var e;(null==(e=t.rules)?void 0:e.length)>0&&ij&&new ij(t.getReporterId(),t.rules)});try{window.ttq&&window.ttq._ppf&&(c.end=performance.now(),window.ttq._ppf.push(c))}catch(t){}try{window.ttq&&window.ttq._ppf&&(t.end=performance.now(),window.ttq._ppf.push(t),"init"===window.ttq._pf_tn&&(window.ttq._pf_tn=""))}catch(t){}}}()}catch(t){A(aI.INIT_ERROR,t)}}()}();