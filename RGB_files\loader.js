(function(){var f=function(a){return a.raw=a},g=function(a,d){a.raw=d;return a},h=function(){for(var a=Number(this),d=[],b=a;b<arguments.length;b++)d[b-a]=arguments[b];return d};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var k=this||self,l=function(a,d){function b(){}b.prototype=d.prototype;a.i=d.prototype;a.prototype=new b;a.prototype.constructor=a;a.h=function(c,e,ka){for(var C=Array(arguments.length-2),q=2;q<arguments.length;q++)C[q-2]=arguments[q];return d.prototype[e].apply(c,C)}},m=function(a){return a};function n(a,d){if(Error.captureStackTrace)Error.captureStackTrace(this,n);else{var b=Error().stack;b&&(this.stack=b)}a&&(this.message=String(a));void 0!==d&&(this.cause=d)}l(n,Error);n.prototype.name="CustomError";function p(a,d){a=a.split("%s");for(var b="",c=a.length-1,e=0;e<c;e++)b+=a[e]+(e<d.length?d[e]:"%s");n.call(this,b+a[c])}l(p,n);p.prototype.name="AssertionError";var r;var u=function(a,d){if(d!==t)throw Error("TrustedResourceUrl is not meant to be built directly");this.g=a};u.prototype.toString=function(){return this.g+""};var t={},v=function(a){if(void 0===r){var d=null;var b=k.trustedTypes;if(b&&b.createPolicy){try{d=b.createPolicy("goog#html",{createHTML:m,createScript:m,createScriptURL:m})}catch(c){k.console&&k.console.error(c.message)}r=d}else r=d}a=(d=r)?d.createScriptURL(a):a;return new u(a,t)};var x=function(a){if(w!==w)throw Error("SafeUrl is not meant to be built directly");this.g=a};x.prototype.toString=function(){return this.g.toString()};var w={};new x("about:invalid#zClosurez");new x("about:blank");var y={},z=function(){if(y!==y)throw Error("SafeStyle is not meant to be built directly");};z.prototype.toString=function(){return"".toString()};new z;var A={},B=function(){if(A!==A)throw Error("SafeStyleSheet is not meant to be built directly");};B.prototype.toString=function(){return"".toString()};new B;var D={},E=function(){var a=k.trustedTypes&&k.trustedTypes.emptyHTML||"";if(D!==D)throw Error("SafeHtml is not meant to be built directly");this.g=a};E.prototype.toString=function(){return this.g.toString()};new E;/*

 SPDX-License-Identifier: Apache-2.0
*/
var F=f([""]),G=g(["\x00"],["\\0"]),H=g(["\n"],["\\n"]),I=g(["\x00"],["\\u0000"]),J=f([""]),aa=g(["\x00"],["\\0"]),ba=g(["\n"],["\\n"]),ca=g(["\x00"],["\\u0000"]);function K(a){return Object.isFrozen(a)&&Object.isFrozen(a.raw)}function L(a){return-1===a.toString().indexOf("`")}var M=L(function(a){return a(F)})||L(function(a){return a(G)})||L(function(a){return a(H)})||L(function(a){return a(I)}),da=K(J)&&K(aa)&&K(ba)&&K(ca);new x("about:blank");new x("about:invalid#zClosurez");var N=[],O=function(a){console.warn("A URL with content '"+a+"' was sanitized away.")};-1===N.indexOf(O)&&N.push(O);function P(a){var d=h.apply(1,arguments);if(!Array.isArray(a)||!Array.isArray(a.raw)||a.length!==a.raw.length||!M&&a===a.raw||!(M&&!da||K(a))||d.length+1!==a.length)throw new TypeError("\n    ############################## ERROR ##############################\n\n    It looks like you are trying to call a template tag function (fn`...`)\n    using the normal function syntax (fn(...)), which is not supported.\n\n    The functions in the safevalues library are not designed to be called\n    like normal functions, and doing so invalidates the security guarantees\n    that safevalues provides.\n\n    If you are stuck and not sure how to proceed, please reach out to us\n    instead through:\n     - go/ise-hardening-yaqs (preferred) // LINE-INTERNAL\n     - g/ise-hardening // LINE-INTERNAL\n     - https://github.com/google/safevalues/issues\n\n    ############################## ERROR ##############################");
if(0===d.length)return v(a[0]);var b=a[0].toLowerCase();if(/^data:/.test(b))throw Error("Data URLs cannot have expressions in the template literal input.");if(/^https:\/\//.test(b)||/^\/\//.test(b)){var c=b.indexOf("//")+2;var e=b.indexOf("/",c);if(e<=c)throw Error("Can't interpolate data in a url's origin, Please make sure to fully specify the origin, terminated with '/'.");c=b.substring(c,e);if(!/^[0-9a-z.:-]+$/i.test(c))throw Error("The origin contains unsupported characters.");if(!/^[^:]*(:[0-9]+)?$/i.test(c))throw Error("Invalid port number.");
if(!/(^|\.)[a-z][^.]*$/i.test(c))throw Error("The top-level domain must start with a letter.");c=!0}else c=!1;if(!c)if(/^\//.test(b))if("/"===b||1<b.length&&"/"!==b[1]&&"\\"!==b[1])c=!0;else throw Error("The path start in the url is invalid.");else c=!1;if(!(c=c||RegExp("^[^:\\s\\\\/]+/").test(b)))if(/^about:blank/.test(b)){if("about:blank"!==b&&!/^about:blank#/.test(b))throw Error("The about url is invalid.");c=!0}else c=!1;if(!c)throw Error("Trying to interpolate expressions in an unsupported url format.");
b=a[0];for(c=0;c<d.length;c++)b+=encodeURIComponent(d[c])+a[c+1];return v(b)};var ea=f(["//www.gstatic.com/call-tracking/call-tracking_9.js"]),fa=f(["//www.gstatic.com/call-tracking/call-tracking_9.js"]),ha=[0],ia=[],ja=P(ea),Q=P(fa);if(window._googWcmAk){var R=parseInt(window._googWcmAk,10);-1===ha.indexOf(R)&&(0<=ia.indexOf(R)||0>R%100)&&(Q=ja)}var S=document.createElement("script");S.async=1;var T;
if(Q instanceof u&&Q.constructor===u)T=Q.g;else{var U=typeof Q;(function(a,d){throw new p("Failure"+(a?": "+a:""),Array.prototype.slice.call(arguments,1));})("expected object of type TrustedResourceUrl, got '%s' of type %s",Q,"object"!=U?U:Q?Array.isArray(Q)?"array":U:"null");T="type_error:TrustedResourceUrl"}S.src=T;var V,W,X,Y=null==(X=(W=(S.ownerDocument&&S.ownerDocument.defaultView||window).document).querySelector)?void 0:X.call(W,"script[nonce]");
(V=Y?Y.nonce||Y.getAttribute("nonce")||"":"")&&S.setAttribute("nonce",V);var Z=document.getElementsByTagName("script")[0];S.setAttribute("nonce",Z.nonce||Z.getAttribute("nonce"));Z.parentNode.insertBefore(S,Z);}).call(this);
