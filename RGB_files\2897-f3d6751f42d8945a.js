!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="09215028-3c3b-4dc2-9dec-1d1799efab2a",e._sentryDebugIdIdentifier="sentry-dbid-09215028-3c3b-4dc2-9dec-1d1799efab2a")}catch(e){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2897],{8139:(e,t,n)=>{n.r(t),n.d(t,{default:()=>r.a});var i=n(44760),r=n.n(i),a={};for(let e in i)"default"!==e&&(a[e]=()=>i[e]);n.d(t,a)},9064:(e,t,n)=>{n.d(t,{Kq:()=>o,Ay:()=>c});var i=n(7620);let r=n(26385).A,a=i.createContext(new r),{Provider:o,Consumer:s}=a,c=a},9126:(e,t,n)=>{var i=n(29775);function r(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter(Boolean).join(".")}function a(e){return r(e.namespace,e.key)}function o(e){console.error(e)}function s(e,t){return i.memoize(e,{cache:{create:()=>({get:e=>t[e],set(e,n){t[e]=n}})},strategy:i.strategies.variadic})}function c(e,t){return s(function(){for(var t=arguments.length,n=Array(t),i=0;i<t;i++)n[i]=arguments[i];return new e(...n)},t)}t.createCache=function(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}},t.createIntlFormatters=function(e){return{getDateTimeFormat:c(Intl.DateTimeFormat,e.dateTime),getNumberFormat:c(Intl.NumberFormat,e.number),getPluralRules:c(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:c(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:c(Intl.ListFormat,e.list),getDisplayNames:c(Intl.DisplayNames,e.displayNames)}},t.defaultGetMessageFallback=a,t.defaultOnError=o,t.initializeConfig=function(e){let{getMessageFallback:t,messages:n,onError:i,...r}=e;return{...r,messages:n,onError:i||o,getMessageFallback:t||a}},t.joinPath=r,t.memoFn=s},26385:(e,t,n)=>{n.d(t,{A:()=>o});var i=n(52299);function r(e,t={}){var n;let i=(n=e)&&"j"===n[0]&&":"===n[1]?n.substr(2):n;if(!t.doNotParse)try{return JSON.parse(i)}catch(e){}return e}class a{constructor(e,t={}){this.changeListeners=[],this.HAS_DOCUMENT_COOKIE=!1,this.update=()=>{if(!this.HAS_DOCUMENT_COOKIE)return;let e=this.cookies;this.cookies=i.q(document.cookie),this._checkChanges(e)};let r="undefined"==typeof document?"":document.cookie;this.cookies=function(e){return"string"==typeof e?i.q(e):"object"==typeof e&&null!==e?e:{}}(e||r),this.defaultSetOptions=t,this.HAS_DOCUMENT_COOKIE=function(){let e=void 0===n.g?void 0:n.g.TEST_HAS_DOCUMENT_COOKIE;return"boolean"==typeof e?e:"object"==typeof document&&"string"==typeof document.cookie}()}_emitChange(e){for(let t=0;t<this.changeListeners.length;++t)this.changeListeners[t](e)}_checkChanges(e){new Set(Object.keys(e).concat(Object.keys(this.cookies))).forEach(t=>{e[t]!==this.cookies[t]&&this._emitChange({name:t,value:r(this.cookies[t])})})}_startPolling(){this.pollingInterval=setInterval(this.update,300)}_stopPolling(){this.pollingInterval&&clearInterval(this.pollingInterval)}get(e,t={}){return t.doNotUpdate||this.update(),r(this.cookies[e],t)}getAll(e={}){e.doNotUpdate||this.update();let t={};for(let n in this.cookies)t[n]=r(this.cookies[n],e);return t}set(e,t,n){n=n?Object.assign(Object.assign({},this.defaultSetOptions),n):this.defaultSetOptions;let r="string"==typeof t?t:JSON.stringify(t);this.cookies=Object.assign(Object.assign({},this.cookies),{[e]:r}),this.HAS_DOCUMENT_COOKIE&&(document.cookie=i.l(e,r,n)),this._emitChange({name:e,value:t,options:n})}remove(e,t){let n=t=Object.assign(Object.assign(Object.assign({},this.defaultSetOptions),t),{expires:new Date(1970,1,1,0,0,1),maxAge:0});this.cookies=Object.assign({},this.cookies),delete this.cookies[e],this.HAS_DOCUMENT_COOKIE&&(document.cookie=i.l(e,"",n)),this._emitChange({name:e,value:void 0,options:t})}addChangeListener(e){this.changeListeners.push(e),this.HAS_DOCUMENT_COOKIE&&1===this.changeListeners.length&&("object"==typeof window&&"cookieStore"in window?window.cookieStore.addEventListener("change",this.update):this._startPolling())}removeChangeListener(e){let t=this.changeListeners.indexOf(e);t>=0&&this.changeListeners.splice(t,1),this.HAS_DOCUMENT_COOKIE&&0===this.changeListeners.length&&("object"==typeof window&&"cookieStore"in window?window.cookieStore.removeEventListener("change",this.update):this._stopPolling())}}let o=a},29775:(e,t,n)=>{function i(e,t){var n=t&&t.cache?t.cache:c,i=t&&t.serializer?t.serializer:o;return(t&&t.strategy?t.strategy:function(e,t){var n,i,o=1===e.length?r:a;return n=t.cache.create(),i=t.serializer,o.bind(this,e,n,i)})(e,{cache:n,serializer:i})}function r(e,t,n,i){var r=null==i||"number"==typeof i||"boolean"==typeof i?i:n(i),a=t.get(r);return void 0===a&&(a=e.call(this,i),t.set(r,a)),a}function a(e,t,n){var i=Array.prototype.slice.call(arguments,3),r=n(i),a=t.get(r);return void 0===a&&(a=e.apply(this,i),t.set(r,a)),a}n.r(t),n.d(t,{memoize:()=>i,strategies:()=>l});var o=function(){return JSON.stringify(arguments)},s=function(){function e(){this.cache=Object.create(null)}return e.prototype.get=function(e){return this.cache[e]},e.prototype.set=function(e,t){this.cache[e]=t},e}(),c={create:function(){return new s}},l={variadic:function(e,t){var n,i;return n=t.cache.create(),i=t.serializer,a.bind(this,e,n,i)},monadic:function(e,t){var n,i;return n=t.cache.create(),i=t.serializer,r.bind(this,e,n,i)}}},52299:(e,t)=>{t.q=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var n={},i=(t||{}).decode||r,a=0;a<e.length;){var o=e.indexOf("=",a);if(-1===o)break;var s=e.indexOf(";",a);if(-1===s)s=e.length;else if(s<o){a=e.lastIndexOf(";",o-1)+1;continue}var c=e.slice(a,o).trim();if(void 0===n[c]){var l=e.slice(o+1,s).trim();34===l.charCodeAt(0)&&(l=l.slice(1,-1)),n[c]=function(e,t){try{return t(e)}catch(t){return e}}(l,i)}a=s+1}return n},t.l=function(e,t,r){var o=r||{},s=o.encode||a;if("function"!=typeof s)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var c=s(t);if(c&&!i.test(c))throw TypeError("argument val is invalid");var l=e+"="+c;if(null!=o.maxAge){var u=o.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(o.domain){if(!i.test(o.domain))throw TypeError("option domain is invalid");l+="; Domain="+o.domain}if(o.path){if(!i.test(o.path))throw TypeError("option path is invalid");l+="; Path="+o.path}if(o.expires){var d,f=o.expires;if(d=f,"[object Date]"!==n.call(d)&&!(d instanceof Date)||isNaN(f.valueOf()))throw TypeError("option expires is invalid");l+="; Expires="+f.toUTCString()}if(o.httpOnly&&(l+="; HttpOnly"),o.secure&&(l+="; Secure"),o.partitioned&&(l+="; Partitioned"),o.priority)switch("string"==typeof o.priority?o.priority.toLowerCase():o.priority){case"low":l+="; Priority=Low";break;case"medium":l+="; Priority=Medium";break;case"high":l+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(o.sameSite)switch("string"==typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var n=Object.prototype.toString,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function r(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}function a(e){return encodeURIComponent(e)}},57175:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});let i=n(54568),r=n(7620);t.default=function(e){let{html:t,height:n=null,width:a=null,children:o,dataNtpc:s=""}=e;return(0,r.useEffect)(()=>{s&&performance.mark("mark_feature_usage",{detail:{feature:"next-third-parties-".concat(s)}})},[s]),(0,i.jsxs)(i.Fragment,{children:[o,t?(0,i.jsx)("div",{style:{height:null!=n?"".concat(n,"px"):"auto",width:null!=a?"".concat(a,"px"):"auto"},"data-ntpc":s,dangerouslySetInnerHTML:{__html:t}}):null]})}},59734:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});var i=n(7620),r=n(9126),a=n(90535);n(29775);var o=function(e){return e&&e.__esModule?e:{default:e}}(i);t.IntlProvider=function(e){let{children:t,defaultTranslationValues:n,formats:s,getMessageFallback:c,locale:l,messages:u,now:d,onError:f,timeZone:g}=e,h=i.useMemo(()=>r.createCache(),[l]),m=i.useMemo(()=>r.createIntlFormatters(h),[h]),p=i.useMemo(()=>({...r.initializeConfig({locale:l,defaultTranslationValues:n,formats:s,getMessageFallback:c,messages:u,now:d,onError:f,timeZone:g}),formatters:m,cache:h}),[h,n,s,m,c,l,u,d,f,g]);return o.default.createElement(a.IntlContext.Provider,{value:p},t)}},76206:(e,t,n)=>{let i;Object.defineProperty(t,"__esModule",{value:!0}),t.sendGAEvent=t.GoogleAnalytics=void 0;let r=n(54568),a=n(7620),o=function(e){return e&&e.__esModule?e:{default:e}}(n(8139));t.GoogleAnalytics=function(e){let{gaId:t,dataLayerName:n="dataLayer"}=e;return void 0===i&&(i=n),(0,a.useEffect)(()=>{performance.mark("mark_feature_usage",{detail:{feature:"next-third-parties-ga"}})},[]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(o.default,{id:"_next-ga-init",dangerouslySetInnerHTML:{__html:"\n          window['".concat(n,"'] = window['").concat(n,"'] || [];\n          function gtag(){window['").concat(n,"'].push(arguments);}\n          gtag('js', new Date());\n\n          gtag('config', '").concat(t,"');")}}),(0,r.jsx)(o.default,{id:"_next-ga",src:"https://www.googletagmanager.com/gtag/js?id=".concat(t)})]})},t.sendGAEvent=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];if(void 0===i)return void console.warn("@next/third-parties: GA has not been initialized");window[i]?window[i].push(arguments):console.warn("@next/third-parties: GA dataLayer ".concat(i," does not exist"))}},91728:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.sendGTMEvent=t.GoogleTagManager=void 0;let i=n(54568),r=n(7620),a=function(e){return e&&e.__esModule?e:{default:e}}(n(8139)),o="dataLayer";t.GoogleTagManager=function(e){let{gtmId:t,dataLayerName:n="dataLayer",auth:s,preview:c,dataLayer:l}=e;o=n;let u="dataLayer"!==n?"&l=".concat(n):"";return(0,r.useEffect)(()=>{performance.mark("mark_feature_usage",{detail:{feature:"next-third-parties-gtm"}})},[]),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.default,{id:"_next-gtm-init",dangerouslySetInnerHTML:{__html:"\n      (function(w,l){\n        w[l]=w[l]||[];\n        w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});\n        ".concat(l?"w[l].push(".concat(JSON.stringify(l),")"):"","\n      })(window,'").concat(n,"');")}}),(0,i.jsx)(a.default,{id:"_next-gtm","data-ntpc":"GTM",src:"https://www.googletagmanager.com/gtm.js?id=".concat(t).concat(u).concat(s?"&gtm_auth=".concat(s):"").concat(c?"&gtm_preview=".concat(c,"&gtm_cookies_win=x"):"")})]})},t.sendGTMEvent=(e,t)=>{let n=t||o;window[n]=window[n]||[],window[n].push(e)}}}]);