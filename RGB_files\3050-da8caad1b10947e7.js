!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="51e3e40e-4890-4289-a3a6-b448bc6d07b9",e._sentryDebugIdIdentifier="sentry-dbid-51e3e40e-4890-4289-a3a6-b448bc6d07b9")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3050],{1869:e=>{e.exports=Array.isArray},6035:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorageInstance",{enumerable:!0,get:function(){return n}});let n=(0,r(59113).createAsyncLocalStorage)()},7800:(e,t,r)=>{e.exports="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g},7817:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(43982),o=r(12384),i=r(9126),a=r(59734),s=r(37898),u=r(92310);r(87969),r(7620),r(29775),r(90535),t.IntlError=n.IntlError,t.IntlErrorCode=n.IntlErrorCode,t.createFormatter=n.createFormatter,t.createTranslator=o.createTranslator,t._createCache=i.createCache,t._createIntlFormatters=i.createIntlFormatters,t.initializeConfig=i.initializeConfig,t.IntlProvider=a.IntlProvider,t.useFormatter=s.useFormatter,t.useMessages=s.useMessages,t.useNow=s.useNow,t.useTimeZone=s.useTimeZone,t.useTranslations=s.useTranslations,t.useLocale=u.useLocale},9126:(e,t,r)=>{"use strict";var n=r(29775);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter(Boolean).join(".")}function i(e){return o(e.namespace,e.key)}function a(e){console.error(e)}function s(e,t){return n.memoize(e,{cache:{create:()=>({get:e=>t[e],set(e,r){t[e]=r}})},strategy:n.strategies.variadic})}function u(e,t){return s(function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return new e(...r)},t)}t.createCache=function(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}},t.createIntlFormatters=function(e){return{getDateTimeFormat:u(Intl.DateTimeFormat,e.dateTime),getNumberFormat:u(Intl.NumberFormat,e.number),getPluralRules:u(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:u(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:u(Intl.ListFormat,e.list),getDisplayNames:u(Intl.DisplayNames,e.displayNames)}},t.defaultGetMessageFallback=i,t.defaultOnError=a,t.initializeConfig=function(e){let{getMessageFallback:t,messages:r,onError:n,...o}=e;return{...o,messages:r,onError:n||a,getMessageFallback:t||i}},t.joinPath=o,t.memoFn=s},10951:(e,t,r)=>{"use strict";r.d(t,{r:()=>i});var n=r(7620),o=r(39262);function i(e,t=!1){let r=t?o.o:n.useEffect,a=(0,n.useRef)(e);return r(()=>{a.current=e}),a}},12384:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(43982),o=r(9126);r(87969),r(7620),r(29775),t.IntlError=n.IntlError,t.IntlErrorCode=n.IntlErrorCode,t.createFormatter=n.createFormatter,t._createCache=o.createCache,t._createIntlFormatters=o.createIntlFormatters,t.initializeConfig=o.initializeConfig,t.createTranslator=function(e){let{_cache:t=o.createCache(),_formatters:r=o.createIntlFormatters(t),getMessageFallback:i=o.defaultGetMessageFallback,messages:a,namespace:s,onError:u=o.defaultOnError,...c}=e;return function(e,t){let{messages:r,namespace:o,...i}=e;return r=r["!"],o=n.resolveNamespace(o,"!"),n.createBaseTranslator({...i,messages:r,namespace:o})}({...c,onError:u,cache:t,formatters:r,getMessageFallback:i,messages:{"!":a},namespace:s?"!.".concat(s):"!"},"!")}},14148:(e,t,r)=>{"use strict";r.r(t),r.d(t,{focusable:()=>H,getTabIndex:()=>p,isFocusable:()=>B,isTabbable:()=>C,tabbable:()=>N});var n=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"],o=n.join(","),i="undefined"==typeof Element,a=i?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,s=!i&&Element.prototype.getRootNode?function(e){var t;return null==e||null==(t=e.getRootNode)?void 0:t.call(e)}:function(e){return null==e?void 0:e.ownerDocument},u=function e(t,r){void 0===r&&(r=!0);var n,o=null==t||null==(n=t.getAttribute)?void 0:n.call(t,"inert");return""===o||"true"===o||r&&t&&e(t.parentNode)},c=function(e){var t,r=null==e||null==(t=e.getAttribute)?void 0:t.call(e,"contenteditable");return""===r||"true"===r},l=function(e,t,r){if(u(e))return[];var n=Array.prototype.slice.apply(e.querySelectorAll(o));return t&&a.call(e,o)&&n.unshift(e),n=n.filter(r)},h=function e(t,r,n){for(var i=[],s=Array.from(t);s.length;){var c=s.shift();if(!u(c,!1))if("SLOT"===c.tagName){var l=c.assignedElements(),h=e(l.length?l:c.children,!0,n);n.flatten?i.push.apply(i,h):i.push({scopeParent:c,candidates:h})}else{a.call(c,o)&&n.filter(c)&&(r||!t.includes(c))&&i.push(c);var f=c.shadowRoot||"function"==typeof n.getShadowRoot&&n.getShadowRoot(c),p=!u(f,!1)&&(!n.shadowRootFilter||n.shadowRootFilter(c));if(f&&p){var d=e(!0===f?c.children:f.children,!0,n);n.flatten?i.push.apply(i,d):i.push({scopeParent:c,candidates:d})}else s.unshift.apply(s,c.children)}}return i},f=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},p=function(e){if(!e)throw Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||c(e))&&!f(e)?0:e.tabIndex},d=function(e,t){var r=p(e);return r<0&&t&&!f(e)?0:r},m=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},b=function(e){return"INPUT"===e.tagName},v=function(e,t){for(var r=0;r<e.length;r++)if(e[r].checked&&e[r].form===t)return e[r]},y=function(e){if(!e.name)return!0;var t,r=e.form||s(e),n=function(e){return r.querySelectorAll('input[type="radio"][name="'+e+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)t=n(window.CSS.escape(e.name));else try{t=n(e.name)}catch(e){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",e.message),!1}var o=v(t,e.form);return!o||o===e},g=function(e){return b(e)&&"radio"===e.type&&!y(e)},E=function(e){var t,r,n,o,i,a,u,c=e&&s(e),l=null==(t=c)?void 0:t.host,h=!1;if(c&&c!==e)for(h=!!(null!=(r=l)&&null!=(n=r.ownerDocument)&&n.contains(l)||null!=e&&null!=(o=e.ownerDocument)&&o.contains(e));!h&&l;)h=!!(null!=(a=l=null==(i=c=s(l))?void 0:i.host)&&null!=(u=a.ownerDocument)&&u.contains(l));return h},T=function(e){var t=e.getBoundingClientRect(),r=t.width,n=t.height;return 0===r&&0===n},O=function(e,t){var r=t.displayCheck,n=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;var o=a.call(e,"details>summary:first-of-type")?e.parentElement:e;if(a.call(o,"details:not([open]) *"))return!0;if(r&&"full"!==r&&"legacy-full"!==r){if("non-zero-area"===r)return T(e)}else{if("function"==typeof n){for(var i=e;e;){var u=e.parentElement,c=s(e);if(u&&!u.shadowRoot&&!0===n(u))return T(e);e=e.assignedSlot?e.assignedSlot:u||c===e.ownerDocument?u:c.host}e=i}if(E(e))return!e.getClientRects().length;if("legacy-full"!==r)return!0}return!1},_=function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(var r=0;r<t.children.length;r++){var n=t.children.item(r);if("LEGEND"===n.tagName)return!!a.call(t,"fieldset[disabled] *")||!n.contains(e)}return!0}t=t.parentElement}return!1},S=function(e,t){return!(t.disabled||u(t)||b(t)&&"hidden"===t.type||O(t,e)||"DETAILS"===t.tagName&&Array.prototype.slice.apply(t.children).some(function(e){return"SUMMARY"===e.tagName})||_(t))},P=function(e,t){return!(g(t)||0>p(t))&&!!S(e,t)},A=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!isNaN(t)||!!(t>=0)},I=function e(t){var r=[],n=[];return t.forEach(function(t,o){var i=!!t.scopeParent,a=i?t.scopeParent:t,s=d(a,i),u=i?e(t.candidates):a;0===s?i?r.push.apply(r,u):r.push(a):n.push({documentOrder:o,tabIndex:s,item:t,isScope:i,content:u})}),n.sort(m).reduce(function(e,t){return t.isScope?e.push.apply(e,t.content):e.push(t.content),e},[]).concat(r)},N=function(e,t){var r;return I((t=t||{}).getShadowRoot?h([e],t.includeContainer,{filter:P.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:A}):l(e,t.includeContainer,P.bind(null,t)))},H=function(e,t){var r;return(t=t||{}).getShadowRoot?h([e],t.includeContainer,{filter:S.bind(null,t),flatten:!0,getShadowRoot:t.getShadowRoot}):l(e,t.includeContainer,S.bind(null,t))},C=function(e,t){if(t=t||{},!e)throw Error("No node provided");return!1!==a.call(e,o)&&P(t,e)},w=n.concat("iframe").join(","),B=function(e,t){if(t=t||{},!e)throw Error("No node provided");return!1!==a.call(e,w)&&S(t,e)}},15345:(e,t,r)=>{"use strict";r.d(t,{d:()=>s});var n=r(70095),o=r.n(n),i=r(7620),a=r(10951);function s(e,t,r){var n;let s=(0,i.useCallback)(e=>o()(e,t,r),[t,r]),u=(0,a.r)(e),c=(0,i.useRef)(s(function(...e){var t;null==(t=u.current)||t.call(u,...e)}));return n=()=>{var e;return null==(e=c.current)?void 0:e.cancel()},(0,i.useEffect)(()=>n,[]),c.current}},15904:e=>{e.exports=function(e){return null!=e&&"object"==typeof e}},23027:(e,t,r)=>{"use strict";r.d(t,{Eq:()=>l});var n=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},s=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,r,n){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=u(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[r]||(a[r]=new WeakMap);var l=a[r],h=[],f=new Set,p=new Set(c),d=function(e){!e||f.has(e)||(f.add(e),d(e.parentNode))};c.forEach(d);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))m(e);else try{var t=e.getAttribute(n),a=null!==t&&"false"!==t,s=(o.get(e)||0)+1,u=(l.get(e)||0)+1;o.set(e,s),l.set(e,u),h.push(e),1===s&&a&&i.set(e,!0),1===u&&e.setAttribute(r,"true"),a||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),f.clear(),s++,function(){h.forEach(function(e){var t=o.get(e)-1,a=l.get(e)-1;o.set(e,t),l.set(e,a),t||(i.has(e)||e.removeAttribute(n),i.delete(e)),a||e.removeAttribute(r)}),--s||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},l=function(e,t,r){void 0===r&&(r="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||n(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),c(o,i,r,"aria-hidden")):function(){return null}}},26816:(e,t,r)=>{"use strict";e.exports=r(59734)},28769:(e,t,r)=>{var n=r(68445),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,s=n?n.toStringTag:void 0;e.exports=function(e){var t=i.call(e,s),r=e[s];try{e[s]=void 0;var n=!0}catch(e){}var o=a.call(e);return n&&(t?e[s]=r:delete e[s]),o}},29775:(e,t,r)=>{"use strict";function n(e,t){var r=t&&t.cache?t.cache:u,n=t&&t.serializer?t.serializer:a;return(t&&t.strategy?t.strategy:function(e,t){var r,n,a=1===e.length?o:i;return r=t.cache.create(),n=t.serializer,a.bind(this,e,r,n)})(e,{cache:r,serializer:n})}function o(e,t,r,n){var o=null==n||"number"==typeof n||"boolean"==typeof n?n:r(n),i=t.get(o);return void 0===i&&(i=e.call(this,n),t.set(o,i)),i}function i(e,t,r){var n=Array.prototype.slice.call(arguments,3),o=r(n),i=t.get(o);return void 0===i&&(i=e.apply(this,n),t.set(o,i)),i}r.r(t),r.d(t,{memoize:()=>n,strategies:()=>c});var a=function(){return JSON.stringify(arguments)},s=function(){function e(){this.cache=Object.create(null)}return e.prototype.get=function(e){return this.cache[e]},e.prototype.set=function(e,t){this.cache[e]=t},e}(),u={create:function(){return new s}},c={variadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,i.bind(this,e,r,n)},monadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,o.bind(this,e,r,n)}}},31543:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(88903);function o(e,t){return function(){try{return t(...arguments)}catch(e){throw Error(void 0)}}}let i=o(0,n.useTranslations);t.useFormatter=o(0,n.useFormatter),t.useTranslations=i,Object.keys(n).forEach(function(e){"default"===e||Object.prototype.hasOwnProperty.call(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}})})},35416:(e,t,r)=>{"use strict";var n=r(46699);function o(){}function i(){}i.resetWarningCache=o,e.exports=function(){function e(e,t,r,o,i,a){if(a!==n){var s=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},37684:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let n=r(21510)._(r(92644));function o(e,t){var r;let o={};"function"==typeof e&&(o.loader=e);let i={...o,...t};return(0,n.default)({...i,modules:null==(r=i.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},37898:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(59734),o=r(92310),i=r(7620),a=r(43982);r(9126),r(29775),r(90535),r(87969);let s=!1,u="undefined"==typeof window;t.IntlProvider=n.IntlProvider,t.useLocale=o.useLocale,t.useFormatter=function(){let{formats:e,formatters:t,locale:r,now:n,onError:s,timeZone:u}=o.useIntlContext();return i.useMemo(()=>a.createFormatter({formats:e,locale:r,now:n,onError:s,timeZone:u,_formatters:t}),[e,t,n,r,s,u])},t.useMessages=function(){let e=o.useIntlContext();if(!e.messages)throw Error(void 0);return e.messages},t.useNow=function(e){let t=null==e?void 0:e.updateInterval,{now:r}=o.useIntlContext(),[n,a]=i.useState(r||new Date);return i.useEffect(()=>{if(!t)return;let e=setInterval(()=>{a(new Date)},t);return()=>{clearInterval(e)}},[r,t]),null==t&&r?r:n},t.useTimeZone=function(){return o.useIntlContext().timeZone},t.useTranslations=function(e){return function(e,t,r){let{cache:n,defaultTranslationValues:c,formats:l,formatters:h,getMessageFallback:f,locale:p,onError:d,timeZone:m}=o.useIntlContext(),b=e["!"],v=a.resolveNamespace(t,"!");return m||s||!u||(s=!0,d(new a.IntlError(a.IntlErrorCode.ENVIRONMENT_FALLBACK,void 0))),i.useMemo(()=>a.createBaseTranslator({cache:n,formatters:h,getMessageFallback:f,messages:b,defaultTranslationValues:c,namespace:v,onError:d,formats:l,locale:p,timeZone:m}),[n,h,f,b,c,v,d,l,p,m])}({"!":o.useIntlContext().messages},e?"!.".concat(e):"!","!")}},39262:(e,t,r)=>{"use strict";r.d(t,{o:()=>o});var n=r(7620);let o="undefined"==typeof window?n.useEffect:n.useLayoutEffect},43982:(e,t,r)=>{"use strict";var n=r(87969),o=r(7620),i=r(9126),a=function(e){return e&&e.__esModule?e:{default:e}}(n);function s(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}let u=function(e){return e.MISSING_MESSAGE="MISSING_MESSAGE",e.MISSING_FORMAT="MISSING_FORMAT",e.ENVIRONMENT_FALLBACK="ENVIRONMENT_FALLBACK",e.INSUFFICIENT_PATH="INSUFFICIENT_PATH",e.INVALID_MESSAGE="INVALID_MESSAGE",e.INVALID_KEY="INVALID_KEY",e.FORMATTING_ERROR="FORMATTING_ERROR",e}({});class c extends Error{constructor(e,t){let r=e;t&&(r+=": "+t),super(r),s(this,"code",void 0),s(this,"originalMessage",void 0),this.code=e,t&&(this.originalMessage=t)}}function l(e,t){return e?Object.keys(e).reduce((r,n)=>(r[n]={timeZone:t,...e[n]},r),{}):e}function h(e,t,r,n){let o=i.joinPath(n,r);if(!t)throw Error(o);let a=t;return r.split(".").forEach(t=>{let r=a[t];if(null==t||null==r)throw Error(o+" (".concat(e,")"));a=r}),a}let f=365/12*86400,p={second:1,seconds:1,minute:60,minutes:60,hour:3600,hours:3600,day:86400,days:86400,week:604800,weeks:604800,month:365/12*86400,months:365/12*86400,quarter:365/12*259200,quarters:365/12*259200,year:31536e3,years:31536e3};t.IntlError=c,t.IntlErrorCode=u,t.createBaseTranslator=function(e){let t=function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:i.defaultOnError;try{if(!t)throw Error(void 0);let n=r?h(e,t,r):t;if(!n)throw Error(r);return n}catch(t){let e=new c(u.MISSING_MESSAGE,t.message);return n(e),e}}(e.locale,e.messages,e.namespace,e.onError);return function(e){let{cache:t,defaultTranslationValues:r,formats:n,formatters:s,getMessageFallback:f=i.defaultGetMessageFallback,locale:p,messagesOrError:d,namespace:m,onError:b,timeZone:v}=e,y=d instanceof c;function g(e,t,r){let n=new c(t,r);return b(n),f({error:n,key:e,namespace:m})}function E(e,c,b){let E,T;if(y)return f({error:d,key:e,namespace:m});try{E=h(p,d,e,m)}catch(t){return g(e,u.MISSING_MESSAGE,t.message)}if("object"==typeof E){let t;return g(e,Array.isArray(E)?u.INVALID_MESSAGE:u.INSUFFICIENT_PATH,t)}let O=function(e,t){if(t)return;let r=e.replace(/'([{}])/gi,"$1");return/<|{/.test(r)?void 0:r}(E,c);if(O)return O;s.getMessageFormat||(s.getMessageFormat=i.memoFn(function(){return new a.default(arguments.length<=0?void 0:arguments[0],arguments.length<=1?void 0:arguments[1],arguments.length<=2?void 0:arguments[2],{formatters:s,...arguments.length<=3?void 0:arguments[3]})},t.message));try{T=s.getMessageFormat(E,p,function(e,t){let r=t?{...e,dateTime:l(e.dateTime,t)}:e,n=a.default.formats.date,o=t?l(n,t):n,i=a.default.formats.time,s=t?l(i,t):i;return{...r,date:{...o,...r.dateTime},time:{...s,...r.dateTime}}}({...n,...b},v),{formatters:{...s,getDateTimeFormat:(e,t)=>s.getDateTimeFormat(e,{timeZone:v,...t})}})}catch(t){return g(e,u.INVALID_MESSAGE,t.message)}try{let e=T.format(function(e){if(0===Object.keys(e).length)return;let t={};return Object.keys(e).forEach(r=>{let n,i=0,a=e[r];n="function"==typeof a?e=>{let t=a(e);return o.isValidElement(t)?o.cloneElement(t,{key:r+i++}):t}:a,t[r]=n}),t}({...r,...c}));if(null==e)throw Error(void 0);return o.isValidElement(e)||Array.isArray(e)||"string"==typeof e?e:String(e)}catch(t){return g(e,u.FORMATTING_ERROR,t.message)}}function T(e,t,r){let n=E(e,t,r);return"string"!=typeof n?g(e,u.INVALID_MESSAGE,void 0):n}return T.rich=E,T.markup=(e,t,r)=>{let n=E(e,t,r);if("string"!=typeof n){let t=new c(u.FORMATTING_ERROR,void 0);return b(t),f({error:t,key:e,namespace:m})}return n},T.raw=e=>{if(y)return f({error:d,key:e,namespace:m});try{return h(p,d,e,m)}catch(t){return g(e,u.MISSING_MESSAGE,t.message)}},T.has=e=>{if(y)return!1;try{return h(p,d,e,m),!0}catch(e){return!1}},T}({...e,messagesOrError:t})},t.createFormatter=function(e){let{_cache:t=i.createCache(),_formatters:r=i.createIntlFormatters(t),formats:n,locale:o,now:a,onError:s=i.defaultOnError,timeZone:l}=e;function h(e){var t;return null!=(t=e)&&t.timeZone||(l?e={...e,timeZone:l}:s(new c(u.ENVIRONMENT_FALLBACK,void 0))),e}function d(e,t,r,n){let o;try{o=function(e,t){let r;if("string"==typeof t){if(!(r=null==e?void 0:e[t])){let e=new c(u.MISSING_FORMAT,void 0);throw s(e),e}}else r=t;return r}(t,e)}catch(e){return n()}try{return r(o)}catch(e){return s(new c(u.FORMATTING_ERROR,e.message)),n()}}function m(e,t){return d(t,null==n?void 0:n.dateTime,t=>(t=h(t),r.getDateTimeFormat(o,t).format(e)),()=>String(e))}function b(){return a||(s(new c(u.ENVIRONMENT_FALLBACK,void 0)),new Date)}return{dateTime:m,number:function(e,t){return d(t,null==n?void 0:n.number,t=>r.getNumberFormat(o,t).format(e),()=>String(e))},relativeTime:function(e,t){try{var n;let i,a,s={};t instanceof Date||"number"==typeof t?i=new Date(t):t&&(i=null!=t.now?new Date(t.now):b(),a=t.unit,s.style=t.style,s.numberingSystem=t.numberingSystem),i||(i=b());let u=(new Date(e).getTime()-i.getTime())/1e3;a||(a=function(e){let t=Math.abs(e);return t<60?"second":t<3600?"minute":t<86400?"hour":t<604800?"day":t<f?"week":t<31536e3?"month":"year"}(u)),s.numeric="second"===a?"auto":"always";let c=(n=a,Math.round(u/p[n]));return r.getRelativeTimeFormat(o,s).format(c,a)}catch(t){return s(new c(u.FORMATTING_ERROR,t.message)),String(e)}},list:function(e,t){let i=[],a=new Map,s=0;for(let t of e){let e;"object"==typeof t?(e=String(s),a.set(e,t)):e=String(t),i.push(e),s++}return d(t,null==n?void 0:n.list,e=>{let t=r.getListFormat(o,e).formatToParts(i).map(e=>"literal"===e.type?e.value:a.get(e.value)||e.value);return a.size>0?t:t.join("")},()=>String(e))},dateTimeRange:function(e,t,i){return d(i,null==n?void 0:n.dateTime,n=>(n=h(n),r.getDateTimeFormat(o,n).formatRange(e,t)),()=>[m(e),m(t)].join(" – "))}}},t.resolveNamespace=function(e,t){return e===t?void 0:e.slice((t+".").length)}},46524:(e,t,r)=>{e.exports=r(35416)()},46699:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},49669:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorage",{enumerable:!0,get:function(){return n.workAsyncStorageInstance}});let n=r(6035)},50377:(e,t,r)=>{"use strict";r.r(t),r.d(t,{createFocusTrap:()=>m});var n=r(14148);function o(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,o;n=t,o=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var s={activateTrap:function(e,t){if(e.length>0){var r=e[e.length-1];r!==t&&r._setPausedState(!0)}var n=e.indexOf(t);-1===n||e.splice(n,1),e.push(t)},deactivateTrap:function(e,t){var r=e.indexOf(t);-1!==r&&e.splice(r,1),e.length>0&&!e[e.length-1]._isManuallyPaused()&&e[e.length-1]._setPausedState(!1)}},u=function(e){return(null==e?void 0:e.key)==="Tab"||(null==e?void 0:e.keyCode)===9},c=function(e){return u(e)&&!e.shiftKey},l=function(e){return u(e)&&e.shiftKey},h=function(e){return setTimeout(e,0)},f=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return"function"==typeof e?e.apply(void 0,r):e},p=function(e){return e.target.shadowRoot&&"function"==typeof e.composedPath?e.composedPath()[0]:e.target},d=[],m=function(e,t){var r,i=(null==t?void 0:t.document)||document,m=(null==t?void 0:t.trapStack)||d,b=a({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0,isKeyForward:c,isKeyBackward:l},t),v={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,manuallyPaused:!1,delayInitialFocusTimer:void 0,recentNavEvent:void 0},y=function(e,t,r){return e&&void 0!==e[t]?e[t]:b[r||t]},g=function(e,t){var r="function"==typeof(null==t?void 0:t.composedPath)?t.composedPath():void 0;return v.containerGroups.findIndex(function(t){var n=t.container,o=t.tabbableNodes;return n.contains(e)||(null==r?void 0:r.includes(n))||o.find(function(t){return t===e})})},E=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.hasFallback,a=r.params,s=b[e];if("function"==typeof s&&(s=s.apply(void 0,function(e){if(Array.isArray(e))return o(e)}(t=void 0===a?[]:a)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||function(e,t){if(e){if("string"==typeof e)return o(e,void 0);var r=({}).toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?o(e,t):void 0}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())),!0===s&&(s=void 0),!s){if(void 0===s||!1===s)return s;throw Error("`".concat(e,"` was specified but was not a node, or did not return a node"))}var u=s;if("string"==typeof s){try{u=i.querySelector(s)}catch(t){throw Error("`".concat(e,'` appears to be an invalid selector; error="').concat(t.message,'"'))}if(!u&&!(void 0!==n&&n))throw Error("`".concat(e,"` as selector refers to no known node"))}return u},T=function(){var e=E("initialFocus",{hasFallback:!0});if(!1===e)return!1;if(void 0===e||e&&!(0,n.isFocusable)(e,b.tabbableOptions))if(g(i.activeElement)>=0)e=i.activeElement;else{var t=v.tabbableGroups[0];e=t&&t.firstTabbableNode||E("fallbackFocus")}else null===e&&(e=E("fallbackFocus"));if(!e)throw Error("Your focus-trap needs to have at least one focusable element");return e},O=function(){if(v.containerGroups=v.containers.map(function(e){var t=(0,n.tabbable)(e,b.tabbableOptions),r=(0,n.focusable)(e,b.tabbableOptions),o=t.length>0?t[0]:void 0,i=t.length>0?t[t.length-1]:void 0,a=r.find(function(e){return(0,n.isTabbable)(e)}),s=r.slice().reverse().find(function(e){return(0,n.isTabbable)(e)}),u=!!t.find(function(e){return(0,n.getTabIndex)(e)>0});return{container:e,tabbableNodes:t,focusableNodes:r,posTabIndexesFound:u,firstTabbableNode:o,lastTabbableNode:i,firstDomTabbableNode:a,lastDomTabbableNode:s,nextTabbableNode:function(e){var o=!(arguments.length>1)||void 0===arguments[1]||arguments[1],i=t.indexOf(e);return i<0?o?r.slice(r.indexOf(e)+1).find(function(e){return(0,n.isTabbable)(e)}):r.slice(0,r.indexOf(e)).reverse().find(function(e){return(0,n.isTabbable)(e)}):t[i+(o?1:-1)]}}}),v.tabbableGroups=v.containerGroups.filter(function(e){return e.tabbableNodes.length>0}),v.tabbableGroups.length<=0&&!E("fallbackFocus"))throw Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times");if(v.containerGroups.find(function(e){return e.posTabIndexesFound})&&v.containerGroups.length>1)throw Error("At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.")},_=function(e){var t=e.activeElement;if(t)return t.shadowRoot&&null!==t.shadowRoot.activeElement?_(t.shadowRoot):t},S=function(e){if(!1!==e&&e!==_(document)){if(!e||!e.focus)return void S(T());e.focus({preventScroll:!!b.preventScroll}),v.mostRecentlyFocusedNode=e,e.tagName&&"input"===e.tagName.toLowerCase()&&"function"==typeof e.select&&e.select()}},P=function(e){var t=E("setReturnFocus",{params:[e]});return t||!1!==t&&e},A=function(e){var t=e.target,r=e.event,o=e.isBackward,i=void 0!==o&&o;t=t||p(r),O();var a=null;if(v.tabbableGroups.length>0){var s=g(t,r),c=s>=0?v.containerGroups[s]:void 0;if(s<0)a=i?v.tabbableGroups[v.tabbableGroups.length-1].lastTabbableNode:v.tabbableGroups[0].firstTabbableNode;else if(i){var l=v.tabbableGroups.findIndex(function(e){var r=e.firstTabbableNode;return t===r});if(l<0&&(c.container===t||(0,n.isFocusable)(t,b.tabbableOptions)&&!(0,n.isTabbable)(t,b.tabbableOptions)&&!c.nextTabbableNode(t,!1))&&(l=s),l>=0){var h=0===l?v.tabbableGroups.length-1:l-1,f=v.tabbableGroups[h];a=(0,n.getTabIndex)(t)>=0?f.lastTabbableNode:f.lastDomTabbableNode}else u(r)||(a=c.nextTabbableNode(t,!1))}else{var d=v.tabbableGroups.findIndex(function(e){var r=e.lastTabbableNode;return t===r});if(d<0&&(c.container===t||(0,n.isFocusable)(t,b.tabbableOptions)&&!(0,n.isTabbable)(t,b.tabbableOptions)&&!c.nextTabbableNode(t))&&(d=s),d>=0){var m=d===v.tabbableGroups.length-1?0:d+1,y=v.tabbableGroups[m];a=(0,n.getTabIndex)(t)>=0?y.firstTabbableNode:y.firstDomTabbableNode}else u(r)||(a=c.nextTabbableNode(t))}}else a=E("fallbackFocus");return a},I=function(e){if(!(g(p(e),e)>=0)){if(f(b.clickOutsideDeactivates,e))return void r.deactivate({returnFocus:b.returnFocusOnDeactivate});f(b.allowOutsideClick,e)||e.preventDefault()}},N=function(e){var t=p(e),r=g(t,e)>=0;if(r||t instanceof Document)r&&(v.mostRecentlyFocusedNode=t);else{e.stopImmediatePropagation();var o,i=!0;if(v.mostRecentlyFocusedNode)if((0,n.getTabIndex)(v.mostRecentlyFocusedNode)>0){var a=g(v.mostRecentlyFocusedNode),s=v.containerGroups[a].tabbableNodes;if(s.length>0){var u=s.findIndex(function(e){return e===v.mostRecentlyFocusedNode});u>=0&&(b.isKeyForward(v.recentNavEvent)?u+1<s.length&&(o=s[u+1],i=!1):u-1>=0&&(o=s[u-1],i=!1))}}else v.containerGroups.some(function(e){return e.tabbableNodes.some(function(e){return(0,n.getTabIndex)(e)>0})})||(i=!1);else i=!1;i&&(o=A({target:v.mostRecentlyFocusedNode,isBackward:b.isKeyBackward(v.recentNavEvent)})),o?S(o):S(v.mostRecentlyFocusedNode||T())}v.recentNavEvent=void 0},H=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];v.recentNavEvent=e;var r=A({event:e,isBackward:t});r&&(u(e)&&e.preventDefault(),S(r))},C=function(e){(b.isKeyForward(e)||b.isKeyBackward(e))&&H(e,b.isKeyBackward(e))},w=function(e){((null==e?void 0:e.key)==="Escape"||(null==e?void 0:e.key)==="Esc"||(null==e?void 0:e.keyCode)===27)&&!1!==f(b.escapeDeactivates,e)&&(e.preventDefault(),r.deactivate())},B=function(e){!(g(p(e),e)>=0||f(b.clickOutsideDeactivates,e))&&(f(b.allowOutsideClick,e)||(e.preventDefault(),e.stopImmediatePropagation()))},R=function(){if(v.active)return s.activateTrap(m,r),v.delayInitialFocusTimer=b.delayInitialFocus?h(function(){S(T())}):S(T()),i.addEventListener("focusin",N,!0),i.addEventListener("mousedown",I,{capture:!0,passive:!1}),i.addEventListener("touchstart",I,{capture:!0,passive:!1}),i.addEventListener("click",B,{capture:!0,passive:!1}),i.addEventListener("keydown",C,{capture:!0,passive:!1}),i.addEventListener("keydown",w),r},L=function(){if(v.active)return i.removeEventListener("focusin",N,!0),i.removeEventListener("mousedown",I,!0),i.removeEventListener("touchstart",I,!0),i.removeEventListener("click",B,!0),i.removeEventListener("keydown",C,!0),i.removeEventListener("keydown",w),r},F="undefined"!=typeof window&&"MutationObserver"in window?new MutationObserver(function(e){e.some(function(e){return Array.from(e.removedNodes).some(function(e){return e===v.mostRecentlyFocusedNode})})&&S(T())}):void 0,M=function(){F&&(F.disconnect(),v.active&&!v.paused&&v.containers.map(function(e){F.observe(e,{subtree:!0,childList:!0})}))};return Object.defineProperties(r={get active(){return v.active},get paused(){return v.paused},activate:function(e){if(v.active)return this;var t=y(e,"onActivate"),r=y(e,"onPostActivate"),n=y(e,"checkCanFocusTrap");n||O(),v.active=!0,v.paused=!1,v.nodeFocusedBeforeActivation=_(i),null==t||t();var o=function(){n&&O(),R(),M(),null==r||r()};return n?n(v.containers.concat()).then(o,o):o(),this},deactivate:function(e){if(!v.active)return this;var t=a({onDeactivate:b.onDeactivate,onPostDeactivate:b.onPostDeactivate,checkCanReturnFocus:b.checkCanReturnFocus},e);clearTimeout(v.delayInitialFocusTimer),v.delayInitialFocusTimer=void 0,L(),v.active=!1,v.paused=!1,M(),s.deactivateTrap(m,r);var n=y(t,"onDeactivate"),o=y(t,"onPostDeactivate"),i=y(t,"checkCanReturnFocus"),u=y(t,"returnFocus","returnFocusOnDeactivate");null==n||n();var c=function(){h(function(){u&&S(P(v.nodeFocusedBeforeActivation)),null==o||o()})};return u&&i?i(P(v.nodeFocusedBeforeActivation)).then(c,c):c(),this},pause:function(e){return v.active?(v.manuallyPaused=!0,this._setPausedState(!0,e)):this},unpause:function(e){return v.active?(v.manuallyPaused=!1,m[m.length-1]!==this)?this:this._setPausedState(!1,e):this},updateContainerElements:function(e){return v.containers=[].concat(e).filter(Boolean).map(function(e){return"string"==typeof e?i.querySelector(e):e}),v.active&&O(),M(),this}},{_isManuallyPaused:{value:function(){return v.manuallyPaused}},_setPausedState:{value:function(e,t){if(v.paused===e)return this;if(v.paused=e,e){var r=y(t,"onPause"),n=y(t,"onPostPause");null==r||r(),L(),M(),null==n||n()}else{var o=y(t,"onUnpause"),i=y(t,"onPostUnpause");null==o||o(),O(),R(),M(),null==i||i()}return this}}}),r.updateContainerElements(e),r}},52846:e=>{var t=Object.prototype.toString;e.exports=function(e){return t.call(e)}},55701:(e,t,r)=>{var n=r(88480),o=r(1869),i=r(15904);e.exports=function(e){return"string"==typeof e||!o(e)&&i(e)&&"[object String]"==n(e)}},59113:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{bindSnapshot:function(){return a},createAsyncLocalStorage:function(){return i},createSnapshot:function(){return s}});let r=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class n{disable(){throw r}getStore(){}run(){throw r}exit(){throw r}enterWith(){throw r}static bind(e){return e}}let o="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function i(){return o?new o:new n}function a(e){return o?o.bind(e):n.bind(e)}function s(){return o?o.snapshot():function(e,...t){return e(...t)}}},59734:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(7620),o=r(9126),i=r(90535);r(29775);var a=function(e){return e&&e.__esModule?e:{default:e}}(n);t.IntlProvider=function(e){let{children:t,defaultTranslationValues:r,formats:s,getMessageFallback:u,locale:c,messages:l,now:h,onError:f,timeZone:p}=e,d=n.useMemo(()=>o.createCache(),[c]),m=n.useMemo(()=>o.createIntlFormatters(d),[d]),b=n.useMemo(()=>({...o.initializeConfig({locale:c,defaultTranslationValues:r,formats:s,getMessageFallback:u,messages:l,now:h,onError:f,timeZone:p}),formatters:m,cache:d}),[d,r,s,m,u,c,l,h,f,p]);return a.default.createElement(i.IntlContext.Provider,{value:b},t)}},60931:(e,t,r)=>{"use strict";function n(e){let{reason:t,children:r}=e;return r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return n}}),r(13159)},67365:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(18927),o=r(7620),i=r(26816),a=function(e){return e&&e.__esModule?e:{default:e}}(o);t.default=function(e){let{locale:t,...r}=e;if(!t)throw Error(void 0);return a.default.createElement(i.IntlProvider,n.extends({locale:t},r))}},68445:(e,t,r)=>{e.exports=r(71809).Symbol},70095:(e,t,r)=>{var n=0/0,o=/^\s+|\s+$/g,i=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,s=/^0o[0-7]+$/i,u=parseInt,c="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g,l="object"==typeof self&&self&&self.Object===Object&&self,h=c||l||Function("return this")(),f=Object.prototype.toString,p=Math.max,d=Math.min,m=function(){return h.Date.now()};function b(e){var t=typeof e;return!!e&&("object"==t||"function"==t)}function v(e){if("number"==typeof e)return e;if("symbol"==typeof(t=e)||t&&"object"==typeof t&&"[object Symbol]"==f.call(t))return n;if(b(e)){var t,r="function"==typeof e.valueOf?e.valueOf():e;e=b(r)?r+"":r}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(o,"");var c=a.test(e);return c||s.test(e)?u(e.slice(2),c?2:8):i.test(e)?n:+e}e.exports=function(e,t,r){var n,o,i,a,s,u,c=0,l=!1,h=!1,f=!0;if("function"!=typeof e)throw TypeError("Expected a function");function y(t){var r=n,i=o;return n=o=void 0,c=t,a=e.apply(i,r)}function g(e){var r=e-u,n=e-c;return void 0===u||r>=t||r<0||h&&n>=i}function E(){var e,r,n,o=m();if(g(o))return T(o);s=setTimeout(E,(e=o-u,r=o-c,n=t-e,h?d(n,i-r):n))}function T(e){return(s=void 0,f&&n)?y(e):(n=o=void 0,a)}function O(){var e,r=m(),i=g(r);if(n=arguments,o=this,u=r,i){if(void 0===s)return c=e=u,s=setTimeout(E,t),l?y(e):a;if(h)return s=setTimeout(E,t),y(u)}return void 0===s&&(s=setTimeout(E,t)),a}return t=v(t)||0,b(r)&&(l=!!r.leading,i=(h="maxWait"in r)?p(v(r.maxWait)||0,t):i,f="trailing"in r?!!r.trailing:f),O.cancel=function(){void 0!==s&&clearTimeout(s),c=0,n=u=o=s=void 0},O.flush=function(){return void 0===s?a:T(m())},O}},71096:(e,t,r)=>{"use strict";r.d(t,{AQ:()=>p,C6:()=>o,Cl:()=>i,Ju:()=>c,N3:()=>f,Tt:()=>a,YH:()=>u,fX:()=>h,sH:()=>s,xN:()=>d,zs:()=>l});var n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function o(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var i=function(){return(i=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function a(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}function s(e,t,r,n){return new(r||(r=Promise))(function(o,i){function a(e){try{u(n.next(e))}catch(e){i(e)}}function s(e){try{u(n.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,s)}u((n=n.apply(e,t||[])).next())})}function u(e,t){var r,n,o,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},a=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return a.next=s(0),a.throw=s(1),a.return=s(2),"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(s){return function(u){var c=[s,u];if(r)throw TypeError("Generator is already executing.");for(;a&&(a=0,c[0]&&(i=0)),i;)try{if(r=1,n&&(o=2&c[0]?n.return:c[0]?n.throw||((o=n.return)&&o.call(n),0):n.next)&&!(o=o.call(n,c[1])).done)return o;switch(n=0,o&&(c=[2&c[0],o.value]),c[0]){case 0:case 1:o=c;break;case 4:return i.label++,{value:c[1],done:!1};case 5:i.label++,n=c[1],c=[0];continue;case 7:c=i.ops.pop(),i.trys.pop();continue;default:if(!(o=(o=i.trys).length>0&&o[o.length-1])&&(6===c[0]||2===c[0])){i=0;continue}if(3===c[0]&&(!o||c[1]>o[0]&&c[1]<o[3])){i.label=c[1];break}if(6===c[0]&&i.label<o[1]){i.label=o[1],o=c;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(c);break}o[2]&&i.ops.pop(),i.trys.pop();continue}c=t.call(e,i)}catch(e){c=[6,e],n=0}finally{r=o=0}if(5&c[0])throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}}}function c(e){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&e[t],n=0;if(r)return r.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function l(e,t){var r="function"==typeof Symbol&&e[Symbol.iterator];if(!r)return e;var n,o,i=r.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(n=i.next()).done;)a.push(n.value)}catch(e){o={error:e}}finally{try{n&&!n.done&&(r=i.return)&&r.call(i)}finally{if(o)throw o.error}}return a}function h(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}function f(e){return this instanceof f?(this.v=e,this):new f(e)}function p(e,t,r){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var n,o=r.apply(e,t||[]),i=[];return n=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",function(e){return function(t){return Promise.resolve(t).then(e,c)}}),n[Symbol.asyncIterator]=function(){return this},n;function a(e,t){o[e]&&(n[e]=function(t){return new Promise(function(r,n){i.push([e,t,r,n])>1||s(e,t)})},t&&(n[e]=t(n[e])))}function s(e,t){try{var r;(r=o[e](t)).value instanceof f?Promise.resolve(r.value.v).then(u,c):l(i[0][2],r)}catch(e){l(i[0][3],e)}}function u(e){s("next",e)}function c(e){s("throw",e)}function l(e,t){e(t),i.shift(),i.length&&s(i[0][0],i[0][1])}}function d(e){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var t,r=e[Symbol.asyncIterator];return r?r.call(e):(e=c(e),t={},n("next"),n("throw"),n("return"),t[Symbol.asyncIterator]=function(){return this},t);function n(r){t[r]=e[r]&&function(t){return new Promise(function(n,o){var i,a,s;i=n,a=o,s=(t=e[r](t)).done,Promise.resolve(t.value).then(function(e){i({value:e,done:s})},a)})}}}Object.create,Object.create,"function"==typeof SuppressedError&&SuppressedError},71809:(e,t,r)=>{var n=r(7800),o="object"==typeof self&&self&&self.Object===Object&&self;e.exports=n||o||Function("return this")()},78684:(e,t,r)=>{"use strict";function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(o=function(){return!!e})()}function i(e){return(i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function a(e,t){return(a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function s(e){var t=function(e,t){if("object"!=n(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var o=r.call(e,t||"default");if("object"!=n(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==n(t)?t:t+""}var u=r(7620),c=r(46524),l=r(50377).createFocusTrap,h=r(14148).isFocusable,f=function(e){var t;function r(e){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");t=r,a=[e],t=i(t),u=h=function(e,t){if(t&&("object"==n(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");var r=e;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,o()?Reflect.construct(t,a||[],i(this).constructor):t.apply(this,a)),c="getNodeForOption",l=function(e){var t,r,n=null!=(t=this.internalOptions[e])?t:this.originalOptions[e];if("function"==typeof n){for(var o=arguments.length,i=Array(o>1?o-1:0),a=1;a<o;a++)i[a-1]=arguments[a];n=n.apply(void 0,i)}if(!0===n&&(n=void 0),!n){if(void 0===n||!1===n)return n;throw Error("`".concat(e,"` was specified but was not a node, or did not return a node"))}var s=n;if("string"==typeof n&&!(s=null==(r=this.getDocument())?void 0:r.querySelector(n)))throw Error("`".concat(e,"` as selector refers to no known node"));return s},(c=s(c))in u?Object.defineProperty(u,c,{value:l,enumerable:!0,configurable:!0,writable:!0}):u[c]=l,h.handleDeactivate=h.handleDeactivate.bind(h),h.handlePostDeactivate=h.handlePostDeactivate.bind(h),h.handleClickOutsideDeactivates=h.handleClickOutsideDeactivates.bind(h),h.internalOptions={returnFocusOnDeactivate:!1,checkCanReturnFocus:null,onDeactivate:h.handleDeactivate,onPostDeactivate:h.handlePostDeactivate,clickOutsideDeactivates:h.handleClickOutsideDeactivates},h.originalOptions={returnFocusOnDeactivate:!0,onDeactivate:null,onPostDeactivate:null,checkCanReturnFocus:null,clickOutsideDeactivates:!1};var t,a,u,c,l,h,f=e.focusTrapOptions;for(var p in f)if(Object.prototype.hasOwnProperty.call(f,p)){if("returnFocusOnDeactivate"===p||"onDeactivate"===p||"onPostDeactivate"===p||"checkCanReturnFocus"===p||"clickOutsideDeactivates"===p){h.originalOptions[p]=f[p];continue}h.internalOptions[p]=f[p]}return h.outsideClick=null,h.focusTrapElements=e.containerElements||[],h.updatePreviousElement(),h}if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&a(r,e),t=[{key:"getDocument",value:function(){return this.props.focusTrapOptions.document||("undefined"!=typeof document?document:void 0)}},{key:"getReturnFocusNode",value:function(){var e=this.getNodeForOption("setReturnFocus",this.previouslyFocusedElement);return e||!1!==e&&this.previouslyFocusedElement}},{key:"updatePreviousElement",value:function(){var e=this.getDocument();e&&(this.previouslyFocusedElement=e.activeElement)}},{key:"deactivateTrap",value:function(){this.focusTrap&&this.focusTrap.active&&this.focusTrap.deactivate({returnFocus:!1,checkCanReturnFocus:null,onDeactivate:this.originalOptions.onDeactivate})}},{key:"handleClickOutsideDeactivates",value:function(e){var t="function"==typeof this.originalOptions.clickOutsideDeactivates?this.originalOptions.clickOutsideDeactivates.call(null,e):this.originalOptions.clickOutsideDeactivates;return t&&(this.outsideClick={target:e.target,allowDeactivation:t}),t}},{key:"handleDeactivate",value:function(){this.originalOptions.onDeactivate&&this.originalOptions.onDeactivate.call(null),this.deactivateTrap()}},{key:"handlePostDeactivate",value:function(){var e=this,t=function(){var t=e.getReturnFocusNode(),r=!!(e.originalOptions.returnFocusOnDeactivate&&null!=t&&t.focus&&(!e.outsideClick||e.outsideClick.allowDeactivation&&!h(e.outsideClick.target,e.internalOptions.tabbableOptions))),n=e.internalOptions.preventScroll;r&&t.focus({preventScroll:void 0!==n&&n}),e.originalOptions.onPostDeactivate&&e.originalOptions.onPostDeactivate.call(null),e.outsideClick=null};this.originalOptions.checkCanReturnFocus?this.originalOptions.checkCanReturnFocus.call(null,this.getReturnFocusNode()).then(t,t):t()}},{key:"setupFocusTrap",value:function(){this.focusTrap?this.props.active&&!this.focusTrap.active&&(this.focusTrap.activate(),this.props.paused&&this.focusTrap.pause()):this.focusTrapElements.some(Boolean)&&(this.focusTrap=this.props._createFocusTrap(this.focusTrapElements,this.internalOptions),this.props.active&&this.focusTrap.activate(),this.props.paused&&this.focusTrap.pause())}},{key:"componentDidMount",value:function(){this.props.active&&this.setupFocusTrap()}},{key:"componentDidUpdate",value:function(e){if(this.focusTrap){e.containerElements!==this.props.containerElements&&this.focusTrap.updateContainerElements(this.props.containerElements);var t=!e.active&&this.props.active,r=e.active&&!this.props.active,n=!e.paused&&this.props.paused,o=e.paused&&!this.props.paused;if(t&&(this.updatePreviousElement(),this.focusTrap.activate()),r)return void this.deactivateTrap();n&&this.focusTrap.pause(),o&&this.focusTrap.unpause()}else e.containerElements!==this.props.containerElements&&(this.focusTrapElements=this.props.containerElements),this.props.active&&(this.updatePreviousElement(),this.setupFocusTrap())}},{key:"componentWillUnmount",value:function(){this.deactivateTrap()}},{key:"render",value:function(){var e=this,t=this.props.children?u.Children.only(this.props.children):void 0;if(t){if(t.type&&t.type===u.Fragment)throw Error("A focus-trap cannot use a Fragment as its child container. Try replacing it with a <div> element.");return u.cloneElement(t,{ref:function(r){var n=e.props.containerElements;t&&("function"==typeof t.ref?t.ref(r):t.ref&&(t.ref.current=r)),e.focusTrapElements=n||[r]}})}return null}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,s(n.key),n)}}(r.prototype,t),Object.defineProperty(r,"prototype",{writable:!1}),r}(u.Component),p="undefined"==typeof Element?Function:Element;f.propTypes={active:c.bool,paused:c.bool,focusTrapOptions:c.shape({document:c.object,onActivate:c.func,onPostActivate:c.func,checkCanFocusTrap:c.func,onPause:c.func,onPostPause:c.func,onUnpause:c.func,onPostUnpause:c.func,onDeactivate:c.func,onPostDeactivate:c.func,checkCanReturnFocus:c.func,initialFocus:c.oneOfType([c.instanceOf(p),c.string,c.bool,c.func]),fallbackFocus:c.oneOfType([c.instanceOf(p),c.string,c.func]),escapeDeactivates:c.oneOfType([c.bool,c.func]),clickOutsideDeactivates:c.oneOfType([c.bool,c.func]),returnFocusOnDeactivate:c.bool,setReturnFocus:c.oneOfType([c.instanceOf(p),c.string,c.bool,c.func]),allowOutsideClick:c.oneOfType([c.bool,c.func]),preventScroll:c.bool,tabbableOptions:c.shape({displayCheck:c.oneOf(["full","legacy-full","non-zero-area","none"]),getShadowRoot:c.oneOfType([c.bool,c.func])}),trapStack:c.array,isKeyForward:c.func,isKeyBackward:c.func}),containerElements:c.arrayOf(c.instanceOf(p)),children:c.oneOfType([c.element,c.instanceOf(p)])},f.defaultProps={active:!0,paused:!1,focusTrapOptions:{},_createFocusTrap:l},e.exports=f},85610:function(e){e.exports=function(e){return e.replace(/\s+/gm," ").split(" ").filter(e=>"false"!==e&&"true"!==e&&"undefined"!==e).join(" ").trim()}},87969:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ErrorCode:()=>u,FormatError:()=>et,IntlMessageFormat:()=>eu,InvalidValueError:()=>er,InvalidValueTypeError:()=>en,MissingValueError:()=>eo,PART_TYPE:()=>c,default:()=>ec,formatToParts:()=>ea,isFormatXMLElementFn:()=>ei});var n,o,i,a,s,u,c,l=r(71096),h=r(29775);function f(e){return e.type===o.literal}function p(e){return e.type===o.number}function d(e){return e.type===o.date}function m(e){return e.type===o.time}function b(e){return e.type===o.select}function v(e){return e.type===o.plural}function y(e){return e.type===o.tag}function g(e){return!!(e&&"object"==typeof e&&e.type===i.number)}function E(e){return!!(e&&"object"==typeof e&&e.type===i.dateTime)}!function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"}(n||(n={})),function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"}(o||(o={})),function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"}(i||(i={}));var T=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,O=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g,_=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i,S=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,P=/^(@+)?(\+|#+)?[rs]?$/g,A=/(\*)(0+)|(#+)(0+)|(0+)/g,I=/^(0+)$/;function N(e){var t={};return"r"===e[e.length-1]?t.roundingPriority="morePrecision":"s"===e[e.length-1]&&(t.roundingPriority="lessPrecision"),e.replace(P,function(e,r,n){return"string"!=typeof n?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):"+"===n?t.minimumSignificantDigits=r.length:"#"===r[0]?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+("string"==typeof n?n.length:0)),""}),t}function H(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function C(e){var t=H(e);return t||{}}var w={"001":["H","h"],419:["h","H","hB","hb"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["h","H","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["h","H","hB","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["h","H","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["h","H","hB","hb"],CU:["h","H","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["h","H","hB","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["h","H","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["h","H","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["h","H","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["h","H","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["h","H","hB","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["h","H","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["h","H","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["h","H","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"en-HK":["h","hb","H","hB"],"en-IL":["H","h","hb","hB"],"en-MY":["h","hb","H","hB"],"es-BR":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]},B=new RegExp("^".concat(T.source,"*")),R=new RegExp("".concat(T.source,"*$"));function L(e,t){return{start:e,end:t}}var F=!!String.prototype.startsWith&&"_a".startsWith("a",1),M=!!String.fromCodePoint,D=!!Object.fromEntries,k=!!String.prototype.codePointAt,G=!!String.prototype.trimStart,U=!!String.prototype.trimEnd,j=Number.isSafeInteger?Number.isSafeInteger:function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&0x1fffffffffffff>=Math.abs(e)},x=!0;try{x=(null==(a=z("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu").exec("a"))?void 0:a[0])==="a"}catch(e){x=!1}var V=F?function(e,t,r){return e.startsWith(t,r)}:function(e,t,r){return e.slice(r,r+t.length)===t},K=M?String.fromCodePoint:function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n="",o=t.length,i=0;o>i;){if((e=t[i++])>1114111)throw RangeError(e+" is not a valid code point");n+=e<65536?String.fromCharCode(e):String.fromCharCode(((e-=65536)>>10)+55296,e%1024+56320)}return n},X=D?Object.fromEntries:function(e){for(var t={},r=0;r<e.length;r++){var n=e[r],o=n[0],i=n[1];t[o]=i}return t},Z=k?function(e,t){return e.codePointAt(t)}:function(e,t){var r,n=e.length;if(!(t<0)&&!(t>=n)){var o=e.charCodeAt(t);return o<55296||o>56319||t+1===n||(r=e.charCodeAt(t+1))<56320||r>57343?o:(o-55296<<10)+(r-56320)+65536}},Y=G?function(e){return e.trimStart()}:function(e){return e.replace(B,"")},W=U?function(e){return e.trimEnd()}:function(e){return e.replace(R,"")};function z(e,t){return new RegExp(e,t)}if(x){var q=z("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");s=function(e,t){var r;return q.lastIndex=t,null!=(r=q.exec(e)[1])?r:""}}else s=function(e,t){for(var r=[];;){var n,o=Z(e,t);if(void 0===o||$(o)||(n=o)>=33&&n<=35||36===n||n>=37&&n<=39||40===n||41===n||42===n||43===n||44===n||45===n||n>=46&&n<=47||n>=58&&n<=59||n>=60&&n<=62||n>=63&&n<=64||91===n||92===n||93===n||94===n||96===n||123===n||124===n||125===n||126===n||161===n||n>=162&&n<=165||166===n||167===n||169===n||171===n||172===n||174===n||176===n||177===n||182===n||187===n||191===n||215===n||247===n||n>=8208&&n<=8213||n>=8214&&n<=8215||8216===n||8217===n||8218===n||n>=8219&&n<=8220||8221===n||8222===n||8223===n||n>=8224&&n<=8231||n>=8240&&n<=8248||8249===n||8250===n||n>=8251&&n<=8254||n>=8257&&n<=8259||8260===n||8261===n||8262===n||n>=8263&&n<=8273||8274===n||8275===n||n>=8277&&n<=8286||n>=8592&&n<=8596||n>=8597&&n<=8601||n>=8602&&n<=8603||n>=8604&&n<=8607||8608===n||n>=8609&&n<=8610||8611===n||n>=8612&&n<=8613||8614===n||n>=8615&&n<=8621||8622===n||n>=8623&&n<=8653||n>=8654&&n<=8655||n>=8656&&n<=8657||8658===n||8659===n||8660===n||n>=8661&&n<=8691||n>=8692&&n<=8959||n>=8960&&n<=8967||8968===n||8969===n||8970===n||8971===n||n>=8972&&n<=8991||n>=8992&&n<=8993||n>=8994&&n<=9e3||9001===n||9002===n||n>=9003&&n<=9083||9084===n||n>=9085&&n<=9114||n>=9115&&n<=9139||n>=9140&&n<=9179||n>=9180&&n<=9185||n>=9186&&n<=9254||n>=9255&&n<=9279||n>=9280&&n<=9290||n>=9291&&n<=9311||n>=9472&&n<=9654||9655===n||n>=9656&&n<=9664||9665===n||n>=9666&&n<=9719||n>=9720&&n<=9727||n>=9728&&n<=9838||9839===n||n>=9840&&n<=10087||10088===n||10089===n||10090===n||10091===n||10092===n||10093===n||10094===n||10095===n||10096===n||10097===n||10098===n||10099===n||10100===n||10101===n||n>=10132&&n<=10175||n>=10176&&n<=10180||10181===n||10182===n||n>=10183&&n<=10213||10214===n||10215===n||10216===n||10217===n||10218===n||10219===n||10220===n||10221===n||10222===n||10223===n||n>=10224&&n<=10239||n>=10240&&n<=10495||n>=10496&&n<=10626||10627===n||10628===n||10629===n||10630===n||10631===n||10632===n||10633===n||10634===n||10635===n||10636===n||10637===n||10638===n||10639===n||10640===n||10641===n||10642===n||10643===n||10644===n||10645===n||10646===n||10647===n||10648===n||n>=10649&&n<=10711||10712===n||10713===n||10714===n||10715===n||n>=10716&&n<=10747||10748===n||10749===n||n>=10750&&n<=11007||n>=11008&&n<=11055||n>=11056&&n<=11076||n>=11077&&n<=11078||n>=11079&&n<=11084||n>=11085&&n<=11123||n>=11124&&n<=11125||n>=11126&&n<=11157||11158===n||n>=11159&&n<=11263||n>=11776&&n<=11777||11778===n||11779===n||11780===n||11781===n||n>=11782&&n<=11784||11785===n||11786===n||11787===n||11788===n||11789===n||n>=11790&&n<=11798||11799===n||n>=11800&&n<=11801||11802===n||11803===n||11804===n||11805===n||n>=11806&&n<=11807||11808===n||11809===n||11810===n||11811===n||11812===n||11813===n||11814===n||11815===n||11816===n||11817===n||n>=11818&&n<=11822||11823===n||n>=11824&&n<=11833||n>=11834&&n<=11835||n>=11836&&n<=11839||11840===n||11841===n||11842===n||n>=11843&&n<=11855||n>=11856&&n<=11857||11858===n||n>=11859&&n<=11903||n>=12289&&n<=12291||12296===n||12297===n||12298===n||12299===n||12300===n||12301===n||12302===n||12303===n||12304===n||12305===n||n>=12306&&n<=12307||12308===n||12309===n||12310===n||12311===n||12312===n||12313===n||12314===n||12315===n||12316===n||12317===n||n>=12318&&n<=12319||12320===n||12336===n||64830===n||64831===n||n>=65093&&n<=65094)break;r.push(o),t+=o>=65536?2:1}return K.apply(void 0,r)};var Q=function(){function e(e,t){void 0===t&&(t={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.locale=t.locale,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(e,t,r){for(var i=[];!this.isEOF();){var a=this.char();if(123===a){var s=this.parseArgument(e,r);if(s.err)return s;i.push(s.val)}else if(125===a&&e>0)break;else if(35===a&&("plural"===t||"selectordinal"===t)){var u=this.clonePosition();this.bump(),i.push({type:o.pound,location:L(u,this.clonePosition())})}else if(60!==a||this.ignoreTag||47!==this.peek())if(60===a&&!this.ignoreTag&&J(this.peek()||0)){var s=this.parseTag(e,t);if(s.err)return s;i.push(s.val)}else{var s=this.parseLiteral(e,t);if(s.err)return s;i.push(s.val)}else if(!r)return this.error(n.UNMATCHED_CLOSING_TAG,L(this.clonePosition(),this.clonePosition()));else break}return{val:i,err:null}},e.prototype.parseTag=function(e,t){var r=this.clonePosition();this.bump();var i=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:o.literal,value:"<".concat(i,"/>"),location:L(r,this.clonePosition())},err:null};if(!this.bumpIf(">"))return this.error(n.INVALID_TAG,L(r,this.clonePosition()));var a=this.parseMessage(e+1,t,!0);if(a.err)return a;var s=a.val,u=this.clonePosition();if(!this.bumpIf("</"))return this.error(n.UNCLOSED_TAG,L(r,this.clonePosition()));if(this.isEOF()||!J(this.char()))return this.error(n.INVALID_TAG,L(u,this.clonePosition()));var c=this.clonePosition();return i!==this.parseTagName()?this.error(n.UNMATCHED_CLOSING_TAG,L(c,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">"))?{val:{type:o.tag,value:i,children:s,location:L(r,this.clonePosition())},err:null}:this.error(n.INVALID_TAG,L(u,this.clonePosition()))},e.prototype.parseTagName=function(){var e,t=this.offset();for(this.bump();!this.isEOF()&&(45===(e=this.char())||46===e||e>=48&&e<=57||95===e||e>=97&&e<=122||e>=65&&e<=90||183==e||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039);)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(e,t){for(var r=this.clonePosition(),n="";;){var i=this.tryParseQuote(t);if(i){n+=i;continue}var a=this.tryParseUnquoted(e,t);if(a){n+=a;continue}var s=this.tryParseLeftAngleBracket();if(s){n+=s;continue}break}var u=L(r,this.clonePosition());return{val:{type:o.literal,value:n,location:u},err:null}},e.prototype.tryParseLeftAngleBracket=function(){var e;return this.isEOF()||60!==this.char()||!this.ignoreTag&&(J(e=this.peek()||0)||47===e)?null:(this.bump(),"<")},e.prototype.tryParseQuote=function(e){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===e||"selectordinal"===e)break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(39===r)if(39===this.peek())t.push(39),this.bump();else{this.bump();break}else t.push(r);this.bump()}return K.apply(void 0,t)},e.prototype.tryParseUnquoted=function(e,t){if(this.isEOF())return null;var r=this.char();return 60===r||123===r||35===r&&("plural"===t||"selectordinal"===t)||125===r&&e>0?null:(this.bump(),K(r))},e.prototype.parseArgument=function(e,t){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,L(r,this.clonePosition()));if(125===this.char())return this.bump(),this.error(n.EMPTY_ARGUMENT,L(r,this.clonePosition()));var i=this.parseIdentifierIfPossible().value;if(!i)return this.error(n.MALFORMED_ARGUMENT,L(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,L(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:o.argument,value:i,location:L(r,this.clonePosition())},err:null};case 44:if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,L(r,this.clonePosition()));return this.parseArgumentOptions(e,t,i,r);default:return this.error(n.MALFORMED_ARGUMENT,L(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),t=this.offset(),r=s(this.message,t),n=t+r.length;return this.bumpTo(n),{value:r,location:L(e,this.clonePosition())}},e.prototype.parseArgumentOptions=function(e,t,r,a){var s,u=this.clonePosition(),c=this.parseIdentifierIfPossible().value,h=this.clonePosition();switch(c){case"":return this.error(n.EXPECT_ARGUMENT_TYPE,L(u,h));case"number":case"date":case"time":this.bumpSpace();var f=null;if(this.bumpIf(",")){this.bumpSpace();var p=this.clonePosition(),d=this.parseSimpleArgStyleIfPossible();if(d.err)return d;var m=W(d.val);if(0===m.length)return this.error(n.EXPECT_ARGUMENT_STYLE,L(this.clonePosition(),this.clonePosition()));f={style:m,styleLocation:L(p,this.clonePosition())}}var b=this.tryParseArgumentClose(a);if(b.err)return b;var v=L(a,this.clonePosition());if(f&&V(null==f?void 0:f.style,"::",0)){var y=Y(f.style.slice(2));if("number"===c){var d=this.parseNumberSkeletonFromString(y,f.styleLocation);if(d.err)return d;return{val:{type:o.number,value:r,location:v,style:d.val},err:null}}if(0===y.length)return this.error(n.EXPECT_DATE_TIME_SKELETON,v);var g,E=y;this.locale&&(E=function(e,t){for(var r="",n=0;n<e.length;n++){var o=e.charAt(n);if("j"===o){for(var i=0;n+1<e.length&&e.charAt(n+1)===o;)i++,n++;var a=1+(1&i),s=i<2?1:3+(i>>1),u=function(e){var t,r=e.hourCycle;if(void 0===r&&e.hourCycles&&e.hourCycles.length&&(r=e.hourCycles[0]),r)switch(r){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw Error("Invalid hourCycle")}var n=e.language;return"root"!==n&&(t=e.maximize().region),(w[t||""]||w[n||""]||w["".concat(n,"-001")]||w["001"])[0]}(t);for(("H"==u||"k"==u)&&(s=0);s-- >0;)r+="a";for(;a-- >0;)r=u+r}else"J"===o?r+="H":r+=o}return r}(y,this.locale));var m={type:i.dateTime,pattern:E,location:f.styleLocation,parsedOptions:this.shouldParseSkeletons?(g={},E.replace(O,function(e){var t=e.length;switch(e[0]){case"G":g.era=4===t?"long":5===t?"narrow":"short";break;case"y":g.year=2===t?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":g.month=["numeric","2-digit","short","long","narrow"][t-1];break;case"w":case"W":throw RangeError("`w/W` (week) patterns are not supported");case"d":g.day=["numeric","2-digit"][t-1];break;case"D":case"F":case"g":throw RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":g.weekday=4===t?"long":5===t?"narrow":"short";break;case"e":if(t<4)throw RangeError("`e..eee` (weekday) patterns are not supported");g.weekday=["short","long","narrow","short"][t-4];break;case"c":if(t<4)throw RangeError("`c..ccc` (weekday) patterns are not supported");g.weekday=["short","long","narrow","short"][t-4];break;case"a":g.hour12=!0;break;case"b":case"B":throw RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":g.hourCycle="h12",g.hour=["numeric","2-digit"][t-1];break;case"H":g.hourCycle="h23",g.hour=["numeric","2-digit"][t-1];break;case"K":g.hourCycle="h11",g.hour=["numeric","2-digit"][t-1];break;case"k":g.hourCycle="h24",g.hour=["numeric","2-digit"][t-1];break;case"j":case"J":case"C":throw RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":g.minute=["numeric","2-digit"][t-1];break;case"s":g.second=["numeric","2-digit"][t-1];break;case"S":case"A":throw RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":g.timeZoneName=t<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),g):{}};return{val:{type:"date"===c?o.date:o.time,value:r,location:v,style:m},err:null}}return{val:{type:"number"===c?o.number:"date"===c?o.date:o.time,value:r,location:v,style:null!=(s=null==f?void 0:f.style)?s:null},err:null};case"plural":case"selectordinal":case"select":var T=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(n.EXPECT_SELECT_ARGUMENT_OPTIONS,L(T,(0,l.Cl)({},T)));this.bumpSpace();var _=this.parseIdentifierIfPossible(),S=0;if("select"!==c&&"offset"===_.value){if(!this.bumpIf(":"))return this.error(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,L(this.clonePosition(),this.clonePosition()));this.bumpSpace();var d=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,n.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(d.err)return d;this.bumpSpace(),_=this.parseIdentifierIfPossible(),S=d.val}var P=this.tryParsePluralOrSelectOptions(e,c,t,_);if(P.err)return P;var b=this.tryParseArgumentClose(a);if(b.err)return b;var A=L(a,this.clonePosition());if("select"===c)return{val:{type:o.select,value:r,options:X(P.val),location:A},err:null};return{val:{type:o.plural,value:r,options:X(P.val),offset:S,pluralType:"plural"===c?"cardinal":"ordinal",location:A},err:null};default:return this.error(n.INVALID_ARGUMENT_TYPE,L(u,h))}},e.prototype.tryParseArgumentClose=function(e){return this.isEOF()||125!==this.char()?this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,L(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,t=this.clonePosition();!this.isEOF();)switch(this.char()){case 39:this.bump();var r=this.clonePosition();if(!this.bumpUntil("'"))return this.error(n.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,L(r,this.clonePosition()));this.bump();break;case 123:e+=1,this.bump();break;case 125:if(!(e>0))return{val:this.message.slice(t.offset,this.offset()),err:null};e-=1;break;default:this.bump()}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(e,t){var r=[];try{r=function(e){if(0===e.length)throw Error("Number skeleton cannot be empty");for(var t=e.split(_).filter(function(e){return e.length>0}),r=[],n=0;n<t.length;n++){var o=t[n].split("/");if(0===o.length)throw Error("Invalid number skeleton");for(var i=o[0],a=o.slice(1),s=0;s<a.length;s++)if(0===a[s].length)throw Error("Invalid number skeleton");r.push({stem:i,options:a})}return r}(e)}catch(e){return this.error(n.INVALID_NUMBER_SKELETON,t)}return{val:{type:i.number,tokens:r,location:t,parsedOptions:this.shouldParseSkeletons?function(e){for(var t={},r=0;r<e.length;r++){var n=e[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=n.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=(0,l.Cl)((0,l.Cl)((0,l.Cl)({},t),{notation:"scientific"}),n.options.reduce(function(e,t){return(0,l.Cl)((0,l.Cl)({},e),C(t))},{}));continue;case"engineering":t=(0,l.Cl)((0,l.Cl)((0,l.Cl)({},t),{notation:"engineering"}),n.options.reduce(function(e,t){return(0,l.Cl)((0,l.Cl)({},e),C(t))},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(n.options.length>1)throw RangeError("integer-width stems only accept a single optional option");n.options[0].replace(A,function(e,r,n,o,i,a){if(r)t.minimumIntegerDigits=n.length;else if(o&&i)throw Error("We currently do not support maximum integer digits");else if(a)throw Error("We currently do not support exact integer digits");return""});continue}if(I.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(S.test(n.stem)){if(n.options.length>1)throw RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(S,function(e,r,n,o,i,a){return"*"===n?t.minimumFractionDigits=r.length:o&&"#"===o[0]?t.maximumFractionDigits=o.length:i&&a?(t.minimumFractionDigits=i.length,t.maximumFractionDigits=i.length+a.length):(t.minimumFractionDigits=r.length,t.maximumFractionDigits=r.length),""});var o=n.options[0];"w"===o?t=(0,l.Cl)((0,l.Cl)({},t),{trailingZeroDisplay:"stripIfInteger"}):o&&(t=(0,l.Cl)((0,l.Cl)({},t),N(o)));continue}if(P.test(n.stem)){t=(0,l.Cl)((0,l.Cl)({},t),N(n.stem));continue}var i=H(n.stem);i&&(t=(0,l.Cl)((0,l.Cl)({},t),i));var a=function(e){var t;if("E"===e[0]&&"E"===e[1]?(t={notation:"engineering"},e=e.slice(2)):"E"===e[0]&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if("+!"===r?(t.signDisplay="always",e=e.slice(2)):"+?"===r&&(t.signDisplay="exceptZero",e=e.slice(2)),!I.test(e))throw Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}(n.stem);a&&(t=(0,l.Cl)((0,l.Cl)({},t),a))}return t}(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(e,t,r,o){for(var i,a=!1,s=[],u=new Set,c=o.value,l=o.location;;){if(0===c.length){var h=this.clonePosition();if("select"!==t&&this.bumpIf("=")){var f=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_SELECTOR,n.INVALID_PLURAL_ARGUMENT_SELECTOR);if(f.err)return f;l=L(h,this.clonePosition()),c=this.message.slice(h.offset,this.offset())}else break}if(u.has(c))return this.error("select"===t?n.DUPLICATE_SELECT_ARGUMENT_SELECTOR:n.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,l);"other"===c&&(a=!0),this.bumpSpace();var p=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:n.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,L(this.clonePosition(),this.clonePosition()));var d=this.parseMessage(e+1,t,r);if(d.err)return d;var m=this.tryParseArgumentClose(p);if(m.err)return m;s.push([c,{value:d.val,location:L(p,this.clonePosition())}]),u.add(c),this.bumpSpace(),c=(i=this.parseIdentifierIfPossible()).value,l=i.location}return 0===s.length?this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR:n.EXPECT_PLURAL_ARGUMENT_SELECTOR,L(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!a?this.error(n.MISSING_OTHER_CLAUSE,L(this.clonePosition(),this.clonePosition())):{val:s,err:null}},e.prototype.tryParseDecimalInteger=function(e,t){var r=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var o=!1,i=0;!this.isEOF();){var a=this.char();if(a>=48&&a<=57)o=!0,i=10*i+(a-48),this.bump();else break}var s=L(n,this.clonePosition());return o?j(i*=r)?{val:i,err:null}:this.error(t,s):this.error(e,s)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var t=Z(this.message,e);if(void 0===t)throw Error("Offset ".concat(e," is at invalid UTF-16 code unit boundary"));return t},e.prototype.error=function(e,t){return{val:null,err:{kind:e,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var e=this.char();10===e?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},e.prototype.bumpIf=function(e){if(V(this.message,e,this.offset())){for(var t=0;t<e.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(e){var t=this.offset(),r=this.message.indexOf(e,t);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset ".concat(e," must be greater than or equal to the current offset ").concat(this.offset()));for(e=Math.min(e,this.message.length);;){var t=this.offset();if(t===e)break;if(t>e)throw Error("targetOffset ".concat(e," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&$(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),t=this.offset(),r=this.message.charCodeAt(t+(e>=65536?2:1));return null!=r?r:null},e}();function J(e){return e>=97&&e<=122||e>=65&&e<=90}function $(e){return e>=9&&e<=13||32===e||133===e||e>=8206&&e<=8207||8232===e||8233===e}function ee(e,t){void 0===t&&(t={});var r=new Q(e,t=(0,l.Cl)({shouldParseSkeletons:!0,requiresOtherClause:!0},t)).parse();if(r.err){var o=SyntaxError(n[r.err.kind]);throw o.location=r.err.location,o.originalMessage=r.err.message,o}return(null==t?void 0:t.captureLocation)||function e(t){t.forEach(function(t){if(delete t.location,b(t)||v(t))for(var r in t.options)delete t.options[r].location,e(t.options[r].value);else p(t)&&g(t.style)||(d(t)||m(t))&&E(t.style)?delete t.style.location:y(t)&&e(t.children)})}(r.val),r.val}!function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"}(u||(u={}));var et=function(e){function t(t,r,n){var o=e.call(this,t)||this;return o.code=r,o.originalMessage=n,o}return(0,l.C6)(t,e),t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),er=function(e){function t(t,r,n,o){return e.call(this,'Invalid values for "'.concat(t,'": "').concat(r,'". Options are "').concat(Object.keys(n).join('", "'),'"'),u.INVALID_VALUE,o)||this}return(0,l.C6)(t,e),t}(et),en=function(e){function t(t,r,n){return e.call(this,'Value for "'.concat(t,'" must be of type ').concat(r),u.INVALID_VALUE,n)||this}return(0,l.C6)(t,e),t}(et),eo=function(e){function t(t,r){return e.call(this,'The intl string context variable "'.concat(t,'" was not provided to the string "').concat(r,'"'),u.MISSING_VALUE,r)||this}return(0,l.C6)(t,e),t}(et);function ei(e){return"function"==typeof e}function ea(e,t,r,n,i,a,s){if(1===e.length&&f(e[0]))return[{type:c.literal,value:e[0].value}];for(var l=[],h=0;h<e.length;h++){var T=e[h];if(f(T)){l.push({type:c.literal,value:T.value});continue}if(T.type===o.pound){"number"==typeof a&&l.push({type:c.literal,value:r.getNumberFormat(t).format(a)});continue}var O=T.value;if(!(i&&O in i))throw new eo(O,s);var _=i[O];if(T.type===o.argument){_&&"string"!=typeof _&&"number"!=typeof _||(_="string"==typeof _||"number"==typeof _?String(_):""),l.push({type:"string"==typeof _?c.literal:c.object,value:_});continue}if(d(T)){var S="string"==typeof T.style?n.date[T.style]:E(T.style)?T.style.parsedOptions:void 0;l.push({type:c.literal,value:r.getDateTimeFormat(t,S).format(_)});continue}if(m(T)){var S="string"==typeof T.style?n.time[T.style]:E(T.style)?T.style.parsedOptions:n.time.medium;l.push({type:c.literal,value:r.getDateTimeFormat(t,S).format(_)});continue}if(p(T)){var S="string"==typeof T.style?n.number[T.style]:g(T.style)?T.style.parsedOptions:void 0;S&&S.scale&&(_*=S.scale||1),l.push({type:c.literal,value:r.getNumberFormat(t,S).format(_)});continue}if(y(T)){var P=T.children,A=T.value,I=i[A];if(!ei(I))throw new en(A,"function",s);var N=I(ea(P,t,r,n,i,a).map(function(e){return e.value}));Array.isArray(N)||(N=[N]),l.push.apply(l,N.map(function(e){return{type:"string"==typeof e?c.literal:c.object,value:e}}))}if(b(T)){var H=T.options[_]||T.options.other;if(!H)throw new er(T.value,_,Object.keys(T.options),s);l.push.apply(l,ea(H.value,t,r,n,i));continue}if(v(T)){var H=T.options["=".concat(_)];if(!H){if(!Intl.PluralRules)throw new et('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',u.MISSING_INTL_API,s);var C=r.getPluralRules(t,{type:T.pluralType}).select(_-(T.offset||0));H=T.options[C]||T.options.other}if(!H)throw new er(T.value,_,Object.keys(T.options),s);l.push.apply(l,ea(H.value,t,r,n,i,_-(T.offset||0)));continue}}return l.length<2?l:l.reduce(function(e,t){var r=e[e.length-1];return r&&r.type===c.literal&&t.type===c.literal?r.value+=t.value:e.push(t),e},[])}function es(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}!function(e){e[e.literal=0]="literal",e[e.object=1]="object"}(c||(c={}));var eu=function(){function e(t,r,n,o){void 0===r&&(r=e.defaultLocale);var i,a,s=this;if(this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(e){var t=s.formatToParts(e);if(1===t.length)return t[0].value;var r=t.reduce(function(e,t){return e.length&&t.type===c.literal&&"string"==typeof e[e.length-1]?e[e.length-1]+=t.value:e.push(t.value),e},[]);return r.length<=1?r[0]||"":r},this.formatToParts=function(e){return ea(s.ast,s.locales,s.formatters,s.formats,e,void 0,s.message)},this.resolvedOptions=function(){var e;return{locale:(null==(e=s.resolvedLocale)?void 0:e.toString())||Intl.NumberFormat.supportedLocalesOf(s.locales)[0]}},this.getAst=function(){return s.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),"string"==typeof t){if(this.message=t,!e.__parse)throw TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var u=o||{},f=(u.formatters,(0,l.Tt)(u,["formatters"]));this.ast=e.__parse(t,(0,l.Cl)((0,l.Cl)({},f),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw TypeError("A message must be provided as a String or AST.");this.formats=(i=e.formats,n?Object.keys(i).reduce(function(e,t){var r,o;return e[t]=(r=i[t],(o=n[t])?(0,l.Cl)((0,l.Cl)((0,l.Cl)({},r||{}),o||{}),Object.keys(r).reduce(function(e,t){return e[t]=(0,l.Cl)((0,l.Cl)({},r[t]),o[t]||{}),e},{})):r),e},(0,l.Cl)({},i)):i),this.formatters=o&&o.formatters||(void 0===(a=this.formatterCache)&&(a={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:(0,h.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.NumberFormat).bind.apply(e,(0,l.fX)([void 0],t,!1)))},{cache:es(a.number),strategy:h.strategies.variadic}),getDateTimeFormat:(0,h.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.DateTimeFormat).bind.apply(e,(0,l.fX)([void 0],t,!1)))},{cache:es(a.dateTime),strategy:h.strategies.variadic}),getPluralRules:(0,h.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.PluralRules).bind.apply(e,(0,l.fX)([void 0],t,!1)))},{cache:es(a.pluralRules),strategy:h.strategies.variadic})})}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(e){if(void 0!==Intl.Locale){var t=Intl.NumberFormat.supportedLocalesOf(e);return new Intl.Locale(t.length>0?t[0]:"string"==typeof e?e:e[0])}},e.__parse=ee,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();let ec=eu},88480:(e,t,r)=>{var n=r(68445),o=r(28769),i=r(52846),a=n?n.toStringTag:void 0;e.exports=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":a&&a in Object(e)?o(e):i(e)}},88903:(e,t,r)=>{"use strict";e.exports=r(7817)},91039:(e,t,r)=>{"use strict";r.d(t,{default:()=>o.a});var n=r(37684),o=r.n(n)},92644:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let n=r(54568),o=r(7620),i=r(60931);function a(e){return{default:e&&"default"in e?e.default:e}}r(96670);let s={loader:()=>Promise.resolve(a(()=>null)),loading:null,ssr:!0},u=function(e){let t={...s,...e},r=(0,o.lazy)(()=>t.loader().then(a)),u=t.loading;function c(e){let a=u?(0,n.jsx)(u,{isLoading:!0,pastDelay:!0,error:null}):null,s=!t.ssr||!!t.loading,c=s?o.Suspense:o.Fragment,l=t.ssr?(0,n.jsxs)(n.Fragment,{children:[null,(0,n.jsx)(r,{...e})]}):(0,n.jsx)(i.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(r,{...e})});return(0,n.jsx)(c,{...s?{fallback:a}:{},children:l})}return c.displayName="LoadableComponent",c}},96670:(e,t,r)=>{"use strict";function n(e){let{moduleIds:t}=e;return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return n}}),r(54568),r(97509),r(49669),r(32252)},99474:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(31543),o=r(6982),i=r(67365),a=r(88903);t.useFormatter=n.useFormatter,t.useTranslations=n.useTranslations,t.useLocale=o.default,t.NextIntlClientProvider=i.default,Object.keys(a).forEach(function(e){"default"===e||Object.prototype.hasOwnProperty.call(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}})})}}]);