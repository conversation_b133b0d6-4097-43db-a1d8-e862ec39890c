!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="2fe5f582-f43f-4747-ad5c-c262bfbabc95",e._sentryDebugIdIdentifier="sentry-dbid-2fe5f582-f43f-4747-ad5c-c262bfbabc95")}catch(e){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9571],{9571:(e,n,l)=>{l.r(n),l.d(n,{BlogHero:()=>y});var t=l(54568),a=l(7620),r=l(50297),s=l(86779),i=l(31328),o=l(68061),c=l(41066),u=l(82854),d=l(16800);function p(e,n){let l,t=!1;return function(){for(var a=arguments.length,r=Array(a),s=0;s<a;s++)r[s]=arguments[s];t||(e.apply(this,r),t=!0,setTimeout(()=>{t=!1},n)),clearTimeout(l),l=setTimeout(()=>{e.apply(this,r)},n)}}var h=l(85610),m=l.n(h);let f={progressBar:m()("\n  bg-ultra-light-purple\n  h-[5px] \n  w-full\n\n"),progress:m()("\n    bg-dp-purple\n    h-[5px] \n    transition-width \n    duration-300 \n    ease-in-out\n  ")},x=()=>{let[e,n]=(0,a.useState)(0),l=(0,a.useCallback)(p(()=>{let e=document.documentElement.scrollHeight-window.innerHeight;n(window.scrollY/e*100)},100),[]);return(0,a.useEffect)(()=>(window.addEventListener("scroll",l),()=>window.removeEventListener("scroll",l)),[l]),(0,t.jsx)("div",{className:f.progressBar,children:(0,t.jsx)("div",{className:f.progress,style:{width:"".concat(e,"%")}})})};var g=l(62904),v=l(5474),b=l(71034);let w=(e,n)=>e?n?m()("lg:top-[159px]"):m()("lg:top-[121px]"):n?m()("lg:top-[110px]"):m()("lg:top-[72px]"),N="\n  relative \n  before:-z-10 \n  before:absolute \n  before:bg-light-tan \n  before:content-[''] \n  before:left-[calc((100vw-100%)/-2)]\n",j={author:m()("\n    flex\n    mt-18px\n    w-fit\n  "),authors:m()("\n    mx-auto\n    w-fit\n  "),authorDetails:m()("\n    text-left\n    flex\n    flex-col\n    justify-center\n  "),authorName:m()("\n    font-semibold\n    not-italic\n    paragraph-small\n    underline\n  "),avatarWrapper:m()("\n    h-57px\n    w-57px\n    mr-3\n    overflow-hidden\n    relative\n    rounded-full\n    aspect-square\n  "),avatarImage:m()("\n    absolute\n    top-0\n    left-0\n    w-full\n    h-full\n    object-cover\n  "),backIcon:m()("\n    h-10px\n    mr-2\n    w-4\n\n    [&_path]:fill-black\n  "),breadcrumbLink:m()("\n    flex\n    items-center\n    justify-center\n    uppercase\n    underline\n\n    hover:text-dp-purple\n  "),breadcrumbs:m()("\n    pt-8\n    \n    h5\n    text-center\n    uppercase\n  "),container:"container !overflow-visible",content:m()("\n    block-container\n    relative\n\n    lg:px-0\n  "),topContent:m()("\n    pb-8 \n    before:h-full \n    \n    ".concat(N,"\n  ")),imageContainer:m()("\n    before:h-3/6\n\n    ".concat(N,"\n  ")),figure:m()("\n    aspect-[1/1]\n    mx-auto\n    overflow-hidden\n    relative\n    rounded-30px\n    w-full\n\n    md:aspect-[704/416]\n\n    lg:aspect-[798/480]\n    lg:max-w-798px\n  "),footer:m()("\n    mt-31px\n\n    md:flex\n\n    lg:justify-start\n    lg:max-w-798px\n    lg:mx-auto\n    px-4\n    lg:px-8\n    items-center\n    pb-[72px]\n  "),image:m()("\n    w-full\n    h-full\n  "),label:m()("\n    md:h5\n    xs:h5-mobile\n    uppercase\n  "),richtext:m()("\n    max-w-798px\n    mt-31px\n    mx-auto\n    [&_ul_li_p]:!paragraph\n  "),role:m()("\n    paragraph-small\n  "),selectWrapper:m()("\n    w-full\n\n    md:max-w-335px   \n    min-h-83px \n  "),share:m()("\n    mt-3\n    transition-opacity\n\n    hover:opacity-70\n\n    [&_svg]:ml-0\n  "),tag:m()("\n    border-eerie-black\n    paragraph-sm\n    !text-eerie-black\n  "),tags:"mt-3 flex flex-wrap gap-6px",title:m()("\n    \n    h2-mobile\n    mt-8\n    text-center\n    px-8\n\n   \n  "),wrapper:m()("\n    mt-33px\n    md:ml-30px\n    md:mt-0\n  "),blogNav:(e,n)=>m()("\n    sticky\n    ".concat(w(e,n),"\n    xs:top-[60px]\n    z-20\n    w-full \n    bg-white \n    shadow-[0px_10px_24px_rgba(0,0,0,0.1)]\n  ")),blogNavBar:m()("\n    container \n    flex \n    items-center \n    justify-between \n    !overflow-visible \n    px-2 \n    lg:max-w-[1280px]\n  "),blogLink:m()("\n    h4\n    mr-0 \n    xs:mr-[24px] \n    text-dp-purple\n  ")},y=e=>{var n,l,h,m,f,w,N;let{authors:y,description:k,imageWithFocalArea:C,tags:_,title:B,navigationData:T,pageOverrides:E}=e,{isHeadachebarVisible:L}=(0,r.A)(null==T?void 0:T.headacheBarData,null==E?void 0:E.headacheBar),[S,I]=(0,a.useState)([]),[D,A]=(0,a.useState)(!1),[R,W]=(0,a.useState)([]),[H,Y]=(0,a.useState)(""),q=(0,a.useRef)(null),z=(0,a.useRef)(null),V=(0,a.useRef)(null),{scrollY:$}=(0,s.s)(),F=()=>{A(!0),navigator.clipboard.writeText(window.location.href)};(0,a.useEffect)(()=>{D&&setTimeout(()=>{A(!1)},3e3)},[D]);let G=e=>{var n;let l=parseInt(e),t=z.current?z.current.getBoundingClientRect().bottom:0,a=(null==(n=S[l])?void 0:n.offsetTop)||0;window.scrollTo({top:a-t,behavior:"smooth"})};(0,a.useEffect)(()=>{let e=S.map((e,n)=>{var l;return{top:(null==e?void 0:e.offsetTop)||0,bottom:(null==(l=S[n+1])?void 0:l.offsetTop)||1/0}}),n=p(()=>{if(!z.current)return;let{bottom:n}=z.current.getBoundingClientRect(),l=window.scrollY+n+45,t=null;for(let n=0;n<S.length;n++){let a=S[n];if(a){let r=e[n];if(r.top<=l&&r.bottom>=l){H!==(t=a.textContent||null)&&t&&Y(t);break}}}t!==q.current&&(q.current=t)},30);return window.addEventListener("scroll",n),()=>{window.removeEventListener("scroll",n)}},[S,z]),(0,a.useEffect)(()=>{let e=setTimeout(()=>{let e=Array.from(document.querySelectorAll(".richtext-heading")),n=[...V.current?[V.current]:[],...e].filter(e=>null!==e),l=e.map((e,n)=>{let{textContent:l}=e;return{label:null!=l?l:"",value:(n+1).toString()}});I(n),W([{label:B,value:"0"},...l])},100);return()=>clearTimeout(e)},[B,k]);let O=R.map(e=>({label:e.label,value:e.value.toString()}));return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:j.blogNav(L,$<118),ref:z,children:[(0,t.jsx)(x,{}),(0,t.jsxs)("div",{className:j.blogNavBar,children:[(0,t.jsx)(b.N,{className:j.blogLink,href:"/blog",children:"BLOG"}),(0,t.jsx)(g.m,{className:"blog-dropdown w-full",selectedValue:H||(null==(n=O[0])?void 0:n.value),onChange:e=>{Y(e),G(e)},options:O,label:H||(null==(l=O[0])?void 0:l.label),variant:"toc"}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("h5",{className:j.label,children:D?"Copied":"Share"}),(0,t.jsx)(i.$n,{className:"".concat(j.share," !mt-0"),disabled:D,onClick:F,variant:"transparent",children:(0,t.jsx)(o.li,{})})]})]})]}),(0,t.jsx)("section",{className:j.container,children:(0,t.jsxs)("div",{className:j.content,children:[(0,t.jsxs)("div",{className:j.topContent,children:[(0,t.jsx)("p",{className:j.breadcrumbs,children:(0,t.jsx)(b.N,{className:j.breadcrumbLink,href:"/blog",children:"Back to Blogs"})}),(0,t.jsx)("h1",{className:j.title,ref:V,children:B}),y.length>0&&(0,t.jsx)("div",{className:j.authors,children:y.map((e,n)=>{var l,a,r,s,i,o,c,u,d,p;return(0,t.jsxs)("div",{className:j.author,children:[(0,t.jsx)(b.N,{href:"/blog/authors/".concat(e.slug),children:(0,t.jsx)("figure",{className:j.avatarWrapper,children:(0,t.jsx)(v.s,{src:null==(a=e.avatar)||null==(l=a.image)?void 0:l.url,alt:(null==(s=e.avatar)||null==(r=s.image)?void 0:r.title)||"",width:null==(o=e.avatar)||null==(i=o.image)?void 0:i.width,height:null==(u=e.avatar)||null==(c=u.image)?void 0:c.height,title:(null==(p=e.avatar)||null==(d=p.image)?void 0:d.title)||"",className:j.avatarImage})})},n),(0,t.jsxs)("div",{className:j.authorDetails,children:[(0,t.jsx)(b.N,{href:"/blog/authors/".concat(e.slug),children:(0,t.jsx)("cite",{className:j.authorName,children:e.name})},n),(0,t.jsx)("p",{className:j.role,children:e.role})]})]},n)})}),k&&(0,t.jsx)(c.default,{alignment:"Left",className:j.richtext,contentBody:k})]}),(0,t.jsx)("div",{className:j.imageContainer,children:(null==C||null==(h=C.image)?void 0:h.url)&&(0,t.jsx)("figure",{className:j.figure,children:(0,t.jsx)(v.s,{alt:(null==C||null==(m=C.image)?void 0:m.title)||"Placeholder",src:null==C||null==(f=C.image)?void 0:f.url,artDirection:{mobile:{width:256,height:256},tablet:{width:704,height:416},desktop:{width:798,height:480}},focusArea:null==C?void 0:C.focusArea,width:null==C||null==(w=C.image)?void 0:w.width,height:null==C||null==(N=C.image)?void 0:N.height,className:j.image})})}),(0,t.jsxs)("div",{className:j.footer,children:[(0,t.jsx)("div",{className:j.selectWrapper,children:(0,t.jsx)(u.l,{selectClassName:"w-full overflow-x-hidden",defaultValue:"0",disallowUnselect:!0,label:"Table of Contents",onChange:e=>{let{target:n}=e;Y(n.value),G(n.value)},options:R})}),_&&_.length>0&&(0,t.jsxs)("div",{className:j.wrapper,children:[(0,t.jsx)("p",{className:j.label,children:"Tags"}),(0,t.jsx)("div",{className:j.tags,children:_.map((e,n)=>(0,t.jsx)(d.Y,{className:j.tag,content:e},n))})]}),(0,t.jsxs)("div",{className:"".concat(j.wrapper," !ml-auto mr-[80px]"),children:[(0,t.jsx)("p",{className:j.label,children:D?"Copied":"Share"}),(0,t.jsx)(i.$n,{className:j.share,disabled:D,onClick:F,variant:"transparent",children:(0,t.jsx)(o.li,{})})]})]})]})})]})}}}]);