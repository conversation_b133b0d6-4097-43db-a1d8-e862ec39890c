
window[window["TiktokAnalyticsObject"]]._env = {"env":"external","key":""};
window[window["TiktokAnalyticsObject"]]._variation_id = 'default';window[window["TiktokAnalyticsObject"]]._vids = '74169685,74169794';window[window["TiktokAnalyticsObject"]]._cc = 'PK';window[window.TiktokAnalyticsObject]._li||(window[window.TiktokAnalyticsObject]._li={}),window[window.TiktokAnalyticsObject]._li["CLBPQIRC77UBB9N4UEG0"]="35ca5beb-6742-11f0-9e86-02001719b163";window[window["TiktokAnalyticsObject"]]._cde = 390;; if(!window[window["TiktokAnalyticsObject"]]._server_unique_id) window[window["TiktokAnalyticsObject"]]._server_unique_id = '35ca8942-6742-11f0-9e86-02001719b163';window[window["TiktokAnalyticsObject"]]._plugins = {"AdvancedMatching":true,"AutoAdvancedMatching":true,"AutoConfig":true,"Callback":true,"DiagnosticsConsole":true,"EnrichIpv6":true,"EnrichIpv6V2":true,"EventBuilder":true,"EventBuilderRuleEngine":false,"HistoryObserver":true,"HitReservoir":true,"Identify":true,"JSBridge":false,"Monitor":true,"PageData":false,"PangleCookieMatching":true,"PerformanceInteraction":false,"RuntimeMeasurement":true,"Shopify":true,"WebFL":false};window[window["TiktokAnalyticsObject"]]._csid_config = {"enable":true};window[window["TiktokAnalyticsObject"]]._auto_config = {"open_graph":["audience"],"microdata":["audience"],"json_ld":["audience"],"meta":null};
!function(e,n,i,d,o,t){var u,a,c=g()._static_map||[{id:"MTc3NWUxZTAxMA",map:{AutoAdvancedMatching:!1,Shopify:!1,JSBridge:!1,EventBuilderRuleEngine:!1,RemoveUnusedCode:!1}},{id:"MTc3NWUxZTAxMQ",map:{AutoAdvancedMatching:!0,Shopify:!1,JSBridge:!1,EventBuilderRuleEngine:!1,RemoveUnusedCode:!1}},{id:"MTc3NWUxZTAxMg",map:{AutoAdvancedMatching:!1,Shopify:!0,JSBridge:!1,EventBuilderRuleEngine:!1,RemoveUnusedCode:!1}},{id:"MTc3NWUxZTAxMw",map:{AutoAdvancedMatching:!0,Shopify:!0,JSBridge:!1,EventBuilderRuleEngine:!1,RemoveUnusedCode:!1}},{id:"MTc3NWUxZTAxNA",map:{AutoAdvancedMatching:!1,Shopify:!1,JSBridge:!0,EventBuilderRuleEngine:!1,RemoveUnusedCode:!1}},{id:"MTc3NWUxZTAxNQ",map:{AutoAdvancedMatching:!0,Shopify:!1,JSBridge:!0,EventBuilderRuleEngine:!1,RemoveUnusedCode:!1}},{id:"MTc3NWUxZTAxNg",map:{AutoAdvancedMatching:!1,Shopify:!0,JSBridge:!0,EventBuilderRuleEngine:!1,RemoveUnusedCode:!1}},{id:"MTc3NWUxZTAxNw",map:{AutoAdvancedMatching:!0,Shopify:!0,JSBridge:!0,EventBuilderRuleEngine:!1,RemoveUnusedCode:!1}},{id:"MTc3NWUxZTAxOA",map:{AutoAdvancedMatching:!1,Shopify:!1,JSBridge:!1,EventBuilderRuleEngine:!0,RemoveUnusedCode:!1}},{id:"MTc3NWUxZTAxOQ",map:{AutoAdvancedMatching:!0,Shopify:!1,JSBridge:!1,EventBuilderRuleEngine:!0,RemoveUnusedCode:!1}},{id:"MTc3NWUxZTAxMTA",map:{AutoAdvancedMatching:!1,Shopify:!0,JSBridge:!1,EventBuilderRuleEngine:!0,RemoveUnusedCode:!1}},{id:"MTc3NWUxZTAxMTE",map:{AutoAdvancedMatching:!0,Shopify:!0,JSBridge:!1,EventBuilderRuleEngine:!0,RemoveUnusedCode:!1}},{id:"MTc3NWUxZTAxMTI",map:{AutoAdvancedMatching:!1,Shopify:!1,JSBridge:!0,EventBuilderRuleEngine:!0,RemoveUnusedCode:!1}},{id:"MTc3NWUxZTAxMTM",map:{AutoAdvancedMatching:!0,Shopify:!1,JSBridge:!0,EventBuilderRuleEngine:!0,RemoveUnusedCode:!1}},{id:"MTc3NWUxZTAxMTQ",map:{AutoAdvancedMatching:!1,Shopify:!0,JSBridge:!0,EventBuilderRuleEngine:!0,RemoveUnusedCode:!1}},{id:"MTc3NWUxZTAxMTU",map:{AutoAdvancedMatching:!0,Shopify:!0,JSBridge:!0,EventBuilderRuleEngine:!0,RemoveUnusedCode:!1}},{id:"MTc3NWUxZTAxMTY",map:{AutoAdvancedMatching:!1,Shopify:!1,JSBridge:!1,EventBuilderRuleEngine:!1,RemoveUnusedCode:!0}},{id:"MTc3NWUxZTAxMTc",map:{AutoAdvancedMatching:!0,Shopify:!1,JSBridge:!1,EventBuilderRuleEngine:!1,RemoveUnusedCode:!0}},{id:"MTc3NWUxZTAxMTg",map:{AutoAdvancedMatching:!1,Shopify:!0,JSBridge:!1,EventBuilderRuleEngine:!1,RemoveUnusedCode:!0}},{id:"MTc3NWUxZTAxMTk",map:{AutoAdvancedMatching:!0,Shopify:!0,JSBridge:!1,EventBuilderRuleEngine:!1,RemoveUnusedCode:!0}},{id:"MTc3NWUxZTAxMjA",map:{AutoAdvancedMatching:!1,Shopify:!1,JSBridge:!0,EventBuilderRuleEngine:!1,RemoveUnusedCode:!0}},{id:"MTc3NWUxZTAxMjE",map:{AutoAdvancedMatching:!0,Shopify:!1,JSBridge:!0,EventBuilderRuleEngine:!1,RemoveUnusedCode:!0}},{id:"MTc3NWUxZTAxMjI",map:{AutoAdvancedMatching:!1,Shopify:!0,JSBridge:!0,EventBuilderRuleEngine:!1,RemoveUnusedCode:!0}},{id:"MTc3NWUxZTAxMjM",map:{AutoAdvancedMatching:!0,Shopify:!0,JSBridge:!0,EventBuilderRuleEngine:!1,RemoveUnusedCode:!0}},{id:"MTc3NWUxZTAxMjQ",map:{AutoAdvancedMatching:!1,Shopify:!1,JSBridge:!1,EventBuilderRuleEngine:!0,RemoveUnusedCode:!0}},{id:"MTc3NWUxZTAxMjU",map:{AutoAdvancedMatching:!0,Shopify:!1,JSBridge:!1,EventBuilderRuleEngine:!0,RemoveUnusedCode:!0}},{id:"MTc3NWUxZTAxMjY",map:{AutoAdvancedMatching:!1,Shopify:!0,JSBridge:!1,EventBuilderRuleEngine:!0,RemoveUnusedCode:!0}},{id:"MTc3NWUxZTAxMjc",map:{AutoAdvancedMatching:!0,Shopify:!0,JSBridge:!1,EventBuilderRuleEngine:!0,RemoveUnusedCode:!0}},{id:"MTc3NWUxZTAxMjg",map:{AutoAdvancedMatching:!1,Shopify:!1,JSBridge:!0,EventBuilderRuleEngine:!0,RemoveUnusedCode:!0}},{id:"MTc3NWUxZTAxMjk",map:{AutoAdvancedMatching:!0,Shopify:!1,JSBridge:!0,EventBuilderRuleEngine:!0,RemoveUnusedCode:!0}},{id:"MTc3NWUxZTAxMzA",map:{AutoAdvancedMatching:!1,Shopify:!0,JSBridge:!0,EventBuilderRuleEngine:!0,RemoveUnusedCode:!0}},{id:"MTc3NWUxZTAxMzE",map:{AutoAdvancedMatching:!0,Shopify:!0,JSBridge:!0,EventBuilderRuleEngine:!0,RemoveUnusedCode:!0}}],e=(g()._static_map=c,a="https://analytics.tiktok.com/i18n/pixel/static/",null==(e=u={"info":{"pixelCode":"CLBPQIRC77UBB9N4UEG0","name":"DP Website TT Pixel","status":0,"setupMode":0,"partner":"","advertiserID":"7297303693299466242","is_onsite":false,"firstPartyCookieEnabled":true},"plugins":{"Shopify":false,"AdvancedMatching":{"email":true,"phone_number":true,"first_name":true,"last_name":true,"city":true,"state":true,"country":true,"zip_code":true},"AutoAdvancedMatching":null,"Callback":true,"Identify":true,"Monitor":true,"PerformanceInteraction":true,"WebFL":true,"AutoConfig":{"form_rules":null,"vc_rules":{}},"DiagnosticsConsole":true,"PangleCookieMatching":false,"CompetitorInsight":true,"EventBuilder":true,"EnrichIpv6":true,"HistoryObserver":{"dynamic_web_pageview":true},"RuntimeMeasurement":true,"JSBridge":true,"EventBuilderRuleEngine":true,"RemoveUnusedCode":true},"rules":[]})||null==(n=e.info)?void 0:n.pixelCode);function l(){return window&&window.TiktokAnalyticsObject||"ttq"}function g(){return window&&window[l()]}function r(e,n){n=g()[n];return n&&n[e]||{}}var v,A,n=g();n||(n=[],window&&(window[l()]=n)),Object.assign(u,{options:r(e,"_o")}),v=u,n._i||(n._i={}),(A=v.info.pixelCode)&&(n._i[A]||(n._i[A]=[]),Object.assign(n._i[A],v),n._i[A]._load=+new Date),Object.assign(u.info,{loadStart:r(e,"_t"),loadEnd:r(e,"_i")._load,loadId:n._li&&n._li[e]||""}),null!=(i=(d=n).instance)&&null!=(o=i.call(d,e))&&null!=(t=o.setPixelInfo)&&t.call(o,u.info),v=function(e,n,i){var t=0<arguments.length&&void 0!==e?e:{},u=1<arguments.length?n:void 0,e=2<arguments.length?i:void 0,n=function(e,n){for(var i=0;i<e.length;i++)if(n.call(null,e[i],i))return e[i]}(c,function(e){for(var i=e.map,n=Object.keys(i),d=function(e){var n;return"JSBridge"===e?"external"!==(null==(n=g()._env)?void 0:n.env)===i[e]:!(!t[e]||!u[e])===i[e]},o=0;o<n.length;o++)if(!d.call(null,n[o],o))return!1;return!0});return n?"".concat(e,"main.").concat(n.id,".js"):"".concat(e,"main.").concat(c[0].id,".js")}(n._plugins,u.plugins,a),A=e,(void 0!==self.DedicatedWorkerGlobalScope?self instanceof self.DedicatedWorkerGlobalScope:"DedicatedWorkerGlobalScope"===self.constructor.name)?self.importScripts&&self.importScripts(v):((i=document.createElement("script")).type="text/javascript",i.async=!0,i.src=v,i.setAttribute("data-id",A),(v=document.getElementsByTagName("script")[0])&&v.parentNode&&v.parentNode.insertBefore(i,v))}();
