!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t=(new e.Error).stack;t&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[t]="7f1abd14-b6b0-45b2-ae8c-dba56d623d6a",e._sentryDebugIdIdentifier="sentry-dbid-7f1abd14-b6b0-45b2-ae8c-dba56d623d6a")}catch(e){}}(),(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2130],{9587:(e,t,n)=>{"use strict";n.d(t,{Analytics:()=>d});var a=n(7620),r=n(40459),o=()=>{window.va||(window.va=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];(window.vaq=window.vaq||[]).push(t)})};function i(){return"undefined"!=typeof window}function s(){return"production"}function l(){return(i()?window.vam:s())||"production"}function c(){return"development"===l()}function d(e){return(0,a.useEffect)(()=>{var t;e.beforeSend&&(null==(t=window.va)||t.call(window,"beforeSend",e.beforeSend))},[e.beforeSend]),(0,a.useEffect)(()=>{var t;!function(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{debug:!0};if(!i())return;!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"auto";if("auto"===e){window.vam=s();return}window.vam=e}(t.mode),o(),t.beforeSend&&(null==(e=window.va)||e.call(window,"beforeSend",t.beforeSend));let n=t.scriptSrc?t.scriptSrc:c()?"https://va.vercel-scripts.com/v1/script.debug.js":t.basePath?"".concat(t.basePath,"/insights/script.js"):"/_vercel/insights/script.js";if(document.head.querySelector('script[src*="'.concat(n,'"]')))return;let a=document.createElement("script");a.src=n,a.defer=!0,a.dataset.sdkn="@vercel/analytics"+(t.framework?"/".concat(t.framework):""),a.dataset.sdkv="1.5.0",t.disableAutoTrack&&(a.dataset.disableAutoTrack="1"),t.endpoint?a.dataset.endpoint=t.endpoint:t.basePath&&(a.dataset.endpoint="".concat(t.basePath,"/insights")),t.dsn&&(a.dataset.dsn=t.dsn),a.onerror=()=>{let e=c()?"Please check if any ad blockers are enabled and try again.":"Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.";console.log("[Vercel Web Analytics] Failed to load script from ".concat(n,". ").concat(e))},c()&&!1===t.debug&&(a.dataset.debug="false"),document.head.appendChild(a)}({framework:e.framework||"react",basePath:null!=(t=e.basePath)?t:function(){if(void 0!==r&&void 0!==r.env)return r.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH}(),...void 0!==e.route&&{disableAutoTrack:!0},...e})},[]),(0,a.useEffect)(()=>{e.route&&e.path&&function(e){var t;let{route:n,path:a}=e;null==(t=window.va)||t.call(window,"pageview",{route:n,path:a})}({route:e.route,path:e.path})},[e.route,e.path]),null}},35999:e=>{e.exports=function(e){var t,n="";return n+('!function(){var analytics=window.analytics=window.analytics||[];if(!analytics.initialize)if(analytics.invoked)window.console&&console.error&&console.error("Segment snippet included twice.");else{analytics.invoked=!0;analytics.methods=["trackSubmit","trackClick","trackLink","trackForm","pageview","identify","reset","group","track","ready","alias","debug","page","once","off","on","addSourceMiddleware","addIntegrationMiddleware","setAnonymousId","addDestinationMiddleware"];analytics.factory=function(e){return function(){if(window.analytics.initialized)return window.analytics[e].apply(window.analytics,arguments);var i=Array.prototype.slice.call(arguments);i.unshift(e);analytics.push(i);return analytics}};for(var i=0;i<analytics.methods.length;i++){var key=analytics.methods[i];analytics[key]=analytics.factory(key)}analytics.load=function(key,i){var t=document.createElement("script");t.type="text/javascript";t.async=!0;t.src="https://'+(null==(t=e.host)?"":t)+(null==(t=e.ajsPath)?"":t)+'";var n=document.getElementsByTagName("script")[0];n.parentNode.insertBefore(t,n);analytics._loadOptions=i};analytics._writeKey="'+(null==(t=e.apiKey)?"":t)+'";'+(null==(t=e.optionalCDN)?"":t)+';analytics.SNIPPET_VERSION="4.16.1";\n'+(null==(t=e.load)?"":t)+"\n"+(null==(t=e.page)?"":t)+"\n}}();")}},37796:(e,t,n)=>{"use strict";n.d(t,{SpeedInsights:()=>f});var a=n(7620),r=n(62942),o=n(40459),i=()=>{window.si||(window.si=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];(window.siq=window.siq||[]).push(t)})};function s(){return"development"===function(){return"production"}()}function l(e){return new RegExp("/".concat(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"(?=[/?#]|$)"))}function c(e){(0,a.useEffect)(()=>{var t;e.beforeSend&&(null==(t=window.si)||t.call(window,"beforeSend",e.beforeSend))},[e.beforeSend]);let t=(0,a.useRef)(null);return(0,a.useEffect)(()=>{if(t.current)e.route&&t.current(e.route);else{var n,a;let r=function(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if("undefined"==typeof window||null===t.route)return null;i();let n=t.scriptSrc?t.scriptSrc:s()?"https://va.vercel-scripts.com/v1/speed-insights/script.debug.js":t.dsn?"https://va.vercel-scripts.com/v1/speed-insights/script.js":t.basePath?"".concat(t.basePath,"/speed-insights/script.js"):"/_vercel/speed-insights/script.js";if(document.head.querySelector('script[src*="'.concat(n,'"]')))return null;t.beforeSend&&(null==(e=window.si)||e.call(window,"beforeSend",t.beforeSend));let a=document.createElement("script");return a.src=n,a.defer=!0,a.dataset.sdkn="@vercel/speed-insights"+(t.framework?"/".concat(t.framework):""),a.dataset.sdkv="1.2.0",t.sampleRate&&(a.dataset.sampleRate=t.sampleRate.toString()),t.route&&(a.dataset.route=t.route),t.endpoint?a.dataset.endpoint=t.endpoint:t.basePath&&(a.dataset.endpoint="".concat(t.basePath,"/speed-insights/vitals")),t.dsn&&(a.dataset.dsn=t.dsn),s()&&!1===t.debug&&(a.dataset.debug="false"),a.onerror=()=>{console.log("[Vercel Speed Insights] Failed to load script from ".concat(n,". Please check if any content blockers are enabled and try again."))},document.head.appendChild(a),{setRoute:e=>{a.dataset.route=null!=e?e:void 0}}}({framework:null!=(n=e.framework)?n:"react",basePath:null!=(a=e.basePath)?a:function(){if(void 0!==o&&void 0!==o.env)return o.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH}(),...e});r&&(t.current=r.setRoute)}},[e.route]),null}var d=()=>{let e=(0,r.useParams)(),t=(0,r.useSearchParams)()||new URLSearchParams,n=(0,r.usePathname)();return e?function(e,t){if(!e||!t)return e;let n=e;try{let e=Object.entries(t);for(let[t,a]of e)if(!Array.isArray(a)){let e=l(a);e.test(n)&&(n=n.replace(e,"/[".concat(t,"]")))}for(let[t,a]of e)if(Array.isArray(a)){let e=l(a.join("/"));e.test(n)&&(n=n.replace(e,"/[...".concat(t,"]")))}return n}catch(t){return e}}(n,Object.keys(e).length?e:Object.fromEntries(t.entries())):null};function u(e){let t=d();return a.createElement(c,{route:t,...e,framework:"next",basePath:function(){if(void 0!==o&&void 0!==o.env)return o.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH}()})}function f(e){return a.createElement(a.Suspense,{fallback:null},a.createElement(u,{...e}))}},55013:e=>{e.exports=function(e){var t,n="";return n+("(function(){\n\n  // Create a queue, but don't obliterate an existing one!\n  var analytics = window.analytics = window.analytics || [];\n\n  // If the real analytics.js is already on the page return.\n  if (analytics.initialize) return;\n\n  // If the snippet was invoked already show an error.\n  if (analytics.invoked) {\n    if (window.console && console.error) {\n      console.error('Segment snippet included twice.');\n    }\n    return;\n  }\n\n  // Invoked flag, to make sure the snippet\n  // is never invoked twice.\n  analytics.invoked = true;\n\n  // A list of the methods in Analytics.js to stub.\n  analytics.methods = [\n    'trackSubmit',\n    'trackClick',\n    'trackLink',\n    'trackForm',\n    'pageview',\n    'identify',\n    'reset',\n    'group',\n    'track',\n    'ready',\n    'alias',\n    'debug',\n    'page',\n    'once',\n    'off',\n    'on',\n    'addSourceMiddleware',\n    'addIntegrationMiddleware',\n    'setAnonymousId',\n    'addDestinationMiddleware'\n  ];\n\n  // Define a factory to create stubs. These are placeholders\n  // for methods in Analytics.js so that you never have to wait\n  // for it to load to actually record data. The `method` is\n  // stored as the first argument, so we can replay the data.\n  analytics.factory = function(e){\n    return function(){\n      if (window.analytics.initialized) {\n        // Sometimes users assigned analytics to a variable before analytics is done loading, resulting in a stale reference.\n        // If so, proxy any calls to the 'real' analytics instance.\n        return window.analytics[e].apply(window.analytics, arguments);\n      }\n      var args = Array.prototype.slice.call(arguments);\n      args.unshift(e);\n      analytics.push(args);\n      return analytics;\n    };\n  };\n\n\n  // For each of our methods, generate a queueing stub.\n  for (var i = 0; i < analytics.methods.length; i++) {\n    var key = analytics.methods[i];\n    analytics[key] = analytics.factory(key);\n  }\n\n  // Define a method to load Analytics.js from our CDN,\n  // and that will be sure to only ever load it once.\n  analytics.load = function(key, options){\n    // Create an async script element based on your key.\n    var t = document.createElement('script');\n    t.type = 'text/javascript';\n    t.async = true;\n    t.src = \"https://"+(null==(t=e.host)?"":t)+(null==(t=e.ajsPath)?"":t)+"\";\n\n    // Insert our script next to the first script element.\n    var first = document.getElementsByTagName('script')[0];\n    first.parentNode.insertBefore(t, first);\n    analytics._loadOptions = options;\n  };\n  analytics._writeKey = '"+(null==(t=e.apiKey)?"":t)+"';\n\n  "+(null==(t=e.optionalCDN)?"":t)+"\n\n  // Add a version to keep track of what's in the wild.\n  analytics.SNIPPET_VERSION = '4.16.1';\n\n  // Load Analytics.js with your key, which will automatically\n  // load the tools you've enabled for your account. Boosh!\n  "+(null==(t=e.load)?"":t)+"\n\n  // Make the first page call to load the integrations. If\n  // you'd like to manually name or tag the page, edit or\n  // move this call however you'd like.\n  "+(null==(t=e.page)?"":t)+"\n})();\n")}},56636:e=>{"use strict";var t=Object.prototype.hasOwnProperty,n=String.prototype.charAt,a=Object.prototype.toString,r=function(e,t){return n.call(e,t)},o=function(e,n){return t.call(e,n)},i=function(e,t){t=t||o;for(var n=[],a=0,r=e.length;a<r;a+=1)t(e,a)&&n.push(String(a));return n},s=function(e,t){t=t||o;var n=[];for(var a in e)t(e,a)&&n.push(String(a));return n};e.exports=function(e){return null==e?[]:"[object String]"===a.call(e)?i(e,r):null!=e&&"function"!=typeof e&&"number"==typeof e.length?i(e,o):s(e)}},57182:(e,t,n)=>{"use strict";var a=n(56636),r=Object.prototype.toString,o=function(e){var t=typeof e;return"number"===t||"object"===t&&"[object Number]"===r.call(e)},i="function"==typeof Array.isArray?Array.isArray:function(e){return"[object Array]"===r.call(e)},s=function(e,t){for(var n=0;n<t.length&&!1!==e(t[n],n,t);n+=1);},l=function(e,t){for(var n=a(t),r=0;r<n.length&&!1!==e(t[n[r]],n[r],t);r+=1);};e.exports=function(e,t){return(null!=t&&(i(t)||"function"!==t&&o(t.length))?s:l).call(this,e,t)}},61728:e=>{e.exports={style:{fontFamily:"'seasonVFSerif', 'seasonVFSerif Fallback'",fontWeight:500,fontStyle:"normal"},className:"__className_d633a2",variable:"__variable_d633a2"}},87527:e=>{e.exports={style:{fontFamily:"'seasonVFSans', 'seasonVFSans Fallback'"},className:"__className_47b4cb",variable:"__variable_47b4cb"}},88622:(e,t,n)=>{"use strict";var a=n(95009),r=(n(55013),n(35999)),o=Object.prototype.hasOwnProperty;t.j=function(e){var t,n,i=((t=e)||(t={}),t.apiKey||(t.apiKey="YOUR_API_KEY"),t.host||(t.host="cdn.segment.com"),t.ajsPath||(t.ajsPath='/analytics.js/v1/" + key + "/analytics.min.js'),t.useHostForBundles||(t.useHostForBundles=!1),o.call(t,"page")||(t.page=!0),o.call(t,"load")||(t.load=!0),t);return i.load=function(e){if(!e.load)return"";if("boolean"!=typeof e.load){var t=JSON.stringify(e.load);return'analytics.load("'+e.apiKey+'", '+t+");"}return'analytics.load("'+e.apiKey+'");'}(i),i.page=function(e){if(!e)return"";var t=[];return e.category&&t.push(e.category),e.name&&t.push(e.name),e.properties&&t.push(e.properties),"analytics.page("+a(JSON.stringify,t).join(", ")+");"}(i.page),i.optionalCDN=(n=i)&&"boolean"==typeof n.useHostForBundles&&n.useHostForBundles?'analytics._cdn = "https://'+n.host+'"':"",r(i)}},95009:(e,t,n)=>{"use strict";var a=n(57182);e.exports=function(e,t){if("function"!=typeof e)throw TypeError("Expected a function but received a "+typeof e);var n=[];return a(function(t,a,r){n.push(e(t,a,r))},t),n}}}]);