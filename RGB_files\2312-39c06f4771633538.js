!function(){try{var e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},n=(new e.Error).stack;n&&(e._sentryDebugIds=e._sentryDebugIds||{},e._sentryDebugIds[n]="9a5f3f88-7982-4ca3-a109-023d38953b73",e._sentryDebugIdIdentifier="sentry-dbid-9a5f3f88-7982-4ca3-a109-023d38953b73")}catch(e){}}();"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2312],{126:(e,n,t)=>{t.d(n,{a:()=>v});var l=t(54568),a=t(7620),i=t(15523),r=t(85610),s=t.n(r);let o={Default:e=>s()("\n      border\n      border-deep-blue\n      flex\n      relative\n      rounded-10px\n      w-fit\n      isolate\n      font-bold\n      ".concat(e?"text-white":"text-deep-blue","\n    ")),Pricing:s()("\n    border-2\n    border-deep-blue\n    flex\n    relative\n    rounded-8px\n    w-fit\n    isolate\n    overflow-hidden\n    p-[2px]\n  ")},c={Default:(e,n)=>s()("\n      h-[57px]\n      my-[-1px]\n      px-4\n      py-[14.5px]\n      relative\n      z-[1]\n      rounded-10px\n\n      hover:bg-dp-purple\n      hover:text-white\n      ".concat(e?"\n        text-white\n        focus:bg-white\n        focus:rounded-10px\n        hover:text-white\n      ":"text-deep-blue","\n    ")),Pricing:(e,n)=>s()("\n    relative\n    px-8\n    py-3\n    w-[105px]\n    text-xs\n    font-bold\n    uppercase\n    text-center\n    flex\n    items-center\n    justify-center\n    transition-all\n    ".concat(n?"bg-deep-blue text-white rounded-8px":"bg-transparent text-deep-blue rounded-8px","\n  "))},d="\n  h-[57px]\n  -my-[1px]\n",u=s()("\n  ".concat(d,"\n  ml-[-1px]\n  hover:rounded-l-10px\n")),m=s()("\n  ".concat(d,"\n  mr-[-1px]\n  hover:rounded-r-10px\n")),p="\n  h-[29px]\n",h=s()("\n  ".concat(p,"\n  rounded-l-8px\n")),x=s()("\n  ".concat(p,"\n  rounded-r-8px\n")),g={Default:s()("\n    absolute\n    border-2\n    border-dp-purple\n    content-['']\n    h-[calc(100%+2px)]\n    w-[calc(100%+2px)]\n    translate-x-[-1px]\n    translate-y-[-1px]\n    rounded-10px\n    transition-all\n    z-[1]\n  "),Pricing:s()("\n    absolute\n    -left-full\n    scale-x-[96%]\n    scale-y-[90%]\n    bg-deep-blue\n    rounded-8px\n    transition-all\n  ")},f={border:e=>s()("\n      inset-0\n      ".concat(g[e],"\n    ")),toggle:function(e,n,t){let l=arguments.length>3&&void 0!==arguments[3]&&arguments[3],a=arguments.length>4&&void 0!==arguments[4]&&arguments[4];return s()("\n      ".concat("function"==typeof c[e]?c[e](n,t):c[e],"\n      ").concat("Pricing"===e?l?h:a?x:"":l?u:a?m:"","\n    "))},toggles:(e,n)=>s()("\n      relative\n      flex\n      ".concat("function"==typeof o[e]?o[e](n):o[e],"\n    "))},v=e=>{let{toggles:n,activeIndex:t,setActiveIndex:r,isDark:s,variant:o="Default",togglesWithIds:c,preRender:d=!1,"aria-label":u,"data-selector-name":m}=e,[p,h]=(0,a.useState)(void 0),[x,g]=(0,a.useState)([]),[v,b]=(0,a.useState)([]),[w,j]=(0,a.useState)([]),y=(0,a.useRef)(!1),N=(0,a.useRef)([]);(0,i.n)(()=>{g(N.current.map(e=>null==e?void 0:e.offsetWidth)),d&&N.current.length>0&&C()});let k=x.length>0?x[t]:void 0,C=()=>{let e=N.current.map(e=>null==e?void 0:e.offsetWidth);b(e);let n=[0];for(let t=1;t<e.length;t++)n[t]=(n[t-1]||0)+(e[t-1]||0);j(n),y.current=!0};return(0,a.useEffect)(()=>{g(N.current.map(e=>null==e?void 0:e.offsetWidth)),d&&N.current.length>0&&!y.current&&C()},[d]),(0,a.useEffect)(()=>{if(d)w.length>t&&void 0!==w[t]&&h(w[t]);else{let e=0;for(let n=0;n<=t;n++)e+=x[n-1]||0;h(e)}},[t,x,d,w]),(0,l.jsxs)("div",{className:f.toggles(o,s),role:"radiogroup","aria-label":u||"Toggle Options","data-selector-name":m,children:[d?void 0!==k&&void 0!==p&&(0,l.jsx)("span",{className:"".concat(f.border(o)," transition-all duration-200"),style:{left:p,width:v[t]||k}}):null!=k&&null!=p&&(0,l.jsx)("span",{className:f.border(o),style:{left:p,width:k}}),null==c?void 0:c.map((e,n)=>{let{id:a,label:i}=e,d=n===c.length-1;return(0,l.jsx)("button",{className:f.toggle(o,s,n===t,0===n,d),onClick:()=>r(n),ref:e=>{N.current[n]=e},id:a,"data-testid":a,role:"radio","aria-checked":n===t,"data-value":i,children:i},a)}),!c&&(null==n?void 0:n.map((e,a)=>{let i=a===n.length-1;return(0,l.jsx)("button",{className:f.toggle(o,s,a===t,0===a,i),onClick:()=>r(a),ref:e=>{N.current[a]=e},type:"button",role:"radio","aria-checked":a===t,"data-value":e,children:e},e)}))]})}},1398:(e,n,t)=>{t.d(n,{M:()=>l.TileCards});var l=t(64304)},1773:(e,n,t)=>{t.d(n,{TabsSection:()=>v});var l=t(54568),a=t(7620),i=t(61773),r=t(91039),s=t(40595),o=t(68808),c=t(31328),d=t(26268),u=t(85610),m=t.n(u);let p={White:"bg-white","Deep Blue":"bg-deep-blue text-white"},h={section:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"White";return m()("\n      ".concat(p[e]||p.White,"\n      py-[64px]\n      lg:py-[96px]\n  "))},container:m()("\n    container\n    mx-auto\n    px-4\n  "),inner:m()("\n    relative\n    mx-auto\n    flex\n    max-w-screen-xl\n    flex-col\n    gap-16\n    lg:gap-[96px]\n  "),tabsWrapper:e=>m()("\n    flex\n    flex-col\n    justify-between\n    gap-8\n    md:flex-row\n    md:items-center\n    ".concat("Both"===e?"lg:min-h-[620px]":"","\n  ")),tabsContainer:m()("\n    flex\n    flex-col\n    items-start\n    md:w-2/5\n  "),tabNav:m()("\n    flex\n    flex-col\n    gap-6\n    lg:gap-12\n    w-full\n  "),tabNavItem:m()("\n    w-full\n    cursor-pointer\n  "),tabNavContent:(e,n)=>m()("\n      flex\n      flex-col\n      gap-2\n      border-t-2\n      ".concat("Deep Blue"===e?n?"border-dark-tan":"border-ultra-dark-tan":"border-[#17100E]/20","\n      pt-2\n      lg:pt-3\n    ")),tabNavTitle:m()("\n    relative\n    flex\n    items-center\n    w-full\n  "),tabNavTitleImage:m()("\n    h-[40px]\n    w-auto\n    object-contain\n    block\n    ml-[-12px]\n  "),tabNavTitleActive:e=>m()("\n    ".concat("Deep Blue"===e?"text-ultra-light-purple":"text-dp-purple","\n  ")),tabNavTitleInactive:e=>m()("\n    ".concat("Deep Blue"===e?"text-white":"text-deep-blue","\n  ")),tabNavDesc:(e,n)=>m()("\n    p\n    ".concat("Deep Blue"===e?n?"text-white/80":"text-ultra-dark-tan":n?"text-deep-blue":"text-ultra-dark-tan","\n  ")),inactiveButton:m()("\n    text-ultra-dark-tan\n    border-b-ultra-dark-tan\n    hover:text-ultra-dark-tan\n    hover:border-b-ultra-dark-tan\n    hover:opacity-70\n    focus:text-ultra-dark-tan\n    focus:border-b-ultra-dark-tan\n    focus:opacity-70\n  "),inactiveButtonIcon:m()("\n    text-ultra-dark-tan\n    [&>path]:fill-ultra-dark-tan\n    [&>path]:stroke-ultra-dark-tan\n  "),tabImageWrapper:(e,n)=>{let t="\n      bg-primary-purple\n      relative\n      w-full\n      overflow-hidden\n      rounded-2xl\n    ";return"Both"===e?m()("\n        ".concat(t,"\n        md:w-1/2\n        flex\n        items-center\n        justify-center\n      ")):n?m()("\n        ".concat(t,"\n        md:w-1/2\n        lg:w-3/5\n      ")):m()("\n      ".concat(t,"\n      md:w-1/2\n    "))},tabImage:m()("\n    h-full\n    w-full\n    object-contain\n  "),progress:(e,n)=>m()("\n    absolute\n    left-0\n    ".concat("Deep Blue"===e&&"Both"===n?"top-[-10.5px] h-[3px] lg:top-[-14.5px]":"top-[-10.5px] h-0.5 lg:top-[-14.5px]","\n    ").concat("Deep Blue"===e&&"Both"===n?"bg-[linear-gradient(81deg,#FF9421_0%,#F8475E_33%,#F020B3_66%,#8636F8_90%)]":"Deep Blue"===e?"bg-ultra-light-purple":"bg-dp-purple","\n    transition-[width]\n    duration-[8000ms]\n    ease-linear\n    will-change-[width]\n  ")),progressReset:(e,n)=>m()("\n    absolute\n    left-0\n    ".concat("Deep Blue"===e&&"Both"===n?"top-[-10.5px] h-[3px] lg:top-[-14.5px]":"top-[-10.5px] h-0.5 lg:top-[-14.5px]","\n    ").concat("Deep Blue"===e&&"Both"===n?"bg-[linear-gradient(81deg,#FF9421_0%,#F8475E_33%,#F020B3_66%,#8636F8_90%)]":"Deep Blue"===e?"bg-ultra-light-purple":"bg-dp-purple","\n    transition-none\n    will-change-[width]\n  "))},x=(0,r.default)(()=>t.e(6818).then(t.t.bind(t,46818,23)),{loadableGenerated:{webpack:()=>[46818]},ssr:!1}),g=e=>{if(!e)return null;let n=e.match(/wistia\.com\/medias\/([a-zA-Z0-9]+)/);if(n)return n[1];let t=e.match(/wistia\.com\/embed\/(?:medias\/)?([a-zA-Z0-9]+)/);return t?t[1]:null},f=e=>!!e&&(null!==e.match(/\.(mp4|webm|ogg)$/i)||e.includes("wistia.com")),v=e=>{var n,t,r,u;let{leftTabItemsCollection:m,rightTabItemsCollection:p,tabPosition:v,tabsTitle:b,className:w="",backgroundColor:j="White",autoplay:y=!0}=e;console.log("TabsSection autoplay:",y);let[N,k]=(0,a.useState)("left"),C=[];"Left"===v&&(C=(null==m?void 0:m.items)||[]),"Right"===v&&(C=(null==p?void 0:p.items)||[]),"Both"===v&&(C="left"===N?(null==m?void 0:m.items)||[]:(null==p?void 0:p.items)||[]);let[T,B]=(0,a.useState)(0),[I,A]=(0,a.useState)(!1),[_,L]=(0,a.useState)(0),[P,D]=(0,a.useState)(0),[,S]=(0,a.useState)(!0),[W,E]=(0,a.useState)(!1),F=(0,a.useRef)(null),R=(0,a.useRef)(!1),M=(0,a.useRef)(null),O=(0,d.a8)(),H=(0,a.useRef)(null),U=(0,a.useRef)(N);(0,a.useEffect)(()=>{if(T!==_||"Both"===v&&U.current!==N){A(!0);let e=setTimeout(()=>{L(T),A(!1),"Both"===v&&(U.current=N)},300);return()=>clearTimeout(e)}},[T,_,N,v]);let z=(0,a.useCallback)(()=>{if(console.log("startProgressAnimation called, autoplay:",y,"activeTab:",T),F.current&&clearTimeout(F.current),M.current&&clearTimeout(M.current),S(!0),!y)return void console.log("Autoplay disabled, showing static bar");console.log("Starting animation from 0%"),E(!0),D(0),R.current=!1,setTimeout(()=>{E(!1)},50),F.current=setTimeout(()=>{console.log("Setting progress to 100%"),D(100),M.current=setTimeout(()=>{if(!R.current){var e,n;console.log("Auto-advancing to next tab"),R.current=!0,S(!1),D(0),"Both"===v&&(null==m||null==(e=m.items)?void 0:e.length)&&(null==p||null==(n=p.items)?void 0:n.length)?"left"===N?T<m.items.length-1?B(T+1):(k("right"),B(0)):T<p.items.length-1?B(T+1):(k("left"),B(0)):B(e=>(e+1)%C.length)}},8e3)},200)},[y,v,N,T,null==m||null==(n=m.items)?void 0:n.length,null==p||null==(t=p.items)?void 0:t.length,C.length]);(0,a.useEffect)(()=>(z(),()=>{F.current&&clearTimeout(F.current),M.current&&clearTimeout(M.current)}),[z]);let V=(e,n)=>{"Both"===v?n!==N||e!==T?(F.current&&clearTimeout(F.current),M.current&&clearTimeout(M.current),R.current=!0,E(!0),D(0),k(n),B(e)):(F.current&&clearTimeout(F.current),M.current&&clearTimeout(M.current),R.current=!0,z()):e!==T?(F.current&&clearTimeout(F.current),M.current&&clearTimeout(M.current),R.current=!0,E(!0),D(0),B(e)):(F.current&&clearTimeout(F.current),M.current&&clearTimeout(M.current),R.current=!0,z())},K=(0,a.useCallback)(()=>{if(H.current){var e;H.current.seekTo(0),null==(e=H.current.getInternalPlayer())||e.play()}},[]);(0,a.useEffect)(()=>{K()},[T,N,K]);let G=function(e){var n,t,a,r;let c=arguments.length>1&&void 0!==arguments[1]&&arguments[1],d=null==e||null==(n=e.image)?void 0:n.url;if(!d)return null;let u=g(d);return u?(0,l.jsx)("div",{className:c?"mb-4 w-full overflow-hidden rounded-lg":"",children:(0,l.jsx)(o.F,{wistiaId:u,size:"limited-width",isVideoCover:!1,preload:!0})}):f(d)?(0,l.jsx)("div",{className:"w-full cursor-pointer ".concat(c?"mb-4 overflow-hidden rounded-lg ".concat("Both"===v?"aspect-square":"aspect-video"):"h-full"),children:(0,l.jsx)(x,{ref:H,url:d,width:"100%",height:"100%",playing:!0,loop:!0,muted:!0,controls:!1,playsinline:!0,onPlay:()=>{(0,s.xu)({url:window.location.href,page_slug:O||"",video_title:(null==e?void 0:e.title)||"Video",event_type:"play"})},onPause:()=>{(0,s.xu)({url:window.location.href,page_slug:O||"",video_title:(null==e?void 0:e.title)||"Video",event_type:"pause"})},config:{file:{attributes:{style:{width:"100%",height:"100%",objectFit:"cover"},autoPlay:!0,loop:!0,muted:!0,playsInline:!0}}}})}):(0,l.jsx)("div",{className:c?"mb-4 w-full overflow-hidden rounded-lg":"",children:(0,l.jsx)(i.default,{src:d,alt:null==e||null==(t=e.image)?void 0:t.description,width:(null==e||null==(a=e.image)?void 0:a.width)||600,height:(null==e||null==(r=e.image)?void 0:r.height)||600,className:c?"w-full object-cover ".concat("Both"===v?"aspect-square":"h-auto"):h.tabImage,priority:!0})})},$=(e,n)=>(0,l.jsx)("div",{className:h.tabNav,children:e.map((e,t)=>{let a=n===N&&t===T;return(0,l.jsx)("div",{className:h.tabNavItem,onClick:()=>V(t,n),children:(0,l.jsxs)("div",{className:h.tabNavContent(j,a),children:[(0,l.jsx)("div",{className:"relative",children:n===N&&t===T&&(0,l.jsx)("div",{className:W?h.progressReset(j,v):h.progress(j,v),style:{width:y?"".concat(P,"%"):"100%"}})}),(0,l.jsx)("h2",{className:"".concat(h.tabNavTitle," ").concat(n===N&&t===T?h.tabNavTitleActive(j):h.tabNavTitleInactive(j)),children:e.titleImageActive&&e.titleImageInactive?(0,l.jsx)(i.default,{src:n===N&&t===T?e.titleImageActive.url:e.titleImageInactive.url,alt:n===N&&t===T?e.titleImageActive.description||e.titleImageActive.title||e.title:e.titleImageInactive.description||e.titleImageInactive.title||e.title,width:n===N&&t===T?e.titleImageActive.width||200:e.titleImageInactive.width||200,height:n===N&&t===T?e.titleImageActive.height||32:e.titleImageInactive.height||32,className:h.tabNavTitleImage,priority:!0}):e.title}),("Both"===v||n===N&&t===T)&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("p",{className:h.tabNavDesc(j,n===N&&t===T),children:e.description}),e.cta&&("Both"===v||n===N&&t===T)&&(0,l.jsx)(c.$n,{variant:"Link"===e.cta.ctaType?"tertiary":"primary",isDark:"Deep Blue"===j,href:e.cta.ctaUrl,className:"w-fit-content whitespace-nowrap ".concat(n!==N||t!==T?h.inactiveButton:""),iconClassName:n!==N||t!==T?h.inactiveButtonIcon:"",onClick:e.cta.onClick,children:e.cta.ctaText||"Learn More"},t),n===N&&t===T&&(0,l.jsx)("div",{className:"mt-4 md:hidden",children:G(e,!0)})]})]})},t)})});return(0,l.jsx)("section",{className:"".concat(h.section(j)," ").concat(w),children:(0,l.jsx)("div",{className:h.container,children:(0,l.jsx)("div",{className:h.inner,children:(0,l.jsxs)("div",{className:h.tabsWrapper(v),children:[("Left"===v||"Both"===v)&&(null==m||null==(r=m.items)?void 0:r.length)?(0,l.jsxs)("div",{className:h.tabsContainer,children:["Left"===v&&b&&(0,l.jsx)("h2",{className:"h1 mb-16",children:b}),$(m.items,"left")]}):null,"Both"===v&&b&&(0,l.jsx)("div",{className:"flex w-full flex-col items-center",children:(0,l.jsx)("h2",{className:"mb-4 text-center",children:b})}),(()=>{var e,n;let t=null==(n=C[_])||null==(e=n.image)?void 0:e.url,a=!!t&&(null!==g(t)||f(t));return(0,l.jsx)("div",{className:"".concat(h.tabImageWrapper(v,a)," hidden md:block"),children:(0,l.jsx)("div",{className:"transition-opacity duration-300 ".concat(I?"opacity-0":"opacity-100"),onClick:K,children:C[_]?G(C[_]):(0,l.jsx)("div",{className:"bg-primary-purple flex h-full w-full items-center justify-center",children:(0,l.jsx)("div",{className:"h-8 w-8 animate-spin rounded-full border-2 border-white border-t-transparent"})})})})})(),("Right"===v||"Both"===v)&&(null==p||null==(u=p.items)?void 0:u.length)?(0,l.jsxs)("div",{className:h.tabsContainer,children:["Right"===v&&b&&(0,l.jsx)("h2",{className:"mb-4",children:b}),$(p.items,"right")]}):null]})})})})}},2886:(e,n,t)=>{t.d(n,{y:()=>r});var l=t(13868),a=t(46379);let i=["monthly","annual"],r=(e,n,t)=>{let r=e||a.f7;return(0,l.A)(a.f7,r)[n][i[t]]}},5742:(e,n,t)=>{t.r(n),t.d(n,{IndustryValidation:()=>p});var l=t(54568),a=t(85906),i=t(7620),r=t(68061),s=t(5474),o=t(71034),c=t(85610),d=t.n(c);let u={container:"container",content:d()("\n    grid grid-cols-16\n    gap-x-4 sm:gap-x-8\n    max-w-content-max px-4 lg:px-8 lg:mx-auto\n    "),description:d()("\n    col-span-full\n    mb-[30px]\n    mt-3\n    paragraph\n    text-left\n    row-start-2\n\n    sm:w-[calc(100%-13px)]\n\n    lg:mb-[19px]\n    lg:mt-4\n    lg:col-span-9\n    lg:w-[calc(100%-6px)]\n  "),figure:(e,n)=>d()("\n      col-span-full\n      overflow-hidden\n      relative\n      transition-all\n\n      row-start-3\n\n      ".concat(n?"\n          opacity-100\n        ":"\n          hidden\n          opacity-0\n        ","\n\n      ").concat(e?"\n            \n            lg:col-start-7\n            col-end-13\n            mr-[68px]\n            md:mr-[92px]\n            lg:mr-[-2px]\n            mt-[15%]\n            md:mt-[96px]\n            lg:mt-[107px]\n          ":"\n            \n            col-start-5\n            lg:col-start-11\n            col-end-17\n            mr-0\n            ml-auto\n            md:w-[calc(100%-93px)]\n            lg:w-[calc(100%)]\n          ","\n    ")),image:"rounded-[10px] sm:rounded-[27px] md:rounded-[30px]",industry:d()("\n    \n    h2-mobile\n\n    lg:h2-desktop\n\n    [&:not(:has(a)):not(:last-child)]:mb-[18px]\n    [&:has(a):not(:last-child)]:mb-[27px]\n  "),link:e=>d()("\n      border-b-[2.5px]\n      flex\n      items-center\n      pb-2\n      transition-colors\n      w-fit\n\n      ".concat(e?"\n        border-b-dp-purple\n        text-dp-purple\n      ":"\n        border-b-transparent\n      ","\n    ")),linkArrowIcon:e=>d()("\n      h-[20px]\n      w-[20px]\n      ml-[18px]\n      transition-all\n\n      lg:h-[30px]\n      lg:w-[30px]\n\n      ".concat(e?"\n          opacity-100\n          visible\n        ":"\n          invisible\n          opacity-0\n        ","\n    ")),lists:d()("\n    col-span-full\n    mt-6\n\n    lg:col-span-6\n    md:row-start-4\n    lg:row-start-3\n    lg:pt-[126px]\n    lg:mt-0\n    empty:hidden\n  "),title:d()("\n    col-span-full\n    \n    h1-mobile\n    text-left\n\n    sm:w-[calc(100%-13px)]\n\n    lg:h1-desktop\n    lg:col-span-9\n    lg:w-[calc(100%-6px)]\n  ")},m=e=>{var n,t,a,i,r;let{imageWithFocalArea:o,isActive:c,isLandScape:d=!1,isHidden:m}=e,p={landscape:{mobile:{width:120,height:114},tablet:{width:428,height:314},desktop:{width:460,height:352}},portrait:{mobile:{width:188,height:187},tablet:{width:427,height:515},desktop:{width:461,height:576}}},h=d?p.landscape:p.portrait;return(null==o||null==(n=o.image)?void 0:n.url)&&(0,l.jsx)("div",{"aria-hidden":m,className:u.figure(d,!m&&c),children:(0,l.jsx)(s.s,{src:null==o||null==(t=o.image)?void 0:t.url,alt:null==o||null==(a=o.image)?void 0:a.title,width:null==o||null==(i=o.image)?void 0:i.width,height:null==o||null==(r=o.image)?void 0:r.height,artDirection:h,focusArea:null==o?void 0:o.focusArea,className:u.image})})},p=e=>{let{title:n,description:t,industries:s}=e,[c,d]=(0,i.useState)(0),{isLg:p}=(0,a.d)(),h=(null==s?void 0:s.items)||[];return h.length>0?(0,l.jsx)("section",{className:u.container,"data-section":"industry-validation",children:(0,l.jsxs)("div",{className:u.content,children:[(0,l.jsx)("h2",{className:u.title,children:n}),(0,l.jsx)("p",{className:u.description,children:t}),h.map((e,n)=>{let{portraitWithFocal:t}=e;return(0,l.jsx)(m,{isActive:n===c,isHidden:p,imageWithFocalArea:t},n)}),h.map((e,n)=>{let{landscapeWithFocal:t}=e;return(0,l.jsx)(m,{isLandScape:!0,isActive:n===c,isHidden:p,imageWithFocalArea:t},n)}),(0,l.jsx)("ul",{className:u.lists,children:h.map((e,n)=>{let{url:t,label:a}=e;return(0,l.jsx)("li",{className:u.industry,children:(0,l.jsxs)(o.N,{className:u.link(n===c),href:t||"#",onMouseOver:()=>d(n),children:[a,(0,l.jsx)(r.Hk,{className:u.linkArrowIcon(n===c)})]})},n)})}),h.map((e,n)=>{let{portraitWithFocal:t}=e;return(0,l.jsx)(m,{isActive:n===c,isHidden:!p,imageWithFocalArea:t},n)}),h.map((e,n)=>{let{landscapeWithFocal:t}=e;return(0,l.jsx)(m,{isLandScape:!0,isActive:n===c,isHidden:!p,imageWithFocalArea:t},n)})]})}):null}},6279:(e,n,t)=>{t.d(n,{A:()=>d});var l=t(54568),a=t(31328),i=t(85610),r=t.n(i);let s={container:"lg:container sticky md:-top-[20px] xs:-top-[34px] -top-[41px] z-10 bg-lighter-tan pr-11",content:"grid-container",title:r()("\n    font-semibold\n    col-span-full\n    max-w-[200px]\n    my-5\n\n    text-[clamp(16px,5vw,30px)]\n  "),controls:r()("\n    min-w-[250px]\n    w-[250px]\n    max-w-[250px]\n  "),toggle:r()("\n    mb-[35px]\n    w-max\n  "),toggleHelperText:r()("\n    paragraph-sm\n    mt-[5px]\n    flex\n    w-full\n    justify-around\n    text-center\n    font-semibold\n    [&>*]:flex-1\n  "),dropdown:r()("\n    mb-[58px]\n    w-full\n    max-w-[212px]\n  "),copyText:r()("\n    h4-desktop\n    mb-4\n    \n    font-semibold\n    max-w-[212px]\n  "),thickSpacer:r()("\n  h-[2px]\n  w-full\n  bg-black\n  block\n  my-8\n"),pricingCardsContainer:r()("\n    flex\n    gap-0\n    flex-1\n    border \n    border-gainsboro\n    rounded-t-[15px]\n  ")},o={pricingCard:r()("\n    w-[250px]\n    flex\n    flex-col\n    items-center\n    col-start-2\n    px-4\n  "),title:r()("\n    \n    pb-[10px]\n    mb-[10px]\n    w-full\n    text-center\n  "),thinSpacer:r()("\n    h-[1px]\n    w-full\n    bg-black\n    block\n    my-1\n  "),line:r()("\n  h-1\n  w-12\n  my-8\n"),contactUs:r()("\n  \n  text-[clamp(16px, 4vw, 20px)]\n  leading-tight\n  color-black\n  mb-1.5\n"),price:r()("\n  \n  mb-[10px]\n  w-full\n  text-center\n  text-[clamp(16px,5vw,30px)]\n  "),period:r()("\n    paragraph-desktop\n    font-semibold\n  "),normalText:r()("\n    mt-1.5\n  "),thickSpacer:r()("\n    h-[2px]\n    w-full\n    bg-black\n    block\n    my-8\n "),featuresTitle:r()("\n    font-semibold\n    !text-base\n    !mb-[5px]\n "),ctaCollectionContainer:r()("\n  mt-[25px]\n  flex\n  items-center\n  gap-6\n  ")},c=e=>{let{price:n,isLastCard:t}=e;return(0,l.jsx)("div",{className:o.pricingCard,children:(0,l.jsx)("div",{className:"flex w-full flex-col items-center py-[23px] ".concat(t?"":"border-r"," border-gainsboro"),children:n&&(0,l.jsx)("h3",{className:o.price,children:n})})})},d=e=>{var n;let{id:t,title:i,compareCTA:r,tierCards:o,copyText:d,activeToggleIndex:u}=e;return(0,l.jsx)("div",{className:s.container,"data-testid":t,children:(0,l.jsxs)("div",{className:"flex !px-0",children:[(0,l.jsxs)("div",{className:s.controls,children:[i&&(0,l.jsx)("h2",{className:s.title,children:i}),d&&(0,l.jsx)("h4",{className:s.copyText,children:d}),(null==r?void 0:r.ctaUrl)&&(0,l.jsx)(a.$n,{href:r.ctaUrl,variant:(null==r?void 0:r.ctaType)==="Link"?"link":"secondary",icon:(null==r?void 0:r.ctaType)==="Link"?"link-arrow":"",size:"small",children:r.ctaText})]}),(0,l.jsx)("div",{className:s.pricingCardsContainer,"data-pricing-feature-cards":!0,children:null==o||null==(n=o.items)?void 0:n.map((e,n)=>{var t;return(0,l.jsx)(c,{...e,activeToggleIndex:u,isLastCard:n===(null==o||null==(t=o.items)?void 0:t.length)-1},n)})})]})})}},6292:(e,n,t)=>{t.r(n),t.d(n,{CarouselImage:()=>o});var l=t(54568),a=t(62270),i=t(57515),r=t(85610);let s={container:"container",content:t.n(r)()("\n    grid-container\n    [&_div.swiper-slide_img]:h-full\n    [&_div.swiper-slide_img]:w-full\n  ")},o=e=>{let{id:n,sys:t,imagesWithFocalArea:r}=e,o=null==t?void 0:t.id,c=[...(null==r?void 0:r.items)||[]],d={...(0,i.O)(o),slidesPerView:1,breakpoints:{768:{spaceBetween:32}}};return(0,l.jsx)("section",{className:s.container,"data-testid":n,children:(0,l.jsx)("div",{className:s.content,children:c&&c.length>0&&(0,l.jsx)(a.l,{id:o,type:"image",images:c,hasControls:!0,options:d,artDirection:{mobile:{width:256,height:192},tablet:{width:520,height:320},desktop:{width:870,height:480}}})})})}},10193:(e,n,t)=>{t.d(n,{t:()=>l});let l={White:"bg-white text-black",Black:"bg-black text-white","Dialpad Purple":"bg-dp-purple text-white","Eerie Black":"bg-eerie-black text-white",Tan:"bg-tan text-black"}},11354:(e,n,t)=>{t.r(n),t.d(n,{CarouselCustomerReviews:()=>j});var l=t(54568),a=t(7620),i=t(4963),r=t(31328),s=t(41066),o=t(68061),c=t(71034),d=t(85610),u=t.n(d);let m={card:e=>e,cite:u()("\n    block\n    h5-mobile\n    not-italic\n    text-eerie-black\n\n    [&:not(:last-child)]:mt-6\n  "),description:u()("\n    mt-6\n    paragraph-mobile\n\n    md:paragraph-desktop\n  "),heading:u()("\n    font-semibold\n    mt-6\n    paragraph-mobile\n\n    md:paragraph-desktop\n  "),star:e=>u()("\n      h-28px\n      w-28px\n\n      ".concat(e&&"[&>path]:fill-ultra-light-purple","\n    ")),stars:u()("\n    flex\n    items-center\n    gap-1\n  "),hyperlink:u()("\n    text-dp-purple\n    hover:underline\n    hover:underline-offset-4\n  ")},p=()=>({renderMark:{[i.MARKS.BOLD]:e=>(0,l.jsx)("strong",{children:e}),[i.MARKS.ITALIC]:e=>(0,l.jsx)("em",{children:e}),[i.MARKS.UNDERLINE]:e=>(0,l.jsx)("u",{children:e}),[i.MARKS.SUBSCRIPT]:e=>(0,l.jsx)("sub",{children:e}),[i.MARKS.SUPERSCRIPT]:e=>(0,l.jsx)("sup",{children:e})},renderNode:{[i.BLOCKS.PARAGRAPH]:(e,n)=>(0,l.jsx)("p",{children:n}),[i.INLINES.HYPERLINK]:(e,n)=>(0,l.jsx)(c.N,{className:m.hyperlink,href:e.data.uri,target:"_blank",rel:"noopener noreferrer",children:n})}}),h=e=>{let{author:n,cite:t,className:i,description:r,heading:c,score:d}=e,u=(0,a.useId)();return(0,l.jsxs)("article",{className:m.card(i),children:[(0,l.jsx)("div",{"aria-label":"Rating: ".concat(d," out of 5"),className:m.stars,role:"img",children:Array.from({length:5}).map((e,n)=>d-n<1&&d-n>0?(0,l.jsx)(o.Pf,{className:m.star(!1),id:u+n},n):(0,l.jsx)(o.Gg,{"aria-hidden":!0,className:m.star(n>=d)},n))}),(0,l.jsx)("h3",{className:m.heading,children:c}),r&&(0,l.jsx)("div",{className:m.description,children:(0,l.jsx)(s.default,{contentBody:r,options:p})}),(0,l.jsx)("cite",{className:m.cite,children:n}),(0,l.jsx)("cite",{className:m.cite,children:t})]})};var x=t(62270),g=t(57515),f=t(90005);let v={White:"bg-white",Tan:"bg-tan"},b={container:e=>u()("\n    container\n\n    ".concat(v[e],"\n    ")),content:"grid-container",textBlock:u()("\n    col-span-full\n    p-0\n    mb-8\n\n    md:mb-[43px]\n\n    lg:mb-8\n    lg:col-span-7\n  "),textTitle:u()("\n    h2-mobile\n    p-0\n    mb-3\n\n    md:h2-desktop\n\n    lg:mb-1.5\n  "),textDescription:u()("\n    paragraph-mobile\n    p-0\n    mb-[26px]\n\n    md:mb-[23px]\n    md:paragraph-desktop\n\n    lg:mb-[25px]\n  "),ctaContainer:u()("\n    p-0\n    mb-0\n  "),carouselBlock:u()("\n    col-span-full\n    overflow-hidden\n    [&_.custom-controls]:mt-0\n    [&_.custom-controls]:mb-[30px]\n\n    md:[&_.custom-controls]:mb-[31px]\n\n    lg:[&_.custom-controls]:mb-[32px]\n  "),hyperlink:u()("\n    text-dp-purple\n    hover:underline\n    hover:underline-offset-4\n  ")},w=()=>({renderMark:{[i.MARKS.BOLD]:e=>(0,l.jsx)("strong",{children:e}),[i.MARKS.ITALIC]:e=>(0,l.jsx)("em",{children:e}),[i.MARKS.UNDERLINE]:e=>(0,l.jsx)("u",{children:e}),[i.MARKS.SUBSCRIPT]:e=>(0,l.jsx)("sub",{children:e}),[i.MARKS.SUPERSCRIPT]:e=>(0,l.jsx)("sup",{children:e})},renderNode:{[i.BLOCKS.PARAGRAPH]:(e,n)=>(0,l.jsx)("p",{children:n}),[i.INLINES.HYPERLINK]:(e,n)=>(0,l.jsx)(c.N,{className:b.hyperlink,href:e.data.uri,target:"_blank",rel:"noopener noreferrer",children:n})}}),j=e=>{var n,t;let{sys:i,id:o,backgroundColor:c="White",title:d="",description:u,ctas:m,customerReviewCards:p}=e,v=null==i?void 0:i.id,j={...(0,g.O)(v),spaceBetween:32,slidesPerView:1,slidesPerGroup:1,breakpoints:{768:{slidesPerView:1.6,slidesPerGroup:1},1088:{slidesPerView:3,slidesPerGroup:3}}},y=null==p||null==(n=p.items)?void 0:n.map((e,n)=>(0,a.createElement)(h,{...e,key:n}));return(0,l.jsx)("section",{className:b.container(c),"data-testid":o,"data-section":"customer-reviews-cards",children:(0,l.jsxs)("div",{className:b.content,children:[(0,l.jsxs)("div",{className:b.textBlock,children:[d&&(0,l.jsx)("p",{className:b.textTitle,children:d}),u&&(0,l.jsx)("div",{className:b.textDescription,children:(0,l.jsx)(s.default,{contentBody:u,options:w})}),m&&(null==m||null==(t=m.items)?void 0:t.length)>0&&(0,l.jsx)("div",{className:b.ctaContainer,children:m.items.map((e,n)=>(0,l.jsx)(r.$n,{href:(0,f.TE)(e.ctaUrl,e.pageLink),variant:{button:"primary",link:"link"}["".concat("Button"===e.ctaType?"button":"link")],icon:"Link"===e.ctaType?"link-arrow":"",type:"button",children:e.ctaText},n))})]}),(0,l.jsx)("div",{className:b.carouselBlock,children:(0,l.jsx)(x.l,{id:v,type:"cards",cards:y,options:j,controlPosition:"topRight"})})]})})}},12125:(e,n,t)=>{t.r(n),t.d(n,{CarouselAwards:()=>m});var l=t(54568),a=t(31328),i=t(62270),r=t(57515),s=t(90005),o=t(85610),c=t.n(o);let d={White:"bg-white",Tan:"bg-tan"},u={container:e=>c()("\n      container\n\n      ".concat(d[e],"\n    ")),content:"grid-container",textBlock:c()("\n    col-span-full\n    p-0\n    mb-[40px]\n\n    md:col-span-4\n    md:mb-[21px]\n\n    lg:mb-0\n  "),textTitle:c()("\n    h2-mobile\n    p-0\n    mb-1.5\n\n    lg:h2-desktop\n  "),textDescription:c()("\n    paragraph-mobile\n    p-0\n    mb-8\n\n    md:paragraph-desktop\n\n    lg:mb-[38px]\n  "),ctaContainer:c()("\n    p-0\n    mb-0\n  "),carouselBlock:c()("\n    col-span-full\n    [&_.custom-controls]:mb-[13px]\n    [&_.custom-controls]:mt-0\n    [&_.swiper-slide]:w-full\n    [&_.swiper-slide]:max-w-full\n\n    md:[&_.custom-controls]:mb-8\n    md:[&_.swiper-pagination-custom]:text-xs\n    md:[&_.swiper-slide]:w-auto\n\n    lg:col-start-5\n    lg:col-span-12\n    lg:overflow-hidden\n    lg:[&_.custom-controls]:mt-5\n    lg:[&_.custom-controls]:mb-[51px]\n    lg:[&_.swiper-pagination-custom]:text-base\n  "),carouselImageContainer:c()("\n    flex justify-center items-center xs:!h-[110px] md:!h-[128px]\n    [&_picture]:max-w-[320px] [&_picture]:w-fit [&_picture]:h-full\n    [&_img]:max-w-[320px] [&_img]:max-h-[118px] [&_img]:w-auto [&_img]:h-auto [&_img]:object-contain\n  "),hyperlink:c()("\n    text-dp-purple\n    hover:underline\n    hover:underline-offset-4\n  ")},m=e=>{let{id:n,sys:t,backgroundColor:o="White",title:c,description:d,cta:m,images:p}=e,h=null==t?void 0:t.id,x={...(0,r.O)(h),slidesPerView:1,breakpoints:{485:{slidesPerView:1,spaceBetween:40},768:{spaceBetween:40,slidesPerView:"auto"},1088:{spaceBetween:30,slidesPerView:"auto"}}},g=null==p?void 0:p.items.map(e=>{var n,t,l;return{image:{...e,title:null!=(n=e.altText)?n:"",width:null!=(t=e.width)?t:1,height:null!=(l=e.height)?l:1}}});return(0,l.jsx)("section",{className:u.container(o),"data-testid":n,"data-section":"carousel-awards",children:(0,l.jsxs)("div",{className:u.content,children:[(0,l.jsxs)("div",{className:u.textBlock,children:[c&&(0,l.jsx)("p",{className:u.textTitle,children:c}),d&&(0,l.jsx)("p",{className:u.textDescription,children:d}),((null==m?void 0:m.ctaUrl)||(null==m?void 0:m.pageLink))&&m.ctaText&&(0,l.jsx)("div",{className:u.ctaContainer,children:(0,l.jsx)(a.$n,{href:(0,s.TE)(m.ctaUrl,m.pageLink),variant:"Button"===m.ctaType?"primary":"link",icon:"Link"===m.ctaType?"link-arrow":"",type:"button",children:m.ctaText})})]}),p&&(null==p?void 0:p.items.length)>0&&(0,l.jsx)("div",{className:u.carouselBlock,children:(0,l.jsx)(i.l,{id:h,type:"image",images:g,hasControls:!0,options:x,controlPosition:"topRight",containerClassName:u.carouselImageContainer,artDirection:{mobile:{height:110},tablet:{height:128},desktop:{height:128}}})})]})})}},14855:(e,n,t)=>{t.r(n),t.d(n,{FormLayouts:()=>p});var l=t(54568),a=t(7620),i=t(80460),r=t(36561),s=t(79900),o=t(68808),c=t(41066),d=t(85610),u=t.n(d);let m={container:u()("\n      container\n    "),content:"grid-container !grid-cols-4 md:!grid-cols-16 lg:!grid-cols-16",copyBelow:"col-span-full mt-6",videoContainer:u()("\n        relative\n        col-span-full\n        xs:col-span-full\n        overflow-hidden\n        h-[205px] \n        md:h-[351px]\n        lg:w-[624px]\n        lg:col-start-9 \n        lg:col-span-8\n      "),videoThumbnail:u()("\n        rounded-[30px]\n        md:w-[624px]\n        md:h-[351px]\n        w-[366px]\n        h-[205px]\n  "),details:e=>u()("\n\n    ".concat(e?"":"flex flex-col justify-center items-start","\n    md:col-span-7\n    md:col-start-10\n    col-span-12\n    relative\n  ")),buttonContainer:u()("\n      flex\n      gap-6\n      items-center\n      w-full\n      justify-center\n      sm:justify-start\n    "),figure:u()("\n    md:col-span-8\n    col-span-12\n    mt-8\n    overflow-hidden\n    relative\n    md:mt-0\n    md:-order-1\n\n  "),form:'[&_button[type="submit"]]:w-auto',formWrapper:"mt-8 md:pt-3"},p=e=>{var n;let{id:t,description:d,eyebrow:u,marketoForm:p,title:h,copyBelow:x,videoId:g}=e,f=null==p?void 0:p.id,v=(0,a.useContext)(i.GatedAssetsContext).unGated||!1;return(0,l.jsx)("section",{className:"form-layouts container","data-section":"form-layouts","data-testid":t,children:(0,l.jsxs)("div",{className:m.content,children:[(0,l.jsx)("div",{className:m.details(!!f),children:!v&&f&&(0,l.jsx)("div",{className:m.formWrapper,children:(0,l.jsx)(r.E,{formId:p.id,variant:"hero",customThankYouPageLink:null!=(n=null==p?void 0:p.customThankYouPageLink)?n:void 0})})}),(0,l.jsxs)("div",{className:m.figure,children:[(0,l.jsx)(s.K,{description:d,eyebrow:u,title:h,variant:"poster"}),g&&(0,l.jsx)("div",{className:m.videoContainer,children:(0,l.jsx)(o.F,{className:m.videoThumbnail,wistiaId:g,isVideoCover:!0,preload:!0,autoPlayInView:!0,isHomeHero:!0,thumbnail:{src:"",width:"624",height:"351",alt:""}})}),x&&(0,l.jsx)("div",{className:m.copyBelow,children:(0,l.jsx)(c.default,{contentBody:x})})]})]})})}},19422:(e,n,t)=>{t.r(n),t.d(n,{GridCard:()=>b});var l=t(54568),a=t(7620),i=t(31328),r=t(5474),s=t(48258),o=t(79900),c=t(90005),d=t(1398),u=t(85610),m=t.n(u);let p={White:"bg-white",Black:"bg-deep-blue dark",Tan:"bg-tan",Purple:"bg-dp-purple dark","Eerie Black":"bg-eerie-black dark","Deep Blue":"bg-deep-blue dark","Ultra Dark Purple":"bg-ultra-dark-purple dark"},h={twoUp:"\n    md:w-[48.7%]\n    !px-0\n    max-w-[416px]\n  ",threeUp:"\n    lg:w-[calc(33.33%)]\n    !px-0\n    max-w-[416px]\n  ",fourUp:"\n    lg:w-[calc(25%)]\n    !px-0\n  ","2 Col":"\n    md:w-[48.7%]\n    !px-0\n  ","3 Col":"\n    lg:w-[calc(32%)]\n    md:w-[48.7%]\n    !px-0\n  ","4 Col":"\n    lg:w-[calc(23.40%)]\n    md:w-[48.7%]\n    !px-0\n  ",Responsive:"\n    lg:w-[calc(25%)]\n    md:w-[calc(50%)]\n    xs:w-[calc(100%)]\n    lg:px-4\n  "},x={twoUp:"\n  ",threeUp:"\n    lg:h-[416px]\n  ",fourUp:"\n    lg:h-[288px]\n  ","2 Col":"\n  ","3 Col":"\n\n  ","4 Col":"\n    lg:h-[288px]\n  "},g={twoUp:"\n    gap-4\n  ",threeUp:"\n    gap-4\n  ",fourUp:"\n    gap-2\n  ","2 Col":"\n    gap-4 xl:gap-8\n  ","3 Col":"\n    gap-4 xl:gap-6\n  ","4 Col":"\n    gap-4 xl:gap-6\n  ",Responsive:"\n    gap-2\n  "},f={outerSection:(e,n)=>m()("\n    ".concat("Product"===n?"py-[64px] lg:py-[96px]":"","\n    ").concat(p[e],"\n  ")),outerContainer:m()("\n    content-container\n  "),outerContent:m()("\n    relative\n    mx-auto\n    max-w-[1280px]\n  "),container:e=>m()("\n    container\n\n    ".concat(p[e],"\n  ")),content:e=>m()("\n    grid\n    grid-cols-4\n    \n    max-w-content-max\n    mx-auto\n    px-0\n    lg:mb-0\n\n    xs:grid-cols-8\n    xs:gap-x-8\n\n    lg:grid-cols-16\n    ".concat("Product"===e?"gap-16":"","\n  ")),cardContainer:(e,n,t)=>m()("\n    flex\n    flex-wrap\n    col-start-1\n    col-end-[-1]\n    m-width-[1280px]\n    mx-auto\n    w-full\n    ".concat("Grid Cards"===e&&"-mx-4 justify-center","\n    ").concat("Tile Cards"===e&&"-mx-8 justify-between","\n    ").concat(n&&g[n]?g[n]:"","\n    ").concat("Product"!==t?"mt-16":"","\n  ")),card:(e,n,t)=>m()("\n      flex\n      flex-col\n      w-full\n      pb-16\n      px-[32px]\n      ".concat(t?"px-[16px]":"","\n\n      dark:text-white\n\n      ").concat(e&&"\n          [&_a:focus_path]:fill-ultra-light-purple\n          [&_a:focus]:text-white\n          [&_a:focus]:border-b-white\n        ","\n\n      ").concat(h[n],"\n")),cardIcon:m()("\n    h-[54px]\n    w-auto\n    mb-[18px]\n    object-contain\n    self-start\n  "),cardImage:(e,n,t)=>m()("\n      relative\n      mb-[32px]\n\n      ".concat(t?x[e]:"","\n\n      ").concat(n?"h-[350px] xs:h-[420px] md:h-[440px] lg:h-[350px] md:[&>img]:!object-cover lg:[&>img]:!h-auto ":"","\n  "))},v=e=>{var n,t,a,i,s,d,u,m,p,h,x,g,v,b,w,j,y,N;let{layout:k="2 Col",isBgPurple:C,isDark:T,gridCard:B,is404Page:I=!1,gridCardImageStrategy:A="Tall",isTitleH2:_,isBgDeepBlue:L,displayStyle:P="Default"}=e,D=!!B.cardCTALink,S=D&&("Button"===B.cardCTALink.ctaType||!B.cardCTALink.videoModal);return(0,l.jsxs)("div",{className:f.card(C,k,S),"data-section":"grid-cards-item",children:[B.isIcon?B.gridItemImageWithFocalArea&&(0,l.jsx)(r.s,{"aria-hidden":!0,alt:null!=(x=null==(n=B.gridItemImageWithFocalArea)?void 0:n.image.title)?x:"",src:null!=(g=null==(t=B.gridItemImageWithFocalArea)?void 0:t.image.url)?g:"",width:null!=(v=null==(a=B.gridItemImageWithFocalArea)?void 0:a.image.width)?v:0,height:null!=(b=null==(i=B.gridItemImageWithFocalArea)?void 0:i.image.height)?b:0,className:f.cardIcon}):B.gridItemImageWithFocalArea&&(0,l.jsx)("div",{className:f.cardImage(k,I,"Tall"===A),children:(0,l.jsx)(r.s,{alt:null!=(w=null==(s=B.gridItemImageWithFocalArea)?void 0:s.image.title)?w:"",artDirection:{"2 Col":{desktop:{width:936,height:720},tablet:{width:936,height:720},mobile:{width:936,height:720}},"3 Col":{desktop:{width:406,height:416},tablet:{width:704,height:736},mobile:{width:294,height:302}},"4 Col":{desktop:{width:296,height:288},tablet:{width:704,height:736},mobile:{width:256,height:302}}}[k],focusArea:null==(d=B.gridItemImageWithFocalArea)?void 0:d.focusArea,src:null!=(j=null==(u=B.gridItemImageWithFocalArea)?void 0:u.image.url)?j:"",width:null!=(y=null==(m=B.gridItemImageWithFocalArea)?void 0:m.image.width)?y:0,height:null!=(N=null==(p=B.gridItemImageWithFocalArea)?void 0:p.image.height)?N:0,className:"w-full rounded-[30px]"})}),I||(null==B?void 0:B.cardTitle)!==""?(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(o.K,{is404Page:I,variant:"text",title:B.cardTitle,eyebrow:B.cardEyebrow,description:B.richCardDescription||B.cardDescription,isBgPurple:C,buttonCollection:D&&[{ctaUrl:(0,c.TE)(B.cardCTALink.ctaUrl,B.cardCTALink.pageLink),ctaText:B.cardCTALink.ctaText,ctaType:B.cardCTALink.ctaType||"Button",formModal:B.cardCTALink.formModal,downloadButtonText:B.cardCTALink.downloadButtonText,addDownloadType:B.cardCTALink.addDownloadType,uploadImageOrPdf:{url:null==(h=B.cardCTALink.uploadImageOrPdf)?void 0:h.url},linkToAsset:B.cardCTALink.linkToAsset}],isDark:T,isCTASecondary:!1,isTitleH2:_,className:L?"!text-white [&_.text-deep-blue]:!text-white [&_a:hover]:!underline [&_a]:!font-semibold [&_a]:!text-white":void 0})}):null]})},b=e=>{var n;let{variation:t="Grid Cards",background:r,promoBlockEyebrow:o,promoBlockTitle:c,promoBlockDescription:u,promoBlockRichText:m,buttonsCollection:p,gridCards:h,gridCardImageStrategy:x="Tall",gridCardLayout:g,tileCards:b,tileCardLayout:w,tileCardIsDark:j=!1,tileCardBackgroundColor:y,tileCardSectionLink:N,tileCardEnableStaggeredLayout:k,id:C,gridCardRenderPromoTitleAsH2:T,is404Page:B,showNextButton:I,isEventsPageHero:A=!1,topPillText:_,onTagClick:L,gridCardsToShow:P,tileCardsToShow:D,displayStyle:S="Default"}=e,W=null!=S?S:"Default",E=["Black","Eerie Black","Purple","Deep Blue","Ultra Dark Purple"].includes(r||"clear"),F="Purple"===r,R="Deep Blue"===r,M=(null==h?void 0:h.items)||[],O=P||(null==M?void 0:M.length),[H,U]=(0,a.useState)(1),[z,V]=(0,a.useState)(O),K=(0,a.useMemo)(()=>(null==h?void 0:h.items.slice(0,z))||[],[h,z]),G=(null==h||null==(n=h.items)?void 0:n.length)||0,$=(null==b?void 0:b.items)||[],q=j?"Black":"",Y="In the news"===c;return(0,l.jsx)("section",{className:f.outerSection(r||q,W),children:(0,l.jsx)("div",{className:f.outerContainer,children:(0,l.jsx)("div",{className:f.outerContent,children:(0,l.jsx)("section",{className:f.container(r||q),"data-testid":C,"data-section":"grid-cards",children:(0,l.jsx)("div",{className:f.content(W),children:"Grid Cards"===t?(0,l.jsxs)(l.Fragment,{children:[c&&(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(s.d,{eyebrow:o,heading:c,copy:u,richTextCopy:m,alignment:"center",buttonsCollection:p,isBtnSecondary:!1,isBgPurple:F,isDark:E,promoCardRenderTitleAsH2:T,className:Y?"GridCardMedia":"GridCard",customButtonContainerClasses:R?"flex justify-center":void 0,customButtonClasses:R?"btn-primary-secondary-styles-lg btn-primary-dark":void 0,customContentClasses:R?"!text-white [&_a]:!text-white [&_a]:!font-semibold [&_a:hover]:!underline mt-4 lg:mt-2 paragraph-small text-center":void 0,customH2Classes:R?"mb-6 text-center text-white text-[32px] lg:text-[48px] font-semibold":void 0})}),(0,l.jsxs)("div",{className:f.cardContainer(t,g,W),children:[(null==M?void 0:M.length)===0&&(0,l.jsx)("div",{className:"paragraph my-6 w-full border-y border-slate-200 p-6",children:(0,l.jsx)("p",{children:"No results"})}),null==K?void 0:K.map((e,n)=>(0,l.jsx)(v,{layout:g,isBgPurple:F,isDark:E,gridCard:e,is404Page:B,isTitleH2:T,gridCardImageStrategy:x,isBgDeepBlue:R},n)),G>z&&(0,l.jsx)("div",{className:"flex w-full justify-center",children:(0,l.jsx)(i.$n,{size:"small",onClick:()=>{U(H+1),V(O*(H+1))},variant:"primary",className:"mx-auto mb-8 mt-16",isDark:E,children:"Load more"})})]})]}):(0,l.jsx)("div",{className:f.cardContainer(t,g,W),children:(0,l.jsx)(d.M,{cardCollection:$,richTextDescription:m,isDark:j,layout:w,tileBgColor:y||"transparent",title:c,description:u,cta:{ctaUrl:N||""},enableStaggered:k,onTagClick:e=>L&&L(e),showNextButton:I,topPillText:_,isEventsPageHero:A,tileCardsToShow:D,displayStyle:W})})})})})})})}},22566:(e,n,t)=>{t.d(n,{O:()=>m});var l=t(54568),a=t(7620),i=t(58966),r=t(85610),s=t.n(r);let o={accordions:"w-full",container:(e,n)=>s()("\n      w-full\n      ".concat(e,"\n      ").concat(n?"hidden md:block":"","\n    ")),title:s()("\n    h5\n    uppercase\n    py-14\n    mb-8\n  ")};var c=t(21747);let d=e=>{let{item:n,tierPhone:t,itemPhone:a}=e;return"+"===n?(0,l.jsx)("div",{className:"pricing-ul-checklist any-p-small flex h-full items-center justify-center",children:(0,l.jsx)("ul",{className:"check-list mb-2 ml-2",children:(0,l.jsx)("li",{})})}):""===n?(0,l.jsx)("div",{className:"h-1 w-4 rounded-full bg-gray"}):"Call for details"===n?(0,l.jsx)("a",{href:"tel:".concat(a||t||"(*************"),className:"flex h-full items-center justify-center font-semibold text-dp-purple hover:cursor-pointer hover:underline",children:"Call for details"}):(0,l.jsx)("div",{className:"flex h-full items-center justify-center",children:n})},u=e=>{var n,t;let{tierCardsCount:a=0,item:i,preserveDesktop:r}=e;return r?(0,l.jsxs)("div",{className:"flex border-t-2 border-gainsboro py-[22px]",children:[(0,l.jsx)("div",{className:"sticky w-[250px] min-w-[250px] max-w-[250px] whitespace-pre-wrap text-base font-medium lg:font-bold",children:(null==(t=i.tooltip)?void 0:t.text)?(0,l.jsx)(c.A,{tooltipText:i.tooltip.text,position:i.tooltip.position,children:i.name}):i.name}),(0,l.jsx)("div",{className:"flex w-[250px] min-w-[250px] max-w-[250px] items-center justify-center whitespace-nowrap text-base",children:(0,l.jsx)(d,{item:i.tierOne,tierPhone:i.tierPhone,itemPhone:i.tierOnePhone})}),a>1&&(0,l.jsx)("div",{className:"flex w-[250px] min-w-[250px] max-w-[250px] items-center justify-center whitespace-nowrap text-base",children:(0,l.jsx)(d,{item:i.tierTwo,tierPhone:i.tierPhone,itemPhone:i.tierTwoPhone})}),a>2&&(0,l.jsx)("div",{className:"flex w-[250px] min-w-[250px] max-w-[250px] items-center justify-center whitespace-nowrap text-base",children:(0,l.jsx)(d,{item:i.tierThree,tierPhone:i.tierPhone,itemPhone:i.tierThreePhone})})]}):(0,l.jsxs)("div",{className:"grid grid-cols-1 border-t-2 border-gainsboro py-[22px] ".concat(2===a?"md:grid-cols-[350px_minmax(100px,_1fr)_333px]":"md:grid-cols-[250px_minmax(100px,_1fr)_1fr_250px] lg:grid-cols-[268px_minmax(100px,_1fr)_1fr_268px]"),children:[(0,l.jsx)("div",{className:"sticky whitespace-pre-wrap text-base font-medium lg:font-bold",children:(null==(n=i.tooltip)?void 0:n.text)?(0,l.jsx)(c.A,{tooltipText:i.tooltip.text,position:i.tooltip.position,children:i.name}):i.name}),(0,l.jsx)("div",{className:"hidden items-center justify-center whitespace-nowrap text-base md:flex",children:(0,l.jsx)(d,{item:i.tierOne,tierPhone:i.tierPhone,itemPhone:i.tierOnePhone})}),a>1&&(0,l.jsx)("div",{className:"hidden items-center justify-center whitespace-nowrap text-base md:flex",children:(0,l.jsx)(d,{item:i.tierTwo,tierPhone:i.tierPhone,itemPhone:i.tierTwoPhone})}),a>2&&(0,l.jsx)("div",{className:"hidden items-center justify-center whitespace-nowrap text-base md:flex",children:(0,l.jsx)(d,{item:i.tierThree,tierPhone:i.tierPhone,itemPhone:i.tierThreePhone})}),(0,l.jsxs)("div",{className:"flex flex-col md:hidden",children:[(0,l.jsxs)("div",{className:"my-2 flex w-full items-center justify-between",children:[(0,l.jsx)("div",{className:"w-[85px] text-base font-semibold",children:"Essentials"}),(0,l.jsx)("div",{className:"flex grow items-center justify-center whitespace-nowrap text-base",children:(0,l.jsx)(d,{item:i.tierOne,tierPhone:i.tierPhone,itemPhone:i.tierOnePhone})})]}),a>1&&(0,l.jsxs)("div",{className:"my-2 flex w-full items-center justify-between",children:[(0,l.jsx)("div",{className:"w-[85px] text-base font-semibold",children:"Advanced"}),(0,l.jsx)("div",{className:"flex grow items-center justify-center whitespace-nowrap text-base",children:(0,l.jsx)(d,{item:i.tierTwo,tierPhone:i.tierPhone,itemPhone:i.tierTwoPhone})})]}),a>2&&(0,l.jsxs)("div",{className:"my-2 flex w-full items-center justify-between",children:[(0,l.jsx)("div",{className:"w-[85px] text-base font-semibold",children:"Premium"}),(0,l.jsx)("div",{className:"flex grow items-center justify-center whitespace-nowrap text-base",children:(0,l.jsx)(d,{item:i.tierThree,tierPhone:i.tierPhone,itemPhone:i.tierThreePhone})})]})]})]})},m=e=>{let{accordions:n,className:t="",isOpenFirst:r=!0,title:s,titleTag:c,features:d,isFeatureDropdown:m,tierCardsCount:p,isAllOpen:h=!1,multipleOpen:x=!1,preserveDesktop:g=!1}=e,[f,v]=(0,a.useState)(void 0);(0,a.useEffect)(()=>{v(null==n?void 0:n.map((e,n)=>!!h||r&&0===n))},[n,r,h]);let b=(e,n)=>{v(x?null==f?void 0:f.map((t,l)=>l!==n?t:e):null==f?void 0:f.map((t,l)=>l===n&&e))},w=null==d?void 0:d.map(e=>e.featureList),j=null==n?void 0:n.map(e=>e);return(0,l.jsxs)("div",{className:o.container(t,m),children:[s&&(0,l.jsx)("h3",{className:o.title,children:s}),(0,l.jsx)("div",{className:o.accordions,children:d?null==j?void 0:j.map((e,n)=>{var t,r,s;return(0,a.createElement)(i.n,{...e,title:e.title,hasCaret:!0,isOpen:!!f&&f[n],key:e.title,setIsOpen:e=>b(e,n),titleTag:"h3",icon:e.icon,isFeatureDropdown:m},m&&w?null==(t=w[n])?void 0:t.map((e,n)=>(0,l.jsx)(u,{item:e,tierCardsCount:p,preserveDesktop:g},n)):null==(s=d[n])||null==(r=s.features)?void 0:r.map((e,n)=>(0,l.jsx)("div",{className:"mb-1 whitespace-pre-wrap py-1 md:py-2",children:e},n)))}):null==n?void 0:n.map((e,n)=>(0,a.createElement)(i.n,{...e,hasCaret:!0,isOpen:!!f&&!!f[n],key:n,setIsOpen:e=>b(e,n),titleTag:c},e.content))})]})}},23072:(e,n,t)=>{t.r(n),t.d(n,{FloatingCtas:()=>u});var l=t(54568),a=t(7620),i=t(61773),r=t(31328),s=t(71034),o=t(85610),c=t.n(o);let d={floatingCtasContainer:c()("\n    fixed\n    flex-col\n    h-full\n    hidden\n    justify-center\n    right-3.5\n    top-0\n    z-10\n\n    lg:flex\n  "),ctas:c()("\n    flex\n    flex-col\n    gap-2\n    items-end\n    justify-center\n  "),cta:e=>c()("\n    bg-dp-purple\n    border-4\n    border-solid\n    border-white\n    duration-200\n    flex\n    gap-5\n    h-14\n    items-center\n    !justify-end\n    !normal-case\n    overflow-hidden\n    p-[14px]\n    rounded-xl\n    text-white\n    transition-all\n    hover:cursor-pointer\n    [&:hover]:max-w-full\n    opacity-0\n    \n    translate-x-5\n    animate-ctaFadeIn\n    ".concat(e?"max-w-full":"w-full max-w-56px","\n  ")),ctaLabel:c()("\n    font-semibold\n    shrink-0\n    text-sm\n  "),icon:c()("\n    h-5\n    !ml-0\n    shrink-0\n    w-5\n    [&_path]:fill-white\n  ")},u=e=>{let{contentCollection:n}=e,[t,o]=(0,a.useState)(!0),[c,u]=(0,a.useState)(!0);(0,a.useEffect)(()=>{let e=()=>{setTimeout(()=>{o(!1)},3400)},n=()=>{t&&(o(!1),window.removeEventListener("scroll",n))};if("complete"!==document.readyState)return window.addEventListener("load",e),window.addEventListener("scroll",n),()=>{window.removeEventListener("load",e),window.removeEventListener("scroll",n)};e()},[t]),(0,a.useEffect)(()=>{if(!t){let e=setTimeout(()=>{u(!1)},2e3);return()=>{clearTimeout(e)}}},[t,c]);let m=e=>{let n=document.querySelector('[id="'+e.replace("#","")+'"]'),t=50*!!document.querySelector('[id="section-headache"]');if(n){let e=n.getBoundingClientRect().top+window.pageYOffset;window.scrollTo({top:e-50-t,behavior:"smooth"})}};return t?null:(0,l.jsx)("section",{className:d.floatingCtasContainer,children:(0,l.jsx)("div",{className:d.ctas,children:n.items.map((e,n)=>{let t="".concat(.3*n,"s");if(e.formId){let n={title:"Contact Us",description:"",marketingForm:{formId:e.formId}};return(0,l.jsxs)(r.$n,{className:"".concat(d.cta(c)," ").concat(""),formModal:n,variant:"transparent",style:{animationDelay:t},children:[(0,l.jsx)("span",{className:d.ctaLabel,children:e.copy}),(0,l.jsx)(i.default,{src:e.icon.url,alt:e.icon.title,width:e.icon.width,height:e.icon.height})]},e.copy)}return e.url.startsWith("#")?(0,l.jsxs)(s.N,{className:"".concat(d.cta(c)," ").concat(""),onClick:()=>m(e.url),style:{animationDelay:t},children:[(0,l.jsx)("span",{className:d.ctaLabel,children:e.copy}),(0,l.jsx)(i.default,{src:e.icon.url,alt:e.icon.title,width:e.icon.width,height:e.icon.height})]},e.copy):(0,l.jsxs)(s.N,{className:"".concat(d.cta(c)," ").concat(""),href:e.url,children:[(0,l.jsx)("span",{className:d.ctaLabel,children:e.copy}),(0,l.jsx)(i.default,{src:e.icon.url,alt:e.icon.title,width:e.icon.width,height:e.icon.height,className:d.icon})]},e.copy)})})})}},23615:(e,n,t)=>{t.r(n),t.d(n,{VideoBlock:()=>h});var l=t(54568),a=t(40595),i=t(72847),r=t(62942),s=t(7620),o=t(5474),c=t(18687),d=t(26268),u=t(85610),m=t.n(u);let p={wistiaResponsive:m()("\n    wistia_responsive_padding\n    padding-[56.25% 0 0 0]\n    position-relative\n    "),wistiaResponsiveWrapper:m()("\n    wistia_responsive_wrapper\n    h-[100%]\n    left-0\n    position-absolute \n    top-0\n    w-[100%]\n    "),setThumbnailHeight:e=>m()("\n    ".concat(e?"h-[0]":"h-[100%]","\n    ")),wistiaEmbed:(e,n)=>m()("\n    wistia_embed wistia_async_".concat(e," seo=true muted=true videoFoam=true autoPlay=muted preload=true settingsControl=false endVideoBehavior=loop fitStrategy=cover playsinline=true silentAutoPlay=true \n    ").concat(n?"volumeControl=true controlsVisibleOnLoad=true playButton=true smallPlayButton=true playbar=true":"volumeControl=false controlsVisibleOnLoad=false playButton=false smallPlayButton=false playbar=false","\n    display-block\n    height-[100%]\n    max-height-none\n    max-width-none\n    position-static\n    visibility-visible\n    width-[100%]\n    object-fit-fill\n    ")),containerInView:(e,n,t)=>m()("\n    ".concat(t?"mx-auto max-w-[912px] aspect-[912/577]":"","\n    ").concat(e?"h-full w-full !mb-0 !p-0":!t?"py-8 aspect-[16/11.4] md:aspect-[16/9.7]":"py-8","\n    ").concat(n,"\n  ")),container:(e,n,t)=>m()("\n    flex\n    justify-center\n    items-center\n    mb-10\n    overflow-hidden\n    relative\n    z-10\n\n    ".concat(t?"mx-auto max-w-[912px] aspect-[912/577]":"","\n    ").concat(e?"h-full w-full !mb-0 !p-0":!t?"py-8 aspect-[16/11.4] md:aspect-[16/9.7]":"py-8","\n    ").concat(n,"\n  ")),videoWrapper:m()("\n    relative\n    w-full\n    [&>div:first-child]:!w-full\n    [&>div:first-child]:!h-auto\n    [&_img]:!h-full\n    [&_img]:!object-cover\n    [&_img]:!w-full\n  "),headingContainer:m()("\n    w-full\n  "),heading:m()("\n    font-semibold\n    mb-8\n    text-[33px]\n    leading-[46px]\n    text-center\n    lg:text-[54px]\n    lg:leading-[62px]\n    lg:mb-20\n    "),player:e=>m()("\n    ".concat(e&&m()("\n      video-player\n      lg:h-full\n      lg:w-full\n      [&_video]:object-cover\n    "),"\n  "))},h=e=>{let{className:n="",size:t="full-width",videoHeading:u,wistiaId:m="",wistiaVideoBlock:h,isVideoCover:x=!1,id:g,preload:f=!1,autoPlayInView:v=!1,isHomeHero:b=!1,thumbnail:w,handleVideoError:j=()=>{}}=e,y=(0,i.a)(),N=(0,r.useSearchParams)(),k=null==N?void 0:N.get("play_video"),C=(0,s.useMemo)(()=>"limited-width"===t,[t]),T=(0,s.useRef)(null),B=(0,s.useRef)(null),I=(0,d.a8)(),A=(0,s.useRef)(!1),[_,L]=(0,s.useState)(!1),[P,D]=(0,s.useState)(!1),[S,W]=(0,s.useState)(null),[E,F]=(0,s.useState)(!1),[R,M]=(0,s.useState)(!1),O=(0,s.useMemo)(()=>{var e,n,t;return m||(null==h||null==(t=h.wistiaVideo)||null==(n=t.items)||null==(e=n[0])?void 0:e.hashed_id)||""},[m,h]);(0,s.useEffect)(()=>{k&&y&&(null==B?void 0:B.current)&&B.current.scrollIntoView({behavior:"smooth"})},[y,k]),(0,s.useEffect)(()=>{if(!y||!O)return;let e=e=>new Promise((n,t)=>{if(document.querySelector('script[src="'.concat(e,'"]')))n();else{let l=document.createElement("script");l.src=e,l.async=!0,l.onload=()=>{let e="wistiajsonp-/embed/medias/".concat(O,".jsonp"),l=window[e];l&&l.error?t(Error("Wistia video not found")):n()},l.onerror=()=>t(Error("Failed to load script: ".concat(e))),document.body.appendChild(l)}});return Promise.all([e("https://fast.wistia.com/embed/medias/".concat(O,".jsonp")),e("https://fast.wistia.com/assets/external/E-v1.js")]).then(()=>{let e=window;e._wq=e._wq||[],e._wq.push({id:O,options:{preload:f?"auto":"metadata",...b&&v?{autoPlay:!1}:{}},onReady:e=>{L(!0),W(e),window.MktoForms2&&(window.__marketoListenerAttached||(window.__marketoListenerAttached=!0,window.MktoForms2.whenReady(e=>{let n=document.querySelector(".wistia_embed"),t=e.getFormElem()[0];!t.dataset.listenerAttached&&(t.dataset.listenerAttached="true",n&&n.contains(t)&&e.onSubmit(()=>{(0,c._D)(e,"Wistia Form Submission",t.id)}))})));let n=e.name();A.current||(e.bind("play",()=>{(0,a.xu)({url:window.location.href,page_slug:I||"",video_title:n,video_id:O,event_type:"play"})}),e.bind("pause",()=>{(0,a.xu)({url:window.location.href,page_slug:I||"",video_title:n,video_id:O,event_type:"pause"})}),A.current=!0),setTimeout(()=>{F(!0)},7e3)}})}).catch(e=>{console.error("Error loading Wistia video:",e),j(e)}),()=>{document.querySelectorAll("[data-listener-attached]").forEach(e=>e.removeAttribute("data-listenerAttached"))}},[y,f,O,v,b,I,j]),(0,s.useEffect)(()=>{if(v){let e=new IntersectionObserver(e=>{e.forEach(e=>{let{target:n,isIntersecting:t}=e;n===T.current&&D(t)})},{threshold:.5});return T.current&&e.observe(T.current),()=>{e.disconnect()}}},[v]);let H=(0,s.useCallback)(()=>{M(!0)},[]);return(0,s.useEffect)(()=>{if(b&&S)return window.addEventListener("scroll",H),()=>window.removeEventListener("scroll",H)},[b,S,H]),(0,s.useEffect)(()=>{S&&v&&(P?(b&&M(!0),S.play()):S.pause())},[P,S,v,b]),(0,s.useEffect)(()=>{E&&S&&b&&M(!0)},[E,S,b,M,H,P]),(0,l.jsx)(l.Fragment,{children:v?(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)("div",{className:p.containerInView(x,n,C),"data-testid":g,ref:B,children:[u&&(0,l.jsx)("div",{className:p.headingContainer,children:(0,l.jsx)("h2",{className:p.heading,children:u})}),b&&w&&(0,l.jsx)("div",{className:p.setThumbnailHeight(R),children:(0,l.jsx)(o.s,{...w,className:"h-full w-full object-cover",priority:!0,loading:"eager"})}),(0,l.jsx)("div",{className:"".concat(p.videoWrapper," video-player-wrapper"),children:(0,l.jsx)("div",{ref:T,children:(0,l.jsx)("div",{className:p.wistiaResponsive,children:(0,l.jsx)("div",{className:p.wistiaResponsiveWrapper,children:(0,l.jsx)("div",{className:p.wistiaEmbed(O,b)})})})})})]})}):(0,l.jsxs)("div",{className:p.container(x,n,C),"data-testid":g,ref:B,children:[u&&(0,l.jsx)("div",{className:p.headingContainer,children:(0,l.jsx)("h2",{className:p.heading,children:u})}),(0,l.jsx)("div",{className:"".concat(p.videoWrapper," video-player-wrapper"),children:O&&(0,l.jsx)("div",{ref:T,className:"wistia_embed wistia_async_".concat(O," videoFoam=true preload=").concat(f),style:{height:"100%",width:"100%"},"data-preload":f.toString()})})]})})}},25933:(e,n,t)=>{t.d(n,{PricingAddOns:()=>o});var l=t(54568),a=t(31328),i=t(85610),r=t.n(i);let s={container:"container",content:"grid-container !px-0",title:"col-span-full text-left my-8",grid:e=>r()("col-span-full grid grid-cols-1 gap-4 lg:gap-x-10 ".concat(e<=2?"lg:grid-cols-".concat(e):"lg:grid-cols-2")),items:{title:"text-xl font-semibold text-left max-w-[200px] lg:max-w-[350px]",container:"flex flex-col p-5 border border-tan rounded-lg",header:"flex justify-between items-center",subContainer:"flex flex-row space-x-1 mb-4",topText:"text-xs md:text-base text-deep-blue",features:"flex flex-col space-y-2 mt-2",featureItem:"flex items-center",iconContainer:"bg-light-purple rounded-md flex items-center justify-center min-w-[44px] min-h-[44px]",icon:"w-6 h-6",featureText:"text-base ml-2.5",button:"md:!block !hidden",mobileButton:"!block md:!hidden mt-6"}},o=e=>{let{title:n,id:t,pricingAddOnsItemsCollection:i}=e;return(0,l.jsx)("section",{className:s.container,"data-testid":t,children:(0,l.jsxs)("div",{className:s.content,children:[(0,l.jsx)("h3",{className:s.title,children:n}),(null==i?void 0:i.items)&&(0,l.jsx)("div",{className:s.grid(i.items.length),children:i.items.map((e,n)=>(0,l.jsxs)("div",{className:s.items.container,children:[(0,l.jsxs)("div",{className:s.items.header,children:[(0,l.jsx)("h4",{className:s.items.title,children:e.title}),(0,l.jsx)(a.$n,{variant:"primary",size:"small",className:s.items.button,children:"Buy Now"})]}),(0,l.jsxs)("div",{className:s.items.subContainer,children:[!e.hasCta&&e.priceTopText&&(0,l.jsx)("span",{className:s.items.topText,children:e.priceTopText}),!e.hasCta&&e.price&&(0,l.jsx)("span",{className:s.items.topText,children:e.price}),!e.hasCta&&e.priceBottomText&&(0,l.jsx)("span",{className:s.items.topText,children:e.priceBottomText})]}),(0,l.jsx)(a.$n,{variant:"primary",size:"small",className:s.items.mobileButton,children:"Buy Now"})]},n))})]})})}},26233:(e,n,t)=>{t.r(n),t.d(n,{AppMarketplaceFilterGroup:()=>s});var l=t(54568),a=t(68061);let i={legendContainer:"is-collapsed group flex w-full cursor-pointer select-none flex-row items-center justify-between gap-2 max-lg:border-b max-lg:py-4 lg:mb-8 lg:justify-start [&.is-collapsed]:border-none",icon:"order-2 flex h-3 w-3 rotate-180 items-center transition-transform group-[.is-collapsed]:rotate-0 lg:order-1",title:"h4 lg:h3 order-1 decoration-1 underline-offset-4 hover:underline group-[.is-collapsed]:max-lg:text-dp-purple",itemsContainer:"mb-0 hidden lg:mb-8 [.is-collapsed+&]:!block [.is-collapsed+&]:max-lg:border-b",itemContainer:"mb-2.5 lg:mb-4 lg:ml-9",itemLabel:"block w-full cursor-pointer select-none capitalize decoration-1 underline-offset-4 checked:underline hover:underline lg:text-[20px]",subtext:"text-xs my-0"},r=(e,n)=>{e.currentTarget.classList.toggle(n)},s=e=>{let{title:n="Filter by",itemsCollection:t={items:[]}}=e;return(0,l.jsxs)("div",{"data-testid":"app-marketplace-filter-group",children:[(0,l.jsxs)("legend",{className:i.legendContainer,onClick:e=>r(e,"is-collapsed"),children:[(0,l.jsx)(a.te,{className:i.icon}),(0,l.jsx)("h4",{className:i.title,children:n})]}),(0,l.jsx)("div",{className:i.itemsContainer,children:t.items.map(e=>(0,l.jsxs)("div",{className:i.itemContainer,children:[(0,l.jsx)("input",{type:"radio",name:"category",className:"hidden",id:e.value}),(0,l.jsx)("label",{className:i.itemLabel,htmlFor:e.value,children:e.title}),e.subtext&&(0,l.jsx)("p",{className:i.subtext,children:e.subtext})]},e.value))})]})}},33010:(e,n,t)=>{t.r(n),t.d(n,{WysiwygTabs:()=>d});var l=t(54568),a=t(85906),i=t(41066),r=t(61864),s=t(85610),o=t.n(s);let c={container:"container",content:"lg:mx-auto mx-4 max-w-screen-lg",title:e=>o()("\n      hidden\n      w-full\n      ".concat(e?"h1-alt":"h1","\n    ")),hyperlink:o()("\n    text-dp-purple\n    hover:underline\n    hover:underline-offset-4\n  "),tabContent:o()("\n    mt-10\n  "),tabsContainer:o()("\n    flex\n    justify-between\n    w-full\n  "),tabPill:e=>o()("\n      \n      font-semibold\n\n      ".concat(e&&"\n          border-b-dp-purple\n          border-b-2\n          text-dp-purple\n        ","\n    ")),tabWrapper:o()("\n    justify-start\n\n    md:justify-center\n  ")},d=e=>{let{id:n="wysiwygTabs",title:t="",tabsCollection:s,tabClassname:o}=e,{isLg:d}=(0,a.d)(),u=(null==s?void 0:s.items)||[];return(0,l.jsx)("section",{className:c.container,"data-testid":n,"data-section":"rich-text",children:(0,l.jsxs)("div",{className:c.content,children:[(0,l.jsx)("h1",{className:c.title(t),children:t||"WysywigTabs"}),u.length>0&&(0,l.jsx)(r.t,{activePillClass:c.tabPill(!0),pillClass:c.tabPill(!1),tabContentClass:c.tabContent,tabsContainerClass:c.tabsContainer,wrapperClass:c.tabWrapper,className:o,tabs:(null==u?void 0:u.map(e=>{var n;return{title:e.title,content:(null==e?void 0:e.content)&&(0,l.jsx)(i.default,{contentBody:e.content}),tabAnchor:null!=(n=e.tabAnchor)?n:e.title.toLocaleLowerCase().replaceAll(" ","-")}}))||[],isInlineTabButtons:!d})]})})}},34077:(e,n,t)=>{t.r(n),t.d(n,{CarouselCustomerStories:()=>g});var l=t(54568),a=t(7620),i=t(31328),r=t(5474),s=t(85610),o=t.n(s);let c={cite:e=>o()("\n      block\n      h5-mobile\n      not-italic\n      text-eerie-black\n\n      ".concat(e&&"mt-8","\n    ")),container:e=>o()("\n      ".concat(e,"\n    ")),content:"grid-container",cta:o()("\n    mt-8\n    btn-small\n\n    lg:btn-large\n  "),description:o()("\n    mt-8\n    paragraph-mobile\n\n    lg:paragraph-desktop\n  "),figure:o()("\n    overflow-hidden\n    relative\n    rounded-30px\n  "),image:e=>o()("\n    !h-full\n    !w-full\n    rounded-30px\n    ".concat(e&&"\n        box-border\n        border-[1px]\n        border-tan\n      ","\n  ")),logo:o()("\n    !h-35px\n    mt-8\n    w-auto\n    !rounded-none\n  ")},d=e=>{var n,t,a,s;let{author:o,cite:d,className:u,description:m,logo:p,linkUrl:h,linkText:x,imageWithFocalArea:g}=e;return(0,l.jsxs)("article",{className:c.container(u),children:[(0,l.jsx)(r.s,{className:c.image(null==g?void 0:g.showBorder),src:null==g||null==(n=g.image)?void 0:n.url,alt:null==g||null==(t=g.image)?void 0:t.title,width:null==g||null==(a=g.image)?void 0:a.width,height:null==g||null==(s=g.image)?void 0:s.height,artDirection:{mobile:{width:256,height:224},tablet:{width:704,height:320},desktop:{width:296,height:224}},focusArea:null==g?void 0:g.focusArea}),p&&(0,l.jsx)(r.s,{alt:p.alt,className:c.logo,height:p.height,src:p.url,width:p.width}),(0,l.jsx)("p",{className:c.description,children:m}),(0,l.jsx)("cite",{className:c.cite(!0),children:o}),(0,l.jsx)("cite",{className:c.cite(!1),children:d}),h&&x&&(0,l.jsx)(i.$n,{className:c.cta,href:h,icon:"link-arrow",variant:"link",children:x})]})};var u=t(62270),m=t(57515),p=t(90005);let h={White:"bg-white",Tan:"bg-tan"},x={container:e=>o()("\n      container\n\n      ".concat(h[e],"\n    ")),content:"grid-container",textBlock:o()("\n    col-span-full\n    p-0\n    mb-[30px]\n    mt-3\n\n    md:mb-8\n\n    lg:mb-0\n    lg:col-span-4\n  "),textTitle:o()("\n    h2\n    mb-6\n    mt-4\n\n  "),textDescription:o()("\n    paragraph-mobile\n    p-0\n    mb-8\n\n    md:mb-[18px]\n\n    lg:paragraph-desktop\n    lg:mb-[30px]\n  "),ctaContainer:o()("\n    p-0\n    mb-0\n\n    md:[&_.btn-primary]:text-sm\n\n    lg:[&_.btn-primary]:text-lg\n  "),carouselBlock:o()("\n    col-span-full\n    overflow-hidden\n    [&_.custom-controls]:mb-[18px]\n    [&_.custom-controls]:mt-0\n\n    md:[&_.custom-controls]:mb-8\n    md:[&_.swiper-pagination-custom]:text-xs\n\n    lg:col-span-12\n    lg:[&_.custom-controls]:mb-[27px]\n    lg:[&_.swiper-pagination-custom]:text-base\n  "),hyperlink:o()("\n    text-dp-purple\n    hover:underline\n    hover:underline-offset-4\n  ")},g=e=>{var n;let{id:t,sys:r,backgroundColor:s="White",title:o,description:c,ctas:h,customerStoriesCards:g}=e,f=null==r?void 0:r.id,v={...(0,m.O)(f),spaceBetween:32,slidesPerView:1,slidesPerGroup:1,breakpoints:{1088:{slidesPerView:3,slidesPerGroup:3}}},b=null==g?void 0:g.items.map((e,n)=>(0,a.createElement)(d,{...e,key:n}));return(0,l.jsx)("section",{className:x.container(s),"data-testid":t,"data-sysid":f,"data-section":"customer-stories-cards",children:(0,l.jsxs)("div",{className:x.content,children:[(0,l.jsxs)("div",{className:x.textBlock,children:[o&&(0,l.jsx)("p",{className:x.textTitle,children:o}),c&&(0,l.jsx)("p",{className:x.textDescription,children:c}),h&&(null==h||null==(n=h.items)?void 0:n.length)>0&&(0,l.jsx)("div",{className:x.ctaContainer,children:h.items.map((e,n)=>(0,l.jsx)(i.$n,{href:(0,p.TE)(e.ctaUrl,e.pageLink),variant:{button:"primary",link:"link"}["".concat("Button"===e.ctaType?"button":"link")],icon:"Link"===e.ctaType?"link-arrow":"",type:"button",children:e.ctaText},n))})]}),(0,l.jsx)("div",{className:x.carouselBlock,children:(0,l.jsx)(u.l,{id:f,type:"cards",cards:b,options:v,controlPosition:"topRight"})})]})})}},36465:(e,n,t)=>{t.r(n),t.d(n,{SEMHero:()=>x});var l=t(54568),a=t(62942),i=t(7620),r=t(31328),s=t(68061),o=t(5474),c=t(71034),d=t(79900),u=t(85610),m=t.n(u),p=t(10193);let h={container:e=>m()("\n      container\n\n      ".concat(p.t[e],"\n    ")),content:"grid-container",cta:m()("\n    mt-8\n    mx-auto\n    text-sm\n\n    sm:mt-12\n    sm:text-18px\n\n    lg:mb-0\n    lg:mt-30px\n    lg:mx-0\n  "),details:m()("\n    col-span-8\n    relative\n  "),figure:m()("\n    aspect-square\n    col-span-8\n    mt-35px\n    overflow-hidden\n    relative\n\n    sm:mt-34px\n\n    lg:mt-0\n  "),imageBlock:e=>m()("\n    rounded-[30px]\n    ".concat(e&&"\n        box-border\n        border\n        border-tan\n      ","\n  ")),login:m()("\n    flex\n    flex-wrap\n  "),loginButton:e=>m()("\n      border\n      flex\n      items-center\n      justify-center\n      p-2\n      rounded-full\n      text-sm\n      w-full\n      font-bold\n\n      sm:p-5\n      sm:w-[calc(50%-9px)]\n\n      lg:max-w-447px\n      lg:p-13px\n      lg:w-full\n\n      ".concat(e.buttonBorderClass,"\n      ").concat(e.backgroundClass,"\n      ").concat(e.buttonHoverClass,"\n\n      last:mt-18px\n\n      sm:last:ml-auto\n      sm:last:mr-0\n      sm:last:mt-0\n      \n      lg:last:mt-30px\n      lg:last:ml-0\n    ")),loginGoogleIcon:m()("\n    h-5\n    mr-3\n    w-5\n\n    lg:h-33px\n    lg:mr-4\n    lg:w-33px\n  "),loginMicrosoftIcon:m()("\n    h-5\n    mr-3\n    w-5\n    xs:p-1\n\n    lg:p-2\n    lg:h-33px\n    lg:mr-4\n    lg:w-33px\n  "),loginHeader:m()("\n    h4-mobile\n    mb-18px\n    text-center\n    w-full\n\n    sm:mb-45px\n\n    lg:h4-desktop\n    lg:mb-30px\n    lg:text-left\n  "),textAreaBLock:m()("\n    sm:mb-30px\n    xs:mb-32px\n  "),backgroundStyles:e=>({White:{backgroundClass:"bg-white",buttonHoverClass:"hover:bg-tan",textClass:"text-black",buttonBorderClass:"border-black",isDark:!1},Black:{backgroundClass:"bg-black",buttonHoverClass:"hover:bg-eerie-black",textClass:"text-white",buttonBorderClass:"border-white",isDark:!0},"Dialpad Purple":{backgroundClass:"bg-dp-purple",buttonHoverClass:"hover:bg-light-purple",textClass:"text-white",buttonBorderClass:"border-white",isDark:!0},"Eerie Black":{backgroundClass:"bg-eerie-black",buttonHoverClass:"hover:bg-black",textClass:"text-white",buttonBorderClass:"border-white",isDark:!0},Tan:{backgroundClass:"bg-tan",buttonHoverClass:"hover:bg-white",textClass:"text-black",buttonBorderClass:"border-black",isDark:!1}})[e]},x=e=>{var n,t,u,m,p,x,g,f;let{background:v="Dialpad Purple",description:b,eyebrow:w,layout:j,title:y,imageWithFocalArea:N,imagePriority:k}=e,C="Image Left"===j,T=h.backgroundStyles(v),B=(0,a.useSearchParams)(),I=(0,a.usePathname)(),A=(0,i.useRef)(""),_={mobile:{width:256,height:256},tablet:{width:704,height:704},desktop:{width:624,height:624}};return(0,i.useEffect)(()=>{A.current=(null==B?void 0:B.toString())||""},[B,I]),(0,l.jsx)("section",{className:h.container(v),"data-section":"sem-hero",children:(0,l.jsxs)("div",{className:h.content,children:[C&&(0,l.jsx)("figure",{className:h.figure,children:(0,l.jsx)(o.s,{className:h.imageBlock(N.showBorder),src:null==N||null==(n=N.image)?void 0:n.url,alt:null==N||null==(t=N.image)?void 0:t.title,width:null==N||null==(u=N.image)?void 0:u.width,height:null==N||null==(m=N.image)?void 0:m.height,artDirection:_,focusArea:null==N?void 0:N.focusArea,imagePriority:k})}),(0,l.jsxs)("div",{className:h.details,children:[(0,l.jsx)(d.K,{className:h.textAreaBLock,description:b,eyebrow:w,isDark:T.isDark,isBgPurple:"Dialpad Purple"===v,title:y,variant:"poster"}),(0,l.jsxs)("div",{className:h.login,children:[(0,l.jsx)("h2",{className:h.loginHeader,children:"Try it free for 14 days."}),(0,l.jsxs)(c.N,{className:h.loginButton(T),href:"https://dialpad.com/auth/google/request?action=signup",children:[(0,l.jsx)(s.NR,{className:h.loginGoogleIcon}),"Sign up with Google"]}),(0,l.jsxs)(c.N,{className:h.loginButton(T),href:"https://dialpad.com/auth/microsoft?action=signup&c=%2F%3F",children:[(0,l.jsx)(s.u1,{className:h.loginMicrosoftIcon}),"Sign up with Microsoft"]})]}),(0,l.jsx)(r.$n,{className:h.cta,href:"https://dialpad.com/signup?".concat(A.current,"#email"),icon:"link-arrow",isDark:T.isDark,variant:"link",children:"Sign up with your email"})]}),!C&&(0,l.jsx)("figure",{className:h.figure,children:(0,l.jsx)(o.s,{className:h.imageBlock(N.showBorder),src:null==N||null==(p=N.image)?void 0:p.url,alt:null==N||null==(x=N.image)?void 0:x.title,width:null==N||null==(g=N.image)?void 0:g.width,height:null==N||null==(f=N.image)?void 0:f.height,artDirection:_,focusArea:null==N?void 0:N.focusArea,imagePriority:k})})]})})}},36669:(e,n,t)=>{t.r(n),t.d(n,{DrawerList:()=>h});var l=t(54568),a=t(7620),i=t(31328),r=t(41066),s=t(22566),o=t(5474),c=t(90005),d=t(85610),u=t.n(d);let m={outerSection:u()("\n    bg-white\n    py-[64px]\n    lg:py-[96px]\n  "),outerContainer:u()("\n    container\n    mx-auto\n    px-4\n  "),outerContent:u()("\n    relative\n    mx-auto\n    max-w-[1280px]\n  "),block:u()("\n    [&:not(:last-child)]:mb-[60px]\n\n    [&:not(:last-child)]:lg:mb-[69px]\n  "),container:"container",content:"grid-container !px-0",contentWithImage:"grid-container !px-0 lg:gap-8",cta:"mt-2",eyebrow:"text-base uppercase bold",figure:u()("\n    aspect-[256/352]\n    relative\n    rounded-[30px]\n    overflow-hidden\n\n    md:aspect-[1/1]\n\n    lg:aspect-[623/640]\n  "),figureWithImage:u()("\n    aspect-[624/624]\n    relative\n    rounded-[30px]\n    overflow-hidden\n    self-end\n\n    md:aspect-[1/1]\n\n    lg:aspect-[1/1]\n    lg:w-[624px]\n    lg:h-[624px]\n  "),image:u()("\n    rounded-[30px]\n  "),imageWithImage:u()("\n    rounded-[30px]\n    w-full\n    h-full\n    object-cover\n  "),leftColumn:u()("\n    col-span-full\n    mb-8\n\n    sm:mb-[42px]\n\n    lg:col-span-5\n    lg:mb-0\n  "),leftColumnWithImage:u()("\n    col-span-full\n    mb-8\n\n    sm:mb-[42px]\n\n    lg:col-span-7\n    lg:mb-0\n  "),rightColumn:u()("\n    col-span-full\n\n    lg:col-start-6\n    lg:col-end-17\n  "),rightColumnWithImage:u()("\n    col-span-full\n    flex\n    justify-end\n    lg:items-end\n    items-center\n    lg:col-start-8\n    lg:col-end-17\n  "),title:u()("\n    h1\n    [&:not(:first-child)]:mt-2\n    [&:not(:last-child)]:mb-2\n  "),ctaButton:u()("\n    mt-8\n  "),hyperlink:u()("\n    text-dp-purple\n    hover:underline\n    hover:underline-offset-4\n  ")},p={mobile:{width:256,height:352},tablet:{width:704,height:704},desktop:{width:624,height:640}},h=e=>{var n,t,d,u,h,x;let{ctaCollection:g,eyebrow:f,title:v="",imageWithFocalArea:b,withImageLists:w,withoutImageLists:j,variation:y,customRightContent:N,centerMobile:k}=e,[C,T]=(0,a.useState)([]),B=(null==b?void 0:b.image.url)&&"With Image"===y,I=B?null==w||null==(n=w.items)?void 0:n.map(e=>{let{title:n,description:t}=e;return{title:n,content:(0,l.jsx)(r.default,{contentBody:t})}}):[];(0,a.useEffect)(()=>{T(function(e){let n,t=[],a=[],i=0;return e.forEach(e=>{if("DrawerListCardSectionHeading"===e.typename)void 0!==n&&t.push({heading:n,accordions:a}),n=e.heading,a=[],i=0;else{let t="".concat(null==n?void 0:n.replace(/\s+/g,"-").toLowerCase(),"-").concat(i,"-").concat(a.length);void 0===n?(a.push(e.description?{title:e.title,content:(0,l.jsx)(r.default,{contentBody:e.description,className:"[&>p:first-child]:paragraph"}),id:t}:{title:e.title,content:void 0}),i++):a.push(e.description?{title:e.title,content:(0,l.jsx)(r.default,{contentBody:e.description,className:"[&>p:first-child]:paragraph"}),id:t}:{title:e.title,content:void 0})}}),void 0!==n?t.push({heading:n,accordions:a}):a.length>0&&t.push({heading:void 0,accordions:a}),t}((null==j?void 0:j.items)||[]))},[j]);let A=null==g||null==(t=g.items)?void 0:t[0];return(0,l.jsx)("section",{className:m.outerSection,children:(0,l.jsx)("div",{className:m.outerContainer,children:(0,l.jsx)("div",{className:m.outerContent,children:(0,l.jsx)("section",{className:m.container,"data-section":"drawer-list-card",children:(0,l.jsxs)("div",{className:B?m.contentWithImage:m.content,children:[(0,l.jsxs)("div",{className:[B?m.leftColumnWithImage:m.leftColumn,k?"flex flex-col items-center text-center md:items-start md:text-left":""].join(" "),children:[f&&(0,l.jsx)("span",{className:m.eyebrow,children:f}),(0,l.jsx)("h2",{className:m.title,children:v}),((null==A?void 0:A.ctaUrl)||(null==A?void 0:A.formModal))&&(0,l.jsx)(i.$n,{className:m.ctaButton,href:(0,c.TE)(A.ctaUrl,A.pageLink),variant:"primary",formModal:A.formModal,children:A.ctaText}),B&&(0,l.jsx)(s.O,{accordions:I,isOpenFirst:!0,titleTag:"h3"})]}),(0,l.jsx)("div",{className:B?m.rightColumnWithImage:m.rightColumn,children:N||(B?(0,l.jsx)("figure",{className:B?m.figureWithImage:m.figure,children:(0,l.jsx)(o.s,{className:B?m.imageWithImage:m.image,src:null==b||null==(d=b.image)?void 0:d.url,alt:null==b||null==(u=b.image)?void 0:u.title,width:null==b||null==(h=b.image)?void 0:h.width,height:null==b||null==(x=b.image)?void 0:x.height,artDirection:p,focusArea:null==b?void 0:b.focusArea})}):!B&&(null==C?void 0:C.map((e,n)=>e.accordions&&e.accordions.length>0&&(0,l.jsx)(s.O,{accordions:e.accordions,className:m.block,isOpenFirst:0===n,title:e.heading},n))))})]})})})})})}},36819:(e,n,t)=>{t.r(n),t.d(n,{TileCardText:()=>p});var l=t(54568);t(7620);var a=t(85906),i=t(4963),r=t(31328),s=t(41066),o=t(71034),c=t(85610),d=t.n(c);let u={container:(e,n,t)=>d()("\n    flex\n    flex-col\n    gap-y-4\n    overflow-hidden\n    break-words\n    word-wrap-break-word\n    ".concat("1 Col"===e?"lg:gap-y-6":"lg:gap-y-4","\n    ").concat("2 Col"===e?"lg:gap-y-6":"","\n    ").concat("3 Col"===e?"lg:gap-y-4":"","\n    ").concat("4 Col"===e?"lg:gap-y-4":"","\n    ").concat(t?"text-white dark":"text-black","\n  ")),textContainer:e=>d()("\n    flex\n    flex-col\n    gap-y-4\n    overflow-hidden\n    break-words\n    word-wrap-break-word\n    ".concat("1 Col"===e?"lg:gap-y-6":"lg:gap-y-4","\n    ").concat("2 Col"===e?"lg:gap-y-6":"","\n    ").concat("3 Col"===e?"lg:gap-y-4":"","\n    ").concat("4 Col"===e?"lg:gap-y-4":"","\n  ")),eyebrow:(e,n)=>d()("\n    text-xs\n    font-semibold\n    uppercase\n    ".concat("1 Col"===e?"lg:text-sm":"","\n    ").concat("2 Col"===e?"lg:text-sm":"","\n    ").concat("3 Col"===e?"lg:text-xs":"","\n    ").concat("4 Col"===e?"lg:text-xs":"","\n    ").concat(n||"","\n  ")),title:(e,n,t)=>d()("\n    font-semibold\n    break-words\n    word-wrap-break-word\n    overflow-wrap-break-word\n    ".concat("1 Col"===e?"text-2xl lg:text-3xl":"text-xl lg:text-2xl","\n    ").concat("2 Col"===e?"lg:text-2xl":"","\n    ").concat("3 Col"===e?"lg:text-xl":"","\n    ").concat("4 Col"===e?"lg:text-xl":"","\n    ").concat(n||"","\n    ").concat("Ultra Dark Purple"===t?"text-ultra-light-purple":"","\n  ")),description:e=>d()("\n    paragraph-small\n    break-words\n    word-wrap-break-word\n    overflow-wrap-break-word\n    ".concat("1 Col"===e?"lg:text-lg":"","\n    ").concat("2 Col"===e?"lg:text-lg":"","\n    ").concat("3 Col"===e?"lg:text-base":"","\n    ").concat("4 Col"===e?"lg:text-base":"","\n  ")),ctaContainer:d()("\n    flex\n    flex-col\n    gap-y-4\n    lg:flex-row\n    lg:items-center\n    lg:gap-x-4\n  "),btn:e=>d()("\n    ".concat("1 Col"===e?"lg:w-fit":"","\n    ").concat("2 Col"===e?"lg:w-fit":"","\n    ").concat("3 Col"===e?"lg:w-fit":"","\n    ").concat("4 Col"===e?"lg:w-fit":"","\n  ")),textLink:e=>d()("\n    ".concat("1 Col"===e?"lg:w-fit":"","\n    ").concat("2 Col"===e?"lg:w-fit":"","\n    ").concat("3 Col"===e?"lg:w-fit":"","\n    ").concat("4 Col"===e?"lg:w-fit":"","\n  ")),subText:e=>d()("\n    text-sm\n    ".concat("1 Col"===e?"lg:text-base":"","\n    ").concat("2 Col"===e?"lg:text-base":"","\n    ").concat("3 Col"===e?"lg:text-sm":"","\n    ").concat("4 Col"===e?"lg:text-sm":"","\n  "))},m=(e,n)=>{if(n)return()=>({renderNode:{[i.INLINES.HYPERLINK]:(e,n)=>{let t=e.data.uri||"",a=function(e){try{return new URL(e).hostname!==window.location.hostname}catch(e){return!1}}(t);return(0,l.jsx)(o.N,{className:"font-semibold text-ultra-light-purple underline hover:underline",href:t,onClick:e=>{if(t.startsWith("#")){e.preventDefault();let n=document.querySelector(t);if(n){let e=n.getBoundingClientRect().top+window.scrollY-133;window.scrollTo({top:e,behavior:"smooth"})}}},target:t.startsWith("#")?"_self":a?"_blank":"_self",rel:t.startsWith("#")?"":"noopener noreferrer",children:n})}}})},p=e=>{let{title:n,titleStyles:t,eyebrow:i="",description:o,richTextDescription:c,btnLabel:d,eyebrowStyles:p,btnLink:h,ctaLinkLabel:x,ctaLink:g,formModal:f,subText:v,layout:b="1 Col",btnVariant:w="primary",ctaVariant:j="link",isDark:y,isVideo:N,bgColor:k}=e,{isBelowSm:C}=(0,a.d)(),T=(e,n)=>{e.preventDefault();let t=document.getElementById(n.substring(1));if(t){let e=t.getBoundingClientRect().top+window.scrollY-(C?180:130);window.scrollTo({top:e,behavior:"smooth"})}},B=(null!=k?k:"")==="Ultra Dark Purple";return(0,l.jsxs)("div",{className:u.container(b,i,null!=y&&y),children:[(0,l.jsxs)("div",{className:u.textContainer(b),children:[i&&(0,l.jsx)("p",{className:u.eyebrow(b,p),children:i}),n&&(0,l.jsx)(l.Fragment,{children:"1 Col"===b?(0,l.jsx)("h2",{className:u.title(b,t,k),children:n}):(0,l.jsx)("h3",{className:u.title(b,t,k),children:n})}),c?(0,l.jsx)(s.default,{contentBody:c,options:m(c,B),className:"[&>p:first-child]:paragraph-small"}):o&&(0,l.jsx)("p",{className:u.description(b),children:o})]}),(d||x||v)&&(0,l.jsxs)("div",{className:u.ctaContainer,children:[d&&!N&&(0,l.jsx)(r.$n,{className:u.btn(b),formModal:f,href:h,variant:w,isDark:y,size:"small",children:d}),x&&("string"==typeof g&&(null==g?void 0:g.startsWith("#"))?(0,l.jsx)(r.$n,{className:u.textLink(b),formModal:f,icon:"link-arrow",onClick:e=>T(e,g),variant:j,isDark:y,size:"small",children:x}):(0,l.jsx)(r.$n,{className:u.textLink(b),formModal:f,icon:"link-arrow",href:g,variant:j,isDark:y,size:"small",children:x})),v&&(0,l.jsx)("p",{className:u.subText(b),children:v})]})]})}},37572:(e,n,t)=>{t.r(n),t.d(n,{FeatureListCards:()=>x});var l=t(54568),a=t(7620),i=t(85906),r=t(31328),s=t(41066),o=t(5474),c=t(85610),d=t.n(c);let u={container:d()("\n    w-full\n    lg:max-w-[378px]\n  "),content:e=>d()("\n    flex\n    flex-col\n    w-full\n    gap-y-3\n    ".concat(e?"text-white":"text-black","\n  ")),title:d()("\n    h3\n    \n    sm:h4\n    sm:max-w-[341px]\n  "),richtextContainer:d()("\n    lg:mx-0\n  ")},m=e=>{let{title:n,copy:t,isDark:a=!1,id:i,icon:r}=e;return(0,l.jsx)("div",{className:u.container,"data-testid":i,children:(0,l.jsx)("div",{className:u.content(a),children:r?(0,l.jsxs)("div",{className:"flex flex-col items-center gap-4 md:flex-row md:items-start",children:[(0,l.jsx)("div",{className:"flex shrink-0 items-center justify-center md:mt-1",children:(0,l.jsx)(o._,{src:r.url,alt:r.title||"",width:r.width||40,height:r.height||40,className:"h-[40px] w-[40px] min-w-[40px]"})}),(0,l.jsxs)("div",{className:"flex flex-col items-center text-center md:items-start md:text-left",children:[(0,l.jsx)("h2",{className:u.title,children:n}),(null==t?void 0:t.json)&&(0,l.jsx)("div",{className:u.richtextContainer,children:(0,l.jsx)(s.default,{contentBody:t,isDark:!0})})]})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("h2",{className:u.title,children:n}),(null==t?void 0:t.json)&&(0,l.jsx)("div",{className:u.richtextContainer,children:(0,l.jsx)(s.default,{contentBody:t,isDark:!0})})]})})})};var p=t(79900);let h={container:e=>d()("\n    container\n    ".concat(e?"bg-black":"bg-white","\n  ")),content:d()("\n    relative\n    grid-container\n  "),textAreaBlockContainer:d()("\n    col-span-full\n    lg:col-start-1\n    lg:col-end-7\n    [&_h3]:sm:text-[41px]\n    [&_h3]:sm:leading-[57px]\n    [&_div]:sm:mx-0\n    mb-8\n  "),cardsContainer:d()("\n    col-span-full\n    grid\n    grid-cols-1\n    gap-y-6\n    pb-[84px]\n    sm:pb-8\n    lg:pb-0\n    lg:grid-cols-2\n    lg:col-start-7\n    lg:col-end-17\n    lg:gap-x-8\n  "),btnContainer:(e,n)=>d()("\n    absolute\n    flex\n    justify-center\n    items-end\n    bottom-0\n    col-span-full\n    w-full\n    h-[140px]\n    lg:hidden\n    ".concat(n?"bg-gradient-to-t from-black to-black/0":"bg-gradient-to-t from-white to-white/0","\n    ").concat(e&&"hidden","\n\n  ")),btn:d()("\n    text-sm\n    min-w-9\n    h-9\n  ")},x=e=>{let{backgroundColor:n,eyebrow:t,title:s,copy:o,ctaButtons:c,listCardCollection:d,id:u="default-story"}=e,x=null==c?void 0:c.items,g="White"!==n,{isLg:f}=(0,i.d)(),[v,b]=(0,a.useState)(!1),w=(null==d?void 0:d.items)||[],j=v||f?w:w.slice(0,8);return(0,l.jsx)("section",{className:h.container(g),"data-testid":u,"data-section":"feature-list-card",children:(0,l.jsxs)("div",{className:h.content,children:[(0,l.jsx)("div",{className:h.textAreaBlockContainer,children:(0,l.jsx)(p.K,{title:s,eyebrow:t,description:o,buttonCollection:x,variant:"poster",isDark:g,isTitleH2:!0})}),(null==j?void 0:j.length)>0&&(0,l.jsx)("div",{className:h.cardsContainer,children:j.map((e,n)=>(0,a.createElement)(m,{...e,isDark:g,key:n}))}),w.length>8&&(0,l.jsx)("div",{className:h.btnContainer(v,g),children:(0,l.jsx)(r.$n,{className:h.btn,variant:"tertiary",onClick:()=>{b(!v)},children:"Load more"})})]})})}},41086:(e,n,t)=>{t.r(n),t.d(n,{ThreeTextColCard:()=>u});var l=t(54568),a=t(44715),i=t(7620),r=t(48258),s=t(5474),o=t(85610),c=t.n(o);let d={container:e=>"Black"===e?"dark bg-black":"bg-white",content:"block-container flex justify-center",columnsContainer:c()("\n    pt-12\n    flex\n    flex-wrap\n    w-full\n    content-center\n    dark:bg-black\n    dark:text-white\n\n    sm:pt-16\n\n    lg:pt-24\n  "),card:c()("\n    w-full\n    py-0\n    pr-8\n    mb-[30px]\n    flex\n    flex-col\n\n    sm:w-full\n    sm:mb-16\n\n    lg:w-1/3\n    lg:mb-0\n\n    [&>span]:paragraph-small\n    [&>span]:uppercase\n  "),cardList:c()("\n    grid\n    sm:grid-flow-col\n    sm:grid-cols-2\n    lg:grid-flow-row\n    lg:grid-cols-1\n    gap-8\n  "),columnTitle:e=>c()("\n    h3\n    \n    mt-[11px]\n    mb-8\n    lg:mb-9\n    h-[".concat(e,"px]\n")),columnItemTitleContainer:c()("\n    flex\n    items-center\n    mb-4\n\n    [&>img]:w-6\n    [&>img]:h-6\n  "),columnItemTitle:c()("\n    ml-[13px]\n    \n    h4\n  "),itemContent:c()("\n    pl-[36px]\n  ")},u=e=>{let{title:n,copy:t,cardsCollection:o,ctaText:c,ctaLink:u,ctaType:m,backgroundColor:p,alignment:h="center",id:x}=e,g=(0,i.useRef)([]),[f,v]=(0,i.useState)(39);return(0,i.useEffect)(()=>{g.current.forEach(e=>{e&&f<e.offsetHeight&&v(e.offsetHeight)})},[f]),(0,l.jsx)("section",{className:d.container(p),"data-testid":x,"data-section":"tct-cards",children:(0,l.jsxs)("div",{className:d.content,children:[(0,l.jsx)(r.d,{alignment:{"Left-aligned":"left",Centered:"center"}[h],heading:n,copy:t,isDark:"Black"===p,buttonsCollection:c?{items:[{ctaText:c,ctaUrl:u,ctaType:m}]}:void 0,className:"ThreeTextColCard",promoCardRenderTitleAsH2:!0,customContentClasses:"max-w-[unset]"}),(null==o?void 0:o.items.length)>0&&(0,l.jsx)("ul",{className:d.columnsContainer,children:o.items.map((e,n)=>(0,l.jsxs)("li",{className:d.card,children:[(0,l.jsxs)("span",{children:[" ",e.eyebrow," "]}),(null==e?void 0:e.title)&&(0,l.jsx)("div",{ref:e=>{g.current[n]=e},className:"".concat(d.columnTitle(f)),children:(0,a.Ay)(e.title)}),(0,l.jsx)("ul",{className:d.cardList,children:e.listCollection.items.map((e,n)=>{var t;return(0,l.jsxs)("li",{children:[(0,l.jsxs)("div",{className:d.columnItemTitleContainer,children:[(null==e||null==(t=e.icon)?void 0:t.url)&&(0,l.jsx)(s._,{"aria-hidden":!0,src:e.icon.url,alt:e.icon.title||e.title||"icon",width:24,height:24}),(0,l.jsxs)("h4",{className:d.columnItemTitle,children:[" ",e.title," "]})]}),(0,l.jsxs)("p",{className:d.itemContent,children:[" ",e.copy," "]})]},n)})})]},n))})]})})}},45258:(e,n,t)=>{t.r(n),t.d(n,{DialpadTip:()=>u});var l=t(54568),a=t(7620),i=t(31328),r=t(41066),s=t(90005),o=t(85610),c=t.n(o);let d={container:c()("\n    border\n    border-magenta\n    bg-lighter-tan\n    \n    max-w-dialpad-tip\n    mx-8\n    p-8\n    rounded-lg\n    w-full\n  "),cta:e=>e?"text-sm":"bg-dp-purple text-sm",description:c()("\n    leading-7\n    mb-3\n    text-xl\n  "),heading:c()("\n    font-semibold\n    leading-normal\n    text-magenta\n    mb-3\n    uppercase\n  "),wrapper:e=>c()("\n      flex-container\n      items-center\n      justify-center\n\n      ".concat(e,"\n    "))},u=e=>{var n,t,o,c,u;let{className:m="",ctaCollection:p,description:h,heading:x}=e,g=(0,a.useRef)(null),f=null==p?void 0:p.items[0],v=(null==f?void 0:f.ctaType)==="Link";return(null==f||null==(t=f.formModal)||null==(n=t.marketingForm)?void 0:n.formId)&&!(null==f||null==(c=f.formModal)||null==(o=c.marketingForm)?void 0:o.downloadType)&&(null==f?void 0:f.addDownloadType)&&(f.formModal.marketingForm.downloadType=f.addDownloadType,f.formModal.marketingForm.linkToTheAsset=f.linkToAsset,f.formModal.marketingForm.uploadImageOrPdf=f.uploadImageOrPdf?{url:null!=(u=f.uploadImageOrPdf.url)?u:null}:null),(0,l.jsx)("section",{className:d.wrapper(m),ref:g,"data-section":"dialpad-tip",children:(0,l.jsxs)("div",{className:d.container,children:[x&&(0,l.jsx)("h5",{className:d.heading,children:x}),(0,l.jsx)("div",{className:d.description,children:h&&(0,l.jsx)(r.default,{contentBody:h})}),(null==f?void 0:f.ctaText)&&(0,l.jsx)(i.$n,{className:d.cta(v),formModal:f.formModal,href:(0,s.TE)(f.ctaUrl,f.pageLink),icon:v?"link-arrow":"",variant:v?"link":"primary",children:f.ctaText})]})})}},46379:(e,n,t)=>{t.d(n,{Wi:()=>v,f7:()=>g,Li:()=>j,ST:()=>b});var l=t(54568),a=t(99474),i=t(7620),r=t(6520),s=t(62942),o=t(31328),c=t(59170);let d=(0,i.forwardRef)((e,n)=>{let{href:t,children:a,...r}=e,{billingIndex:s}=(0,i.useContext)(c.yy),d=t;if(d&&void 0!==s){let e=c.lN[s];d=d.replace(/(billing=)(\w+)/g,"$1".concat(e))}return(0,l.jsx)(o.$n,{...r,href:d,ref:n,children:a})});var u=t(41066),m=t(2886),p=t(85610),h=t.n(p);let x={pricingCard:h()("\n    flex\n    flex-col\n    justify-between\n    flex-1\n    bg-light-tan\n    rounded-xl\n    lg:rounded-3xl\n    p-6\n    lg:pb-12\n  "),line:h()("\n    bg-[rgba(0,0,0,0.2)]\n    h-1\n    w-12\n    my-8\n  "),contactUs:h()(" \n    flex \n    flex-col \n    justify-end \n    mb-2\n    h-[67px]\n    text-dp-purple\n    text-[32px]\n    font-seasonvf-serif\n    !font-medium\n  "),title:h()("\n    text-[24px]\n    mb-6\n    \n  "),price:h()("\n    h1-desktop\n    text-deep-blue\n    font-seasonvf-serif\n    !font-medium\n  "),period:h()("\n    paragraph-desktop\n    !semibold\n  "),microcopy:h()("\n    paragraph-mobile\n  "),normalText:h()("\n    paragraph-mobile\n  "),featuresTitle:h()("\n    font-semibold\n    !text-base\n    !mb-[5px]\n "),ctaCollectionContainer:h()("\n  flex\n  items-center\n  gap-6\n  "),annualSavings:e=>h()("\n    bg-[#DDF4D9]\n    text-[#124620]\n    font-semibold\n    text-sm\n    lg:text-xs\n    xl:text-sm\n    p-2.5\n    py-2\n    !mt-[30px]\n    mb-[18px]\n    block\n    w-fit\n    h-fit\n    rounded-md\n    transition-all\n    ".concat(e?"opacity-100":"opacity-0 !h-0 !p-0 !my-0","\n  "))},g={usd:{monthly:0,annual:0},cad:{monthly:0,annual:0},eur:{monthly:0,annual:0},gbp:{monthly:0,annual:0},nzd:{monthly:0,annual:0},aud:{monthly:0,annual:0},jpy:{monthly:0,annual:0}},f=["monthly","annual"],v={usd:"$",cad:"$",eur:"€",gbp:"\xa3",nzd:"$",aud:"$",jpy:"\xa5"},b={us:"usd",ca:"cad",eu:"eur",uk:"gbp",au:"aud",nz:"nzd",jp:"jpy"},w=(e,n)=>0===e||0===n?0:Math.floor((e-12*n/12)/e*100),j=e=>{var n,t,c,p;let{title:h,price:g={},hidePrice:j=!1,ctasCollection:y,featuresOverview:N,priceLabel:k,allFeaturesCtaText:C,allFeaturesCollection:T,selectedCurrency:B="usd",activeToggleIndex:I=0,modalInfo:A}=e,_=(0,a.useTranslations)(),L=_("pricingTiers.priceLabel"),P=_("pricingTiers.allFeaturesCtaText"),D=+(0===I);f[D];let S=(0,m.y)(g,B,I);(0,m.y)(g,B,D);let W=(0,s.useSearchParams)(),E=(0,a.useLocale)(),[F]=(0,r.A)(["dp_regional_pricing"]),R=(null==W?void 0:W.get("currency"))?null==W?void 0:W.get("currency"):E?b[E]:"usd",[M,O]=(0,i.useState)(R),H=v[null!=B?B:M]||"$";(0,i.useEffect)(()=>{let e=null==W?void 0:W.get("currency");e&&e!==M?O(e):O(E?b[E]:"usd")},[F,W,B,E]),w((null==(n=g[B])?void 0:n.monthly)||0,(null==(t=g[B])?void 0:t.annual)||0);let U=()=>f[I],z=(e,n)=>{let t=n.toLowerCase();if(t.includes("try free")||t.includes("buy now")){let n=e.includes("?")?"&":"?";return"".concat(e).concat(n,"billing=").concat(U())}return e};return(0,l.jsxs)("div",{className:x.pricingCard,"data-pricing-card":!0,children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:x.title,"data-plan-title":!0,children:h}),(0,l.jsxs)("div",{className:"flex flex-col lg:min-h-[20px]",children:[j&&(0,l.jsx)("div",{className:"mb-6 flex flex-col lg:mb-12",children:(0,l.jsx)("h4",{className:x.contactUs,children:"Contact us for pricing"})}),!j&&(0,l.jsx)("div",{className:"mb-6 flex flex-col lg:mb-12",children:(0,l.jsxs)("div",{className:"align-center flex items-baseline gap-2",children:[(0,l.jsxs)("h2",{className:x.price,children:[H,S]}),(0,l.jsx)("p",{className:x.period,children:k||L})]})}),(null==y||null==(c=y.items)?void 0:c.length)>0&&(0,l.jsx)("div",{className:x.ctaCollectionContainer,children:y.items.map((e,n)=>{var t;return(null==e?void 0:e.ctaUrl)&&(0,l.jsx)(d,{href:z(e.ctaUrl,null!=(t=e.ctaText)?t:""),variant:(null==e?void 0:e.ctaType)==="Link"?"link":"primary",icon:(null==e?void 0:e.ctaType)==="Link"?"link-arrow":"",size:"large",children:e.ctaText},n)})})]}),N&&(0,l.jsx)(u.default,{contentBody:N,className:"any-ul-checklist mt-12"})]}),T&&(null==T||null==(p=T.items)?void 0:p.length)>0&&(0,l.jsx)(o.$n,{size:"large",icon:"link-arrow",variant:"link",className:"ml-9 mt-7",modalData:A,children:C||P})]})}},49740:(e,n,t)=>{t.d(n,{default:()=>f});var l=t(54568),a=t(9662),i=t(87777),r=t(4963),s=t(7620),o=t(68061),c=t(5474),d=t(21747),u=t(71034),m=t(85610),p=t.n(m);let h={container:(e,n)=>p()("\n      container\n\n      ".concat(e?"bg-white text-black":"bg-black text-white","\n      ").concat(n,"\n    ")),content:e=>p()("\n      ".concat(e?"block text-left px-4 lg:px-8 lg:mx-auto max-w-4xl":"flex-container flex-col items-center text-center","\n    ")),global:e=>p()("\n      break-words\n      \n\n      ".concat(e,"\n    ")),a:"text-dp-purple font-semibold hover:underline",figure:"relative w-full",image:"w-full h-auto",em:"italic",h1:"h1 richtext-heading mb-4",h2:"h2 richtext-heading mb-4",h3:"h3 mb-4",h4:"h4 mb-4",h5:"h5 mb-4",h6:"h5 mb-4",ol:"list-decimal pl-5 text-left w-fit",paragraph:"paragraph white-space-normal mb-3",strong:"font-bold",sub:"align-sub",u:"underline",ul:"list-disc pl-5 text-left",table:"rich-text-table",tableWrapper:"w-full overflow-x-auto rounded-[28px] border border-gainsboro",blockquote:p()("\n    flex\n    items-start\n    font-normal\n    mb-5\n    relative\n\n    [&_p]:text-xl\n\n    [&_*:not(:first-child)]:text-xs\n\n    [&_:empty]:block\n    [&_:empty]:!mb-5\n\n    lg:[&_p]:text-24px\n    lg:[&_p:not(:first-child)]:font-semibold\n    lg:[&_*:not(:first-child)]:text-base\n  "),blockQuoteInner:p()("\n    block\n  "),leftQuote:p()("\n    min-h-14px\n    min-w-14px\n    h-14px\n    w-14px\n    mr-4\n    relative\n    top-7px\n    \n    lg:h-18px\n    lg:w-18px\n    lg:min-h-18px\n    lg:min-w-18px\n    lg:top-5px\n  "),invisibleAnchor:p()("\n      invisible\n  ")},x=e=>{var n,t,a,i,m,p,x,g,f;let v=new Map;null==e||null==(a=e.links)||null==(t=a.assets)||null==(n=t.block)||n.forEach(e=>v.set(e.sys.id,e));let b=new Map;null==e||null==(p=e.links)||null==(m=p.assets)||null==(i=m.hyperlink)||i.forEach(e=>b.set(e.sys.id,e));let w=new Map;return null==e||null==(f=e.links)||null==(g=f.entries)||null==(x=g.inline)||x.forEach(e=>w.set(e.sys.id,e)),{renderMark:{[r.MARKS.BOLD]:e=>(0,l.jsx)("strong",{className:h.strong,children:e}),[r.MARKS.ITALIC]:e=>(0,l.jsx)("em",{className:h.em,children:e}),[r.MARKS.UNDERLINE]:e=>(0,l.jsx)("u",{className:h.u,children:e}),[r.MARKS.SUBSCRIPT]:e=>(0,l.jsx)("sub",{className:h.sub,children:e}),[r.MARKS.SUPERSCRIPT]:e=>(0,l.jsx)("sup",{children:e})},renderNode:{[r.BLOCKS.HEADING_1]:(e,n)=>(0,l.jsx)("h1",{className:h.h1,children:n}),[r.BLOCKS.HEADING_2]:(e,n)=>(0,l.jsx)("h2",{className:h.h2,children:n}),[r.BLOCKS.HEADING_3]:(e,n)=>(0,l.jsx)("h3",{className:h.h3,children:n}),[r.BLOCKS.HEADING_4]:(e,n)=>(0,l.jsx)("h4",{className:h.h4,children:n}),[r.BLOCKS.HEADING_5]:(e,n)=>(0,l.jsx)("h5",{className:h.h5,children:n}),[r.BLOCKS.HEADING_6]:(e,n)=>(0,l.jsx)("h5",{className:h.h5,children:n}),[r.BLOCKS.PARAGRAPH]:(e,n)=>(0,l.jsx)("p",{className:h.paragraph,children:n}),[r.BLOCKS.LIST_ITEM]:(e,n)=>(0,l.jsx)("li",{children:n}),[r.BLOCKS.OL_LIST]:(e,n)=>(0,l.jsx)("ol",{className:h.ol,children:n}),[r.BLOCKS.UL_LIST]:(e,n)=>(0,l.jsx)("ul",{className:h.ul,children:n}),[r.BLOCKS.QUOTE]:(e,n)=>{let t=(0,s.useRef)(null);return(0,s.useEffect)(()=>{var e,n;let l=null==(e=t.current)?void 0:e.querySelector(":first-child");(null==l||null==(n=l.textContent)?void 0:n.endsWith("”"))||null==l||l.insertAdjacentText("beforeend","”")},[]),(0,l.jsxs)("blockquote",{className:h.blockquote,children:[(0,l.jsx)(o.qs,{"aria-hidden":!0,className:h.leftQuote}),(0,l.jsx)("div",{className:h.blockQuoteInner,ref:t,children:n})]})},[r.BLOCKS.EMBEDDED_ASSET]:e=>{let n=v.get(e.data.target.sys.id);return n&&(0,l.jsx)("figure",{className:h.figure,children:(0,l.jsx)(c._,{className:h.image,src:n.url,alt:n.title,width:n.width,height:n.height})})},[r.BLOCKS.TABLE]:(e,n)=>(0,l.jsx)("div",{className:h.tableWrapper,children:(0,l.jsx)("table",{className:h.table,children:(0,l.jsx)("tbody",{children:n})})}),[r.BLOCKS.TABLE_ROW]:(e,n)=>(0,l.jsx)("tr",{children:n}),[r.BLOCKS.TABLE_HEADER_CELL]:(e,n)=>(0,l.jsx)("th",{children:n}),[r.BLOCKS.TABLE_CELL]:(e,n)=>(0,l.jsx)("td",{children:n}),[r.INLINES.HYPERLINK]:(e,n)=>{let t=e.data.uri||"",a=function(e){try{return new URL(e).hostname!==window.location.hostname}catch(e){return!1}}(t);return(0,l.jsx)(u.N,{className:h.a,href:t,onClick:e=>{if(t.startsWith("#")){e.preventDefault();let n=document.querySelector(t);if(n){let e=n.getBoundingClientRect().top+window.scrollY-133;window.scrollTo({top:e,behavior:"smooth"})}}},target:t.startsWith("#")?"_self":a?"_blank":"_self",rel:t.startsWith("#")?"":"noopener noreferrer",children:n})},[r.INLINES.EMBEDDED_ENTRY]:(e,n)=>{let t=w.get(e.data.target.sys.id);if(t&&"Tooltip"===t.__typename){let e=t.tooltipText,n=t.description,a=t.position||"top";return(0,l.jsx)(d.A,{tooltipText:e,position:a,children:n})}return n}},renderText:e=>e.split("\n").reduce((n,t,a)=>{let i,r=/##(\w+)/g,s=0,o=[];for(;null!==(i=r.exec(t));){let e=i[1],n=i.index,a=r.lastIndex;n>s&&o.push(t.substring(s,n)),o.push((0,l.jsx)("span",{id:e,className:h.invisibleAnchor},e)),s=a}return s<t.length&&o.push(t.substring(s)),[...n,...o,a<e.split("\n").length-1&&(0,l.jsx)("br",{},a)]},[])}},g=e=>!!(0,a.l)(e).trim(),f=e=>{let{alignment:n,background:t,className:a="",questionRichText:r,answerRichText:s,isFull:o,options:c}=e,d=c?c(r):x(r),u=c?c(s):x(s);return g(null==r?void 0:r.json)||g(null==s?void 0:s.json)?o?(0,l.jsx)("div",{className:h.container("White"===t,a),children:(0,l.jsxs)("div",{className:h.content("Left"===n),children:[(0,i.i)(null==r?void 0:r.json,d),(0,i.i)(null==s?void 0:s.json,u)]})}):(0,l.jsxs)("div",{className:h.global(a),children:[(0,i.i)(null==r?void 0:r.json,d),(0,i.i)(null==s?void 0:s.json,u)]}):null}},50379:(e,n,t)=>{t.r(n),t.d(n,{CustomerStoriesHero:()=>x});var l=t(54568),a=t(7620),i=t(62942),r=t(68061),s=t(5474),o=t(68808),c=t(71034),d=t(31974),u=t(85610),m=t.n(u);let p={backIcon:m()("\n    h-10px\n    mr-2\n    w-4\n\n    [&_path]:fill-black\n  "),comma:m()("\n    inline\n    mr-1\n  "),container:"container",content:m()("\n    container\n    relative\n    text-center\n\n    lg:px-0\n  "),breadcrumbLink:m()("\n    flex\n    items-center\n    justify-center\n    uppercase\n\n    hover:text-dp-purple\n  "),breadcrumbs:m()("\n    \n    h5\n    my-30px\n    uppercase\n\n  "),figure:e=>m()("\n    max-w-[789px]\n    mb-[32px]\n    mx-auto\n    overflow-hidden\n    relative\n    w-full\n    md:mb-41px\n    ".concat(e.isVideo?"aspect-[296/166.5]":"rounded-[30px]","\n  ")),picture:m()("\n    w-full\n    h-inherit\n  "),image:m()("\n    w-full\n    h-full\n    !object-contain\n  "),footer:m()("\n    px-4\n    gap-x-5\n    grid\n    grid-cols-2\n\n    md:flex\n    md:justify-evenly\n    md:gap-x-0\n    md:items-start\n\n    lg:max-w-789px\n    lg:mx-auto\n    lg:px-0\n  "),item:(e,n)=>m()("\n      flex\n      font-semibold\n      \n      items-center\n\n      [&:not(:last-child)]:mb-3\n\n      sm:text-sm\n\n      md:text-base\n\n      ".concat(!e&&n&&"\n          underline\n\n          md:[&:not(:last-child)]:mb-0\n        ","\n    ")),label:m()("\n    mb-2\n    uppercase\n    font-bold\n  "),link:m()("\n    whitespace-normal\n\n    hover:text-dp-purple\n  "),info:m()("\n    whitespace-normal\n  "),list:e=>m()("\n      mb-[30px]\n\n      ".concat(!e&&"\n          flex\n          flex-wrap\n        ","\n\n      md:mb-0\n    ")),listWrapper:e=>m()("\n      text-left\n      w-fit\n\n      sm:mb-30px\n      sm:w-full\n\n      md:[&:nth-child(2)]:max-w-[11%]\n      md:[&:nth-child(3)]:max-w-[13.35%]\n      md:[&:nth-child(4)]:max-w-[20.45%]\n      md:[&:nth-child(5)]:max-w-[30.68%]\n\n      lg:[&:nth-child(3)]:max-w-[12.16%]\n      lg:[&:nth-child(4)]:max-w-[18.25%]\n      lg:[&:nth-child(5)]:max-w-[27.37%]\n\n      ".concat(!e&&"col-start-2","\n    ")),logo:m()("\n    mb-3\n    overflow-hidden\n    relative\n    w-full\n    h-58px\n    flex\n    items-start\n    justify-center    \n\n    sm:mb-5\n\n    lg:h-80px\n    max-w-160px\n  "),share:m()("\n    mx-auto\n    transition-opacity\n    hover:opacity-70\n    mb-8\n\n    sm:mb-8\n    md:mb-41px\n    lg:mb-5\n  "),shareIcon:m()("\n    h-7\n    text-dp-purple\n    w-30px\n  "),title:m()("\n    mx-8\n    pb-16\n    sm:mx-8\n    lg:max-w-851px\n    lg:mx-auto\n    before:absolute\n    before:bg-tan\n    before:content-['']\n    before:h-[100%]\n    before:left-0\n    before:top-0\n    before:w-full\n    before:-z-10\n    sm:before:h-[60%]\n    md:before:h-[65%]\n    lg:before:h-[60%]\n    \n  "),topicIcon:m()("\n    mr-1.5\n  "),separator:m()("\n      px-4\n      lg:px-8\n  ")},h=e=>{let{hasIcon:n=!1,isNthChildEven:t=!1,items:a,label:i,displayIcons:r=!0}=e;return a&&a.items.length>0&&(0,l.jsxs)("div",{className:p.listWrapper(t),children:[(0,l.jsx)("p",{className:p.label,children:i}),(0,l.jsx)("ul",{"aria-label":i,className:p.list(n),role:"list",children:a.items.map((e,t)=>{var i,o,u,m;return((null==(i=e.industryLandingPage)?void 0:i.slug)||(null==e?void 0:e.url)||(null==e?void 0:e.label))&&(0,l.jsxs)("li",{className:p.item(n,!!((null==e?void 0:e.url)||(null==(o=e.industryLandingPage)?void 0:o.slug))),role:"listitem",children:[n&&e.icon&&(0,l.jsx)(s.s,{"aria-hidden":!0,className:p.topicIcon,src:e.icon.url,alt:e.icon.alt,width:12,height:12}),e.url||(null==(u=e.industryLandingPage)?void 0:u.slug)?(0,l.jsx)(c.N,{className:p.link,href:e.url||(0,d.By)({path:null==(m=e.industryLandingPage)?void 0:m.slug})||"",children:(0,l.jsx)(g,{item:e,itemIndex:t,displayIcons:r,list:a})}):(0,l.jsx)("span",{className:p.info,children:(0,l.jsx)(g,{item:e,itemIndex:t,displayIcons:r,list:a})})]},t)})})]})},x=e=>{var n;let{showImage:t,wistiaVideo:d,industries:u,ingrationCustomers:m,logo:x,relatedProductCustomers:g,relatedSegements:f,title:v,imageWithFocalArea:b,imagePriority:w}=e,[j,y]=(0,a.useState)(!1),N=(0,i.useSearchParams)();return(0,a.useEffect)(()=>{j&&setTimeout(()=>{y(!1)},3e3)},[j]),(0,l.jsx)("section",{className:p.container,"data-section":"customer-stories-hero",children:(0,l.jsxs)("div",{className:p.content,children:[(0,l.jsx)("p",{className:p.breadcrumbs,children:(0,l.jsxs)(c.N,{className:p.breadcrumbLink,href:(()=>{let e=(null==N?void 0:N.get("locale"))||"default";return"default"===e?"/customers":"/".concat(e,"/customers")})(),children:[(0,l.jsx)(r.Z3,{className:p.backIcon}),"Back to Customer Stories"]})}),(0,l.jsx)("h1",{className:"".concat(p.title),children:v}),(t||null===t)&&b&&(0,l.jsx)("div",{className:"px-4 lg:px-0",children:(0,l.jsx)("figure",{className:p.figure({isVideo:!1}),children:(0,l.jsx)(s.s,{src:b.image.url,alt:null==(n=b.image)?void 0:n.title,artDirection:{mobile:{width:256,height:256},tablet:{width:704,height:416},desktop:{width:789,height:480}},focusArea:null==b?void 0:b.focusArea,imagePriority:w})})}),!t&&d&&d.wistiaVideo&&d.wistiaVideo.items&&d.wistiaVideo.items[0]&&(0,l.jsx)("div",{className:"mb-8 px-4 lg:px-0",children:(0,l.jsx)(o.F,{className:p.figure({isVideo:!0}),wistiaId:d.wistiaVideo.items[0].hashed_id,isVideoCover:!0,"data-testid":"video-block"})}),(0,l.jsxs)("div",{className:p.footer,children:[(null==x?void 0:x.url)&&(0,l.jsx)("figure",{className:p.logo,children:(0,l.jsx)(s.s,{pictureClassName:p.picture,className:p.image,src:x.url,alt:x.alt||x.alternateAlt||"",artDirection:{mobile:{width:100},tablet:{width:100},desktop:{width:160}},imagePriority:w})}),(0,l.jsx)(h,{items:f,isNthChildEven:!0,label:"Segment"}),(0,l.jsx)(h,{items:u,label:"Industry"}),(0,l.jsx)(h,{items:m,displayIcons:!1,isNthChildEven:!0,label:"Integrations"}),(0,l.jsx)(h,{hasIcon:!0,items:g,label:"Products"})]}),(0,l.jsx)("div",{className:p.separator,children:(0,l.jsx)("hr",{})})]})})},g=e=>{let{item:n,list:t,itemIndex:a,displayIcons:i}=e,r=n.label?n.label.trim():"";return(0,l.jsxs)(l.Fragment,{children:[r,a!==t.items.length-1&&(!i||!n.icon)&&(0,l.jsx)("span",{"data-testid":"comma-test-id","aria-hidden":!0,className:p.comma,children:","})]})}},58966:(e,n,t)=>{t.d(n,{n:()=>u});var l=t(54568),a=t(7620),i=t(5467),r=t(68061),s=t(5474),o=t(85610),c=t.n(o);let d={caretIcon:e=>c()("\n      h-3\n      w-3\n      min-w-[12px]\n\n      sm:h-3.5\n      sm:w-3.5\n      transition-transform\n\n      lg:min-h-18px\n      lg:min-w-18px\n\n      ".concat(e?"\n          [transform:rotateX(0deg)]\n          [&_path]:fill-dp-purple\n        ":"\n          [transform:rotateX(180deg)]\n          [&_path]:fill-deep-blue\n        ","\n    ")),container:(e,n)=>c()("\n      ".concat(!e&&" border-b\n      border-b-ultra-dark-tan\n      py-9\n      first:md:pt-0\n      ","\n      ").concat(n&&"py-4 px-0","\n  ")),children:(e,n,t)=>c()("\n      duration-500\n      overflow-hidden\n      paragraph-small\n      transition-all\n      pt-0\n\n      ".concat(!n&&!t&&e&&"mt-4","\n\n      ").concat(n&&"md:pt-0","\n\n      ").concat(t&&"!overflow-visible","\n\n      ").concat(e?"\n          opacity-100\n          visible\n        ":"\n          invisible\n          opacity-0\n        ","\n    ")),childrenWrapper:e=>c()("\n    lg:text-xl \n    ".concat(e&&"!whitespace-pre","\n    whitespace-normal\n    ")),h3:e=>c()("".concat(!e&&"lg:h4-desktop"," h4-mobile")),h4:e=>c()("".concat(e?"font-semibold":"h2 mr-4")),toggle:e=>c()("\n    flex\n    items-center\n    justify-between\n    w-full\n    text-left\n    ".concat(e&&"py-6 border-t-2 border-black","\n  "))},u=e=>{var n;let{children:t,hasCaret:o,onToggle:c,title:u,titleTag:m,isOpen:p,setIsOpen:h,icon:x,isFeatureDropdown:g,isRatesDropdown:f}=e,[v,b]=(0,a.useState)(0),w=(0,a.useRef)(null),{innerWidth:j}=(0,i.l)();return(0,a.useEffect)(()=>{var e;b((null==(e=w.current)?void 0:e.offsetHeight)||0)},[j,p,null==(n=w.current)?void 0:n.offsetHeight]),(0,a.useEffect)(()=>{c&&c()},[p,c]),(0,l.jsxs)("div",{className:d.container(g,f),children:[(0,l.jsxs)("button",{"aria-expanded":p,className:d.toggle(g),onClick:()=>h(!p),children:[(0,l.jsxs)("div",{className:"flex",children:[x&&"/assets/images/"!==x&&(0,l.jsx)(s._,{className:"mr-4",src:x,alt:"accordion-icon",width:26,height:26}),"h3"===m?(0,l.jsx)("h3",{className:d.h3(g),children:u}):(0,l.jsx)("h4",{className:d.h4(f),children:u})]}),o&&(0,l.jsx)(r.te,{className:d.caretIcon(p)})]}),(0,l.jsx)("div",{className:d.children(p,g,f),style:{height:p?v:0},children:(0,l.jsx)("div",{className:d.childrenWrapper(g),ref:w,children:t})})]})}},59040:(e,n,t)=>{t.r(n),t.d(n,{PromoTickerBanner:()=>p});var l=t(54568),a=t(91039),i=t(7620),r=t(68061),s=t(71034),o=t(85610),c=t.n(o);let d={White:"bg-white","Light Tan":"bg-pale-tan",Tan:"bg-tan",Black:"dark bg-black","Dialpad Purple":"dark bg-dp-purple"},u={container:e=>c()("\n      container\n      ".concat(d[e],"\n  ")),ticker:e=>c()("\n      w-full\n      \n      ".concat(e?"text-white":"text-black","\n  ")),tickerContainer:"\n    h3\n    mr-[30px]\n    inline-flex\n    items-center\n    gap-[30px]\n    [&>*]:!leading-[140%]\n    md:text-[30px]\n    font-semibold\n    uppercase\n    py-[19px]\n    md:py-[30px]\n  ",spacer:e=>c()("\n      h-[18px] \n      w-[23px]\n      md:h-[26px]\n      md:w-[34px]\n      ".concat(e?"[&_path]:fill-white":"[&_path]:fill-black","\n  "))},m=(0,a.default)(()=>t.e(3131).then(t.bind(t,3131)),{loadableGenerated:{webpack:()=>[3131]},ssr:!0}),p=e=>{var n;let{tickerItemsCollection:t,backgroundColor:a,id:o}=e,[c,d]=(0,i.useState)(!1),p="Dialpad Purple"===a||"Black"===a,[h,x]=(0,i.useState)(0),g=(0,i.useRef)(null),f=()=>{setTimeout(()=>{var e;(null==(e=g.current)?void 0:e.contains(document.activeElement))||(x(e=>e+1),d(!1))},10)};return(0,l.jsx)("section",{className:u.container(a),"data-testid":o,ref:g,onBlur:f,children:(0,l.jsx)("div",{className:u.ticker(p),children:(0,l.jsx)(m,{autoFill:!0,pauseOnHover:!0,play:!c,children:null==t||null==(n=t.items)?void 0:n.map((e,n)=>{let{url:t,tickerText:a,textOnly:i}=e;if(i)return(0,l.jsxs)("div",{className:u.tickerContainer,children:[t?(0,l.jsx)(s.N,{href:t,onFocus:()=>{d(!0)},onBlur:f,children:a}):(0,l.jsx)("p",{children:a}),(0,l.jsx)(r.Ii,{className:u.spacer(p)})]},n)})})})},h)}},59170:(e,n,t)=>{t.d(n,{c4:()=>l.PricingTiers,lN:()=>l.SEARCH_PARAM_BILLING_CYCLES,yy:()=>l.PricingTierContext});var l=t(90706)},64304:(e,n,t)=>{t.r(n),t.d(n,{TileCards:()=>F});var l=t(54568),a=t(7620),i=t(31328),r=t(85906),s=t(76230),o=t(68061),c=t(5474),d=t(16800),u=t(71034),m=t(93726),p=t(79364),h=t(85610),x=t.n(h);let g={White:"bg-white",Tan:"bg-lighter-tan",transparent:"bg-transparent","Ultra Dark Purple":"bg-ultra-dark-purple dark text-white"},f="relative overflow-hidden",v={"1 Col":x()("\n    lg:h-[480px]\n    lg:order-2\n    rounded-[30px]\n  "),"2 Col":x()("\n    lg:w-full\n    lg:h-auto\n    lg:order-1\n  "),"3 Col":x()("\n    lg:w-full\n    lg:h-[288px]\n    lg:order-1\n    xl:w-[406px]\n    xl:h-[416px]\n    rounded-[30px]\n  "),"4 Col":x()("\n    lg:w-full\n    lg:h-[288px]\n    lg:order-1\n  \n    rounded-[30px]\n  ")},b={"1 Col":x()("\n    lg:w-full\n    lg:h-auto\n    rounded-t-[30px]\n    lg:rounded-[30px]\n    lg:order-2\n    lg:mr-[81px]\n  "),"2 Col":x()("\n    lg:w-full\n    lg:h-auto\n    rounded-t-[30px]\n    lg:rounded-[30px]\n    lg:order-1\n    lg:mr-0\n  "),"3 Col":x()("\n    lg:w-full\n    lg:h-[320px]\n    rounded-t-[30px]\n    lg:order-1\n    lg:mr-0\n    xl:w-[406px]\n    xl:h-[320px]\n  "),"4 Col":x()("\n    lg:w-full\n    lg:h-[224px]\n    rounded-t-[30px]\n    lg:order-1\n    lg:mr-0\n  ")},w={"1 Col":x()("\n    flex\n    flex-col\n    pt-8\n    gap-y-[24px]\n    lg:w-[574px]\n    lg:pt-6\n  "),"2 Col":x()("\n    flex\n    flex-col\n    gap-y-30px\n    pt-8\n    mb-0\n\n    lg:w-full\n    lg:gap-y-30px\n    lg:order-2\n    lg:ml-0\n    lg:pt-[30px]\n    lg:pb-[52px]\n    lg:pl-8\n    lg:pr-25px\n  "),"3 Col":x()("\n    flex\n    flex-col\n    xs:mb-8\n    sm:mb-8\n    lg:mb-0\n    gap-y-[30px]\n    \n    lg:w-full\n    lg:gap-y-[26px]\n    lg:rounded-tr-none\n    lg:order-2\n    lg:ml-0\n    pt-8\n    xl:w-[406px]\n  "),"4 Col":x()("\n    flex\n    flex-col\n    xs:mb-8\n    sm:mb-8\n    lg:mb-0\n    gap-y-[30px]\n    pt-8\n    \n    lg:w-full\n    lg:gap-y-[30px]\n    lg:rounded-tr-none\n    lg:order-2\n    lg:ml-0\n    \n  ")},j={container:x()("\n    mx-0\n    group\n    h-full\n  "),content:(e,n,t,l,a,i)=>x()("\n    flex\n    flex-col\n    h-full\n    underline-offset-2\n    transition-drop-shadow duration-500 ease-in-out\n    overflow-hidden\n    preserve-2d\n    [&:hover_.video-player-wrapper_path]:fill-ultra-light-purple\n    ".concat(g[t],"\n    ").concat(!e&&x()("\n        [&_div.video-player]:!h-full\n        [&_div.video-player]:!w-full\n      "),"\n    ").concat("1 Col"===n&&x()("\n        [&_img.tilecard-image]:h-full\n        lg:[&_img.tilecard-image]:max-w-full\n        lg:py-7\n        ".concat(i?"lg:gap-x-32":"lg:gap-x-[84px]","\n      ")),"\n    ").concat("2 Col"===n&&"[&_img.tilecard-image]:h-full lg:gap-x-0","\n    ").concat("3 Col"===n||"4 Col"===n?"lg:flex-col lg:gap-x-0":"lg:flex-row","\n    ").concat("1 Col"===n&&"Tan"===t?"rounded-[30px] p-6 md:py-8 md:px-12":"","\n    ").concat("2 Col"===n&&"Tan"===t?"rounded-[30px] p-6 md:p-8 lg:p-0":"","\n    ").concat("Ultra Dark Purple"===t?"rounded-[16px] p-6":"","\n  ")),imageWrapper:(e,n,t)=>x()("\n    ".concat("","\n    ").concat("","\n    ").concat(!t&&"1 Col"===e?"lg:[&_img]:w-[526px] lg:w-[526px]":"","\n    ").concat(!t&&"1 Col"!==e?"[&_img]:w-full [&_img]:h-full":"","\n    ").concat(!t?f:"","\n    ").concat(!t?v["".concat(e)]:"","\n    ").concat("1 Col"===e&&!t&&"md:h-[480px]","\n    ").concat(("3 Col"===e||"4 Col"===e)&&!t&&"md:h-[440px]","\n    ").concat(n&&!t?"lg:[&_img]:!w-[524px] md:!h-[480px]":"","\n  ")),imageBlock:e=>x()("\n    tilecard-image\n    absolute\n    left-0\n    top-0\n    rounded-[30px]\n    w-full\n    h-full\n    ".concat(e&&"\n        box-border\n        border-[1px]\n        border-tan\n      ","\n  ")),videoWrapper:(e,n)=>x()("\n    ".concat(f,"\n    ").concat(b["".concat(e)],"\n    ").concat("1 Col"===e&&"lg:rounded-[30px]","\n    ").concat("2 Col"===e&&x()("\n        lg:rounded-r-none\n        lg:rounded-l-[30px]\n      "),"\n    ").concat(("3 Col"===e||"4 Col"===e)&&"transparent"===n?"rounded-[30px]":"2 Col"!==e&&"rounded-[30px]","\n\n    ").concat("1 Col"===e&&"lg:[&_.video-player-wrapper]:h-[480px]","\n    ").concat("2 Col"===e&&"lg:[&_.video-player-wrapper]:h-[352px]","\n    ").concat("3 Col"===e&&"lg:[&_.video-player-wrapper]:h-[320px]","\n    ").concat("4 Col"===e&&"lg:[&_.video-player-wrapper]:h-[224px]","\n  ")),videoIconOnly:x()("\n    absolute\n    inset-0\n    flex\n    items-center\n    justify-center\n    hover:cursor-pointer\n    group-hover:[&_path]:fill-ultra-light-purple\n  "),videoIconV2:x()("\n    absolute\n    bottom-0\n    left-0\n    flex\n    h-[60px]\n    w-[60px]\n    items-center\n    justify-center\n    rounded-tr-[20px]\n    bg-[#EDEDED]\n    pl-[3px]\n    group-hover:[&_path]:fill-dp-purple\n  "),textContent:(e,n,t,l)=>x()("\n    ".concat(w["".concat(e)],"\n    ").concat("2 Col"===e&&"transparent"===n?"lg:p-0 max-lg:!px-0 max-lg:!pb-0":"md:pr-0 md:pb-0","\n    ").concat("3 Col"===e||"4 Col"===e?"lg:pt-8 lg:!px-0 lg:pb-0":"","\n    ").concat("1 Col"===e&&"Tan"===n&&"pt-8 pb-5","\n    ").concat("2 Col"===e&&"Tan"===n&&"pt-8","\n    ").concat(t&&"lg:!ml-0","\n    ").concat("Ultra Dark Purple"===n?"text-white":"","\n    ").concat(l?"shrink-0":"","\n    overflow-hidden\n    break-words\n    word-wrap-break-word\n  ")),logoContainer:x()("\n    w-full\n  "),logoWrapper:x()("\n    w-[72px]\n    h-[26px]\n    relative\n  "),logo:x()("\n    h-full\n    w-full\n    object-center\n    object-cover\n  "),tagsContainer:e=>x()("\n    ".concat("1 Col"===e?"":"md:mb-16","\n    flex\n    gap-x-[6px]\n    text-center\n    mb-4\n  ")),tagWrapper:x()("\n    lg:block\n  "),tag:x()("\n    text-xs\n    cursor-pointer lg:cursor-auto\n  "),videoIcon:e=>x()("\n    absolute\n    inset-[calc(50%-29px)]\n    w-[58px]\n    h-[58px]\n    transition-opacity\n    duration-1000\n    ease-in-out\n    ".concat(e?"opacity-0":"opacity-100","\n  ")),player:x()("\n    lg:h-full\n    lg:w-full\n    [&_video]:object-cover\n  "),topPillText:e=>x()("\n    uppercase\n    bg-ultra-light-purple\n    w-fit\n    px-4\n    py-1\n    font-semibold\n    rounded-full\n    ".concat("1 Col"===e?"text-base":"text-xs","  \n  "))},y=e=>{let{href:n,children:t}=e,a=(0,s.h)(n),i="".concat(n,"?play_video=true");return a?(0,l.jsx)(u.N,{...e,href:n,rel:"noreferrer",target:"_blank",children:t}):(0,l.jsx)(u.N,{...e,href:i,children:t})},N={mobile:{height:48},tablet:{height:48},desktop:{height:48}},k=e=>{var n,t,a,i,s,u,h,x,g,f,v,b,w,k,C;let{layout:T="1 Col",bgColor:B,logo:I,logoDark:A,textData:_,tagsData:L,isVideo:P=!1,isDark:D=!1,id:S,isV2:W=!1,companyData:E,isEventsPageHero:F=!1,tileItemMainImageWithFocalArea:R,tileItemLogoWithFocalArea:M,tileItemLogoDarkWithFocalArea:O,onTagClick:H}=e,{isLg:U}=(0,r.d)(),z={src:D?null!=(b=null==O?void 0:O.image.url)?b:null==A?void 0:A.src:null!=(w=null==M?void 0:M.image.url)?w:null==I?void 0:I.src,altText:D?null==A?void 0:A.altText:null==I?void 0:I.altText,focalArea:D?null==O?void 0:O.focusArea:null==M?void 0:M.focusArea},V=null==_?void 0:_.videoLink,K=m.A["play-video"],G={"1 Col":{desktop:{width:526,height:480},tablet:{width:584,height:418},mobile:{width:256,height:266}},"2 Col":{desktop:{width:296,height:352},tablet:{width:704,height:736},mobile:{width:256,height:288}},"3 Col":{desktop:{width:406,height:416},tablet:{width:704,height:736},mobile:{width:256,height:288}},"4 Col":{desktop:{width:296,height:288},tablet:{width:704,height:736},mobile:{width:256,height:288}}};return(0,l.jsx)("div",{className:j.container,"data-testid":S,children:(0,l.jsxs)("div",{className:j.content(U,T,B,D,void 0!==L,F),children:[(null==R?void 0:R.image.url)&&!P&&(0,l.jsx)("div",{className:j.imageWrapper(T,F,(null==R?void 0:R.focusArea)==="Full"),children:(0,l.jsx)(c.s,{src:null==R?void 0:R.image.url,alt:null!=(k=null==R?void 0:R.image.title)?k:"",width:null==R?void 0:R.image.width,height:null==R?void 0:R.image.height,artDirection:F?{desktop:{width:526,height:480},tablet:{width:584,height:418},mobile:{width:256,height:266}}:G[T],focusArea:null==R?void 0:R.focusArea,className:(null==R?void 0:R.focusArea)==="Full"?"":j.imageBlock(null==R?void 0:R.showBorder)})}),P&&(null==R?void 0:R.image.url)&&V&&(0,l.jsxs)(y,{href:V,className:j.videoWrapper(T,B),children:[(0,l.jsx)(c.s,{src:null==R?void 0:R.image.url,alt:null!=(C=null==R?void 0:R.image.title)?C:"",width:null==R?void 0:R.image.width,height:null==R?void 0:R.image.height,artDirection:G[T],focusArea:null==R?void 0:R.focusArea,className:j.imageBlock(!1)}),W?(0,l.jsx)("div",{className:j.videoIconV2,children:(0,l.jsx)(o.js,{height:20,width:13})}):(0,l.jsx)("div",{className:j.videoIconOnly,children:(0,l.jsx)(K,{height:58,width:58})})]}),(0,l.jsxs)("div",{className:j.textContent(T,B,F,(null==R?void 0:R.focusArea)==="Full"),children:[(null==_?void 0:_.topPillText)&&(0,l.jsx)("h5",{className:j.topPillText(T),children:_.topPillText}),(null==z?void 0:z.src)&&(0,l.jsx)("div",{className:j.logoContainer,children:(0,l.jsx)("div",{className:j.logoWrapper,children:(0,l.jsx)(c.s,{pictureClassName:"h-full",className:"h-full w-auto !object-contain",src:z.src,alt:z.altText||"tile logo",focusArea:z.focalArea,artDirection:{desktop:{height:26},tablet:{height:26},mobile:{height:26}}})})}),_&&(0,l.jsx)(p.e,{..._,subText:null==_?void 0:_.subText,ctaLinkLabel:P?"":_.ctaLinkLabel,isDark:D,layout:T,isVideo:P}),E&&(0,l.jsxs)("div",{className:"flex items-center gap-5 text-xl font-semibold",children:[(null==(t=E.companyLogo)||null==(n=t.image)?void 0:n.url)&&(0,l.jsx)(c.s,{src:null==(i=E.companyLogo)||null==(a=i.image)?void 0:a.url,alt:null==(u=E.companyLogo)||null==(s=u.image)?void 0:s.title,width:null==(x=E.companyLogo)||null==(h=x.image)?void 0:h.width,height:null==(f=E.companyLogo)||null==(g=f.image)?void 0:g.height,artDirection:N,focusArea:null==(v=E.companyLogo)?void 0:v.focusArea}),E.companyName]}),L&&L.length>0&&(0,l.jsx)("div",{className:j.tagsContainer(T),children:L.map((e,n)=>(0,l.jsx)("div",{className:j.tagWrapper,children:(0,l.jsx)(d.Y,{...e,isDark:D,className:j.tag,onClick:()=>{H&&H(e.content)}})},n))})]})]})})};var C=t(82854),T=t(126),B=t(41066),I=t(90005);let A={container:"",content:(e,n)=>x()("\n    flex\n    flex-col\n    gap-y-5\n    lg:gap-y-0\n    lg:justify-between\n    lg:items-end\n    lg:flex-row\n    ".concat(e?"text-white dark":"text-black","\n    ").concat("Product"===n?"text-center w-full max-w-[800px] mx-auto":"","\n  ")),titleContainer:x()("\n    flex\n    flex-col\n    gap-y-2\n  "),title:e=>x()("\n    font-semibold\n    h1\n  "),description:x()("\n    paragraph\n  "),cta:x()("\n    h-[25px]\n  ")},_=e=>{let{title:n,description:t,cta:a,isDark:r=!1,id:s,richTextDescription:o,displayStyle:c="Default"}=e;return(0,l.jsx)("div",{className:A.container,"data-testid":s,children:(0,l.jsxs)("div",{className:A.content(r,c),children:[(0,l.jsxs)("div",{className:A.titleContainer,children:[(0,l.jsx)("h2",{className:A.title(c),children:n}),o?(0,l.jsx)(B.default,{contentBody:o}):t&&(0,l.jsx)("p",{className:A.description,children:t})]}),(null==a?void 0:a.ctaUrl)&&(0,l.jsx)(i.$n,{className:A.cta,icon:"link-arrow",href:(0,I.TE)(a.ctaUrl,a.pageLink),variant:"link",isDark:r,size:"small",children:(null==a?void 0:a.ctaText)||"Read more"})]})})},L={container:e=>x()("\n    mx-auto\n    ".concat(e?"bg-black dark":"","  \n  ")),newFiltersContainer:x()("\n    flex\n    flex-col\n    container\n    px-4\n    lg:px-8\n  "),newFiltersContent:x()("\n    flex\n    flex-col\n    gap-2\n  "),newFiltersSelectContainer:x()("\n    flex\n    flex-col\n    items-center\n    md:flex-row\n    gap-12\n  "),content:e=>x()("\n    flex\n    flex-col\n    overflow-hidden\n    ".concat(e?"text-white":"text-black","\n  ")),tileCardTitleWrapper:x()("\n    mb-[46px]\n    lg:mb-[56px]\n  "),filtersContainer:x()("\n    grid\n    max-w-content-max\n    grid-cols-4\n    gap-x-4\n    xs:grid-cols-16\n    lg:grid-cols-16\n    lg:gap-x-1\n    xl:gap-x-5\n  "),selectWrapper:x()("\n    col-span-full\n    lg:col-span-5\n    !w-full\n    lg:pr-[24px]\n    xl:pr-0\n  "),selectWrapperInner:x()("\n    !min-w-[100%]\n    !max-w-[100%]\n  "),btn:x()("\n    underline\n    underline-offset-2\n    col-span-full\n    text-right\n    lg:text-start\n    sm:mt-[22px]\n    lg:mt-0\n    lg:col-span-1\n  "),toggleContainer:x()("\n    flex\n    flex-col\n    items-center\n    lg:pl-[57px]\n    lg:flex-row\n    lg:gap-x-[60px]\n  "),toggleTitle:e=>x()("\n    h3\n    \n    ".concat(e?"text-white":"text-dark","\n  "))},P=e=>{let{selectProps:n,title:t,description:a,cta:i,isDark:r=!1}=e;return(0,l.jsxs)(l.Fragment,{children:[t||a?(0,l.jsx)("div",{className:L.tileCardTitleWrapper,children:(0,l.jsx)(_,{title:t,description:a,cta:i,isDark:r})}):"",(0,l.jsxs)("div",{className:L.filtersContainer,children:[n&&n.length>0&&n.map((e,n)=>(0,l.jsx)(C.l,{className:L.selectWrapper,selectWrapperClassName:L.selectWrapperInner,label:null==e?void 0:e.label,options:null==e?void 0:e.options,isDark:r},n)),(0,l.jsx)("button",{className:L.btn,children:"Clear all"})]})]})},D=e=>{let{toggleProps:n,title:t,isDark:i=!1}=e,[r,s]=(0,a.useState)(0);return(0,l.jsxs)("div",{className:L.toggleContainer,children:[(0,l.jsx)("h3",{className:L.toggleTitle(i),children:t}),(0,l.jsx)(T.a,{activeIndex:r,toggles:null==n?void 0:n.toggles,setActiveIndex:s,isDark:i})]})},S=e=>{let{selectProps:n,title:t,description:a,cta:i,isDark:r=!1,toggleProps:s,variant:o="dropdowns",id:c}=e;return(0,l.jsx)("div",{className:L.container(r),"data-testid":c,children:(0,l.jsxs)("div",{className:L.content(r),children:["dropdowns"===o&&(0,l.jsx)(P,{selectProps:n,title:t,description:a,cta:i,isDark:r}),"tabs"===o&&(0,l.jsx)(D,{title:t,toggleProps:s,isDark:r})]})})},W={"1 Col":"lg:grid-cols-1","2 Col":"grid-cols-1 md:grid-cols-2 lg:grid-cols-2","3 Col":"grid-cols-1 md:grid-cols-2 lg:grid-cols-3","4 Col":"grid-cols-1 md:grid-cols-2 lg:grid-cols-4"},E={container:e=>x()("\n    container\n    ".concat(e?"dark":"","  \n  ")),content:x()("\n    flex-container\n    flex-col\n    xl:p-0\n    !px-0\n    w-[100%]\n  "),titleContainer:x()("\n    mt-0\n    mb-[46px]\n\n    lg:mb-14\n  "),filtersContainer:x()("\n    mt-0\n    mb-16\n\n    md:mb-[84px]\n\n    lg:mb-[52px]\n  "),cardsContainer:(e,n)=>x()("\n    grid\n    grid-cols-1\n    \n    ".concat("3 Col"!==e?"gap-4":"gap-4 lg:gap-8","\n    ").concat(n&&"3 Col"===e&&"[&>*:nth-child(3n+2)]:lg:mt-[133px]","\n    ").concat(W["".concat(e)],"\n  ")),cardWrapper:x()("\n    relative\n  ")},F=e=>{let{title:n,description:t,richTextDescription:r,isDark:s=!1,layout:o="1 Col",enableStaggered:c=!1,cta:d,cardCollection:u,filtersData:m,tileBgColor:p,id:h,showNextButton:x=!0,topPillText:g,isEventsPageHero:f=!1,onTagClick:v,tileCardsToShow:b=12,displayStyle:w="Default"}=e,j=(0,a.useRef)(null),[y,N]=(0,a.useState)(1),[C,T]=(0,a.useState)(b),B=(null==u?void 0:u.length)||0;return(0,a.useEffect)(()=>{j.current&&j.current.focus()},[u]),(0,l.jsx)("section",{className:E.container(s),"data-testid":h,"data-section":"tile-grid-cards",children:(0,l.jsxs)("div",{className:E.content,children:[n&&(0,l.jsx)("div",{className:E.titleContainer,children:(0,l.jsx)(_,{title:n,richTextDescription:r,description:t,cta:d,isDark:s,displayStyle:w})}),m&&(0,l.jsx)("div",{className:E.filtersContainer,children:(0,l.jsx)(S,{...m,isDark:s})}),u&&(0,l.jsx)("div",{className:E.cardsContainer(o,c),children:u.map((e,n)=>{var t,a,i,r,c,d,u,m,h,x,w,y;let N=(null==e||null==(t=e.cta)?void 0:t.ctaType)==="Button",C=null==e||null==(a=e.cta)?void 0:a.ctaText,T=(null==e||null==(i=e.cta)?void 0:i.ctaUrl)||"",B=null==e||null==(r=e.cta)?void 0:r.pageLink,A={title:null==e?void 0:e.title,description:null==e?void 0:e.description,richTextDescription:null==e?void 0:e.richCardDescription,eyebrow:null==e?void 0:e.eyebrow,btnLabel:N?C:"",btnLink:N?(0,I.TE)(T,B):"",ctaLinkLabel:N?"":C,ctaLink:N?"":(0,I.TE)(T,B),subText:(null==e?void 0:e.isVideo)?null==e?void 0:e.tileVideoDuration:g?null==e?void 0:e.tileCardDateAndTime:"",formModal:null==e||null==(c=e.cta)?void 0:c.formModal,videoLink:(0,I.TE)(T,B),topPillText:g||(null==e?void 0:e.tileCardDateAndTime),layout:o,isDark:s,bgColor:p},_={fill:!1,image:{url:null!=(y=null==e||null==(d=e.tileItemMainImageWithFocalArea)?void 0:d.image.url)?y:"",width:null==e||null==(u=e.tileItemMainImageWithFocalArea)?void 0:u.image.width,height:null==e||null==(m=e.tileItemMainImageWithFocalArea)?void 0:m.image.height},imageLazyLoading:!1,isWhole:!1,title:(null==e||null==(h=e.tileItemMainImageWithFocalArea)?void 0:h.image.title)||""},L=(null==e?void 0:e.tileCardTags)||[],P=null==L?void 0:L.map(e=>({isDark:!1,isActive:!1,content:e,className:""})),D={url:(null==e?void 0:e.isVideo)?null==e||null==(x=e.tileVideo)?void 0:x.url:"",altText:(null==e?void 0:e.isVideo)?null==e||null==(w=e.tileVideo)?void 0:w.url:""},S=n%b;return(0,l.jsxs)("div",{className:E.cardWrapper,children:[0===S&&0!==n&&(0,l.jsxs)("p",{tabIndex:-1,ref:j,className:"absolute top-0 opacity-0",children:[b," more cards has been loaded"]}),(0,l.jsx)(k,{...e,imageData:_,textData:A,tagsData:P,isVideo:null==e?void 0:e.isVideo,videoData:D,bgColor:p,layout:o,isDark:s,isEventsPageHero:f,onTagClick:e=>v&&v(e)})]},n)})}),B>C&&x&&(0,l.jsx)(i.$n,{size:"small",onClick:()=>{N(y+1),T(b*(y+1))},variant:"primary",className:"mx-auto mb-8",isDark:s,children:"Load more"})]})})}},64448:(e,n,t)=>{t.r(n),t.d(n,{IntegrationsTickerBanner:()=>h});var l=t(54568),a=t(91039),i=t(7620),r=t(48258),s=t(5474),o=t(71034),c=t(85610),d=t.n(c);let u={White:"bg-white",Tan:"bg-tan",Black:"dark bg-black","DP Purple":"dark bg-dp-purple","Eerie Black":"dark bg-eerie-black"},m={container:e=>d()("\n      container\n\n      ".concat(u[e],"\n    ")),content:d()("\n    flex-container\n    flex-col\n    px-8\n  "),marquee:d()("\n    w-screen\n    ml-[calc((100vw-100%)/-2)]\n  "),title:e=>d()("\n      col-span-full\n      ".concat(e?"h1-alt":"h1","\n    ")),slickslider:"\n    scale-80\n    [&>.slick-list>.slick-track]:flex\n    mt-[38px]\n    md:mt-[66px] \n  ",slickSlide:"\n    h-auto\n    flex\n    items-center\n    justify-center\n  ",slickLogoContainer:"\n    w-[83px]\n    h-[83px]\n    mx-[13px]\n\n    flex\n    items-center\n    justify-center\n    border\n    border-black\n    rounded-2xl\n    bg-white\n\n    sm:w-[155px]\n    sm:h-[157px]\n    sm:mx-[26px]\n  ",slickLogo:"\n    w-[auto]\n    object-contain\n    h-[40px]\n    max-w-[45px]\n    sm:h-[73px]\n    sm:max-w-[100px]\n  ",partnerTickerDescription:"\n    h4\n    text-center\n    mb-8\n\n    dark:text-white\n  "},p=(0,a.default)(()=>t.e(3131).then(t.bind(t,3131)),{loadableGenerated:{webpack:()=>[3131]},ssr:!0}),h=e=>{var n;let{eyebrow:t,title:a,copy:c,ctaCollection:d,imageCollection:u,id:h,integrationCardImageLazyLoad:x,integrationCardBackgroundColor:g}=e,[f,v]=(0,i.useState)(0),[b,w]=(0,i.useState)(!1),j=(0,i.useRef)(null),y=()=>{setTimeout(()=>{var e;(null==(e=j.current)?void 0:e.contains(document.activeElement))||(v(e=>e+1),w(!1))},10)};return(0,l.jsx)("section",{className:m.container(g),"data-testid":h,children:(0,l.jsxs)("div",{className:m.content,children:[(0,l.jsx)(r.d,{eyebrow:t,heading:a,copy:c,className:"Banner",isDark:"Black"===g,alignment:"center",buttonsCollection:d,customContentClasses:"mb-[22px] md:mb-[30px] lg:mb-4"}),(0,l.jsx)("div",{className:m.marquee,onBlur:y,ref:j,children:(0,l.jsx)(p,{autoFill:!0,className:m.slickslider,pauseOnHover:!0,play:!b,children:null==u||null==(n=u.items)?void 0:n.map((e,n)=>(0,l.jsx)("div",{className:m.slickSlide,children:(0,l.jsx)("div",{className:m.slickLogoContainer,children:e.url?(0,l.jsx)(o.N,{href:e.url,onFocus:()=>{w(!0)},onBlur:y,children:(0,l.jsx)(s._,{src:e.image.url,alt:e.image.title||"Banner Image",width:"300",height:"300",className:m.slickLogo,loading:x?"lazy":"eager"})}):(0,l.jsx)(s._,{src:e.image.url,alt:e.image.title||"Banner Image",width:"300",height:"300",className:m.slickLogo,loading:x?"lazy":"eager"})})},n))})},f)]})})}},68808:(e,n,t)=>{t.d(n,{F:()=>l.VideoBlock});var l=t(23615)},69996:(e,n,t)=>{t.r(n),t.d(n,{FeatureTiles:()=>j});var l=t(54568),a=t(7620),i=t(85906),r=t(15518),s=t(3087),o=t(5474),c=t(85610),d=t.n(c);let u="transition-all duration-1000 ease-in-out",m="translate-y-0 opacity-100",p="translate-y-10 opacity-0",h={White:"bg-white",Black:"bg-black",Tan:"bg-tan",Purple:"bg-dp-purple","Eerie Black":"bg-eerie-black",Transparent:"bg-eerie-black"},x={small:d()("\n    h-[288px]\n    aspect-[256/288]\n    md:aspect-[704/480]\n    lg:aspect-[624/480]\n  "),large:d()("\n    h-[480px]\n    aspect-[256/480]\n    md:aspect-[704/992]\n    lg:aspect-[624/992]\n  ")},g={Default:"text-black",White:"text-white",Blue:"text-blue",Pink:"text-light-pink",Purple:"text-dp-purple"},f=["White","Tan","Transparent"],v={container:"feature-tiles container",content:d()("\n    grid-container\n  "),masonryContainer:d()("\n    col-span-full\n  "),featureTilesContainer:d()("\n    col-span-full\n    [&_.feature-tile-wrapper]:mb-8\n    [&_.feature-tile-wrapper:last-child]:mb-0\n  "),tileWrapper:(e,n,t,l)=>d()("\n    flex\n    flex-col\n    overflow-hidden\n    rounded-[30px]\n    w-full\n    h-auto\n\n    lg:max-w-[calc(50%-18px)]\n\n    ".concat(!l&&d()("\n      mb-8\n      [&:last-child]:mb-0\n    "),"\n\n    ").concat(x[e],"\n\n    ").concat(n&&d()("\n        relative\n      "),"\n\n    ").concat(u,"\n    ").concat(t?m:p,"\n  ")),titleContainer:(e,n)=>d()("\n    py-9\n    text-center\n    w-full\n\n    xs:px-4\n    sm:px-[62px]\n    lg:px-[38px]\n\n    ".concat(!n?h[e]:"bg-transparent","\n\n    ").concat(n&&d()("\n        absolute\n        top-0\n        left-0\n        w-full\n        z-10\n      "),"\n  ")),title:(e,n,t,l)=>d()("\n    \n    font-semibold\n    text-xl\n    w-full\n\n    sm:text-[30px]\n    sm:leading-[42px]\n\n    ".concat(n&&d()("\n        bg-transparent\n      "),"\n\n    ").concat("Default"===t?d()("".concat(f.includes(e)?"text-black":"text-white")):g[t],"\n\n    ").concat(u,"\n    ").concat(l?m:p,"\n  ")),imageWrapper:d()("\n    relative\n    grow\n    bg-eerie-black\n  ")},b={small:{mobile:{width:256,height:288},tablet:{width:704,height:480},desktop:{width:624,height:480}},tall:{mobile:{width:256,height:480},tablet:{width:704,height:992},desktop:{width:624,height:992}}},w=e=>{var n,t,a,r;let{variant:c,bgColor:d,isFullImage:u,titleTextColor:m,headline:p,imageData:h}=e,{ref:x,inView:g}=(0,s.Wx)({triggerOnce:!0}),{isLg:f}=(0,i.d)();return(0,l.jsxs)("div",{ref:x,className:v.tileWrapper(c,u,g,f),children:[(0,l.jsx)("div",{className:v.titleContainer(d,u),children:(0,l.jsx)("h3",{className:v.title(d,u,m,g),children:p})}),(null==h||null==(n=h.image)?void 0:n.url)&&(0,l.jsx)("div",{className:v.imageWrapper,children:(0,l.jsx)(o.s,{src:null==h||null==(t=h.image)?void 0:t.url,alt:(null==h?void 0:h.altText)||"",width:null==h||null==(a=h.image)?void 0:a.width,height:null==h||null==(r=h.image)?void 0:r.height,artDirection:h.artDirection,focusArea:null==h?void 0:h.focusArea})})]})},j=e=>{var n;let{cardsCollection:t,id:s="feature-tiles"}=e,{isLg:o}=(0,i.d)(),c=(null==t||null==(n=t.items)?void 0:n.map(e=>{var n,t,l,a,i,r,s,o,c,d;let u={altText:(null==e||null==(t=e.imageWithFocalArea)||null==(n=t.image)?void 0:n.title)||(null==e?void 0:e.title)||"",image:{url:(null==e||null==(l=e.imageWithFocalArea)?void 0:l.image.url)||"/",height:null==e||null==(i=e.imageWithFocalArea)||null==(a=i.image)?void 0:a.height,width:null==e||null==(s=e.imageWithFocalArea)||null==(r=s.image)?void 0:r.width},title:(null==e||null==(c=e.imageWithFocalArea)||null==(o=c.image)?void 0:o.title)||(null==e?void 0:e.title)||"",artDirection:e.isTallCard?b.tall:b.small,focusArea:null==(d=e.imageWithFocalArea)?void 0:d.focusArea},m=(null==e?void 0:e.titleTextColor)||"Default",p=(null==e?void 0:e.bgColor)||"Transparent";return{...e,variant:e.isTallCard?"large":"small",imageData:u,isFullImage:null==e?void 0:e.isFullImage,titleTextColor:m,bgColor:p}}))||[];return(0,l.jsx)("section",{className:v.container,"data-testid":s,"data-section":"feature-tile-card",children:(0,l.jsx)("div",{className:v.content,children:o?(0,l.jsx)(r.yK,{className:v.masonryContainer,gap:36,align:"justify",children:(null==c?void 0:c.length)>0&&(null==c?void 0:c.map((e,n)=>(0,a.createElement)(w,{...e,key:n})))}):(0,l.jsx)("div",{className:v.featureTilesContainer,children:(null==c?void 0:c.length)>0&&(null==c?void 0:c.map((e,n)=>(0,a.createElement)(w,{...e,key:n})))})})})}},72068:(e,n,t)=>{t.d(n,{V:()=>i});var l=t(54568),a=t(22566);let i=e=>{var n;let{features:t,columns:i,isAllOpen:r=!1,multipleOpen:s=!1}=e,o=null==t||null==(n=t.features)?void 0:n.map(e=>({title:e.title,icon:"/assets/images/".concat(e.icon)}));return(0,l.jsx)(a.O,{accordions:o,features:t.features,isOpenFirst:!0,className:"!block pr-12",isAllOpen:r,isFeatureDropdown:!0,tierCardsCount:i,multipleOpen:s,preserveDesktop:!0})}},79034:(e,n,t)=>{t.d(n,{TestimonialsCarousel:()=>u});var l=t(54568),a=t(7620),i=t(61773),r=t(23263);t(86507),t(60739);var s=t(85610),o=t.n(s);let c={White:"bg-white","Deep Blue":"bg-deep-blue"},d={section:e=>o()("\n      ".concat(c[e],"\n      py-[64px]\n      lg:py-[96px]\n    ")),container:o()("container mx-auto px-4"),inner:o()("relative mx-auto flex max-w-[1280px] flex-col gap-16 lg:gap-[96px]"),title:o()("mb-8 text-center text-dp-purple text-lg font-bold")},u=e=>{let{testimonialsCollection:n,backgroundColor:t="White"}=e,s=a.useRef(null),o=e=>{var n;return null==(n=s.current)?void 0:n.slickGoTo(e,!0)};return(0,l.jsx)("section",{className:"".concat(d.section(t)),children:(0,l.jsx)("div",{className:d.container,children:(0,l.jsx)("div",{className:d.inner,children:(0,l.jsxs)("div",{className:"testimonials-carousel relative",children:[(0,l.jsx)("style",{dangerouslySetInnerHTML:{__html:"\n  .testimonials-carousel .slick-list {\n    overflow: visible !important;\n  }\n  .testimonials-carousel .slick-track {\n    display: flex !important;\n    align-items: center !important;\n  }\n  @media (min-width: 768px) {\n    .testimonials-carousel .slick-slide {\n        transition: all 0.3s ease;\n        transform: scale(0.85);\n    }\n  }\n  .testimonials-carousel .slick-slide {\n    transition: all 0.3s ease;\n    transform: scale(0.95);\n  }\n  .testimonials-carousel .slick-slide.slick-center {\n    transform: scale(1);\n  }\n  .slick-slide .preview-content {\n    display: flex;\n    min-height: 300px;\n    min-width: 300px;\n  }\n  @media (min-width: 768px) {\n    .slick-slide .preview-content {\n        display: flex;\n        min-height: 400px;\n        min-width: 400px;\n    }\n  }\n  .slick-slide.slick-active.slick-center .preview-content {\n    display: none;\n  }\n  .slick-slide.slick-active.slick-center .active-content {\n    display: flex;\n    min-height: 600px;\n  }\n  @media (max-width: 768px) {\n    .slick-slide.slick-active.slick-center .active-content {\n      min-height: 400px;\n    }\n  }\n  .active-content {\n    display: none;\n  }\n  .testimonials-carousel .slick-prev,\n  .testimonials-carousel .slick-next {\n    z-index: 10;\n    width: auto;\n    height: auto;\n    transform: none;\n    opacity: 1;\n    visibility: visible;\n  }\n  .testimonials-carousel .slick-prev:before,\n  .testimonials-carousel .slick-next:before {\n    display: none;\n  }\n  .testimonials-carousel .carousel-controls {\n    position: relative;\n    height: 60px;\n    width: 100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n"}}),(0,l.jsxs)("div",{className:"overflow-visible",children:[(0,l.jsx)("div",{className:"carousel-controls md:!hidden",children:(0,l.jsxs)("div",{className:"mb-6 flex items-center justify-center gap-4",children:[(0,l.jsx)(function(e){let{onClick:n,disabled:t,background:a}=e,i="";return i="Deep Blue"===a?t?"border-2 border-white bg-transparent text-white":"bg-ultra-light-purple text-deep-blue":"bg-deep-blue text-white",(0,l.jsx)("button",{onClick:n,className:"z-10 flex h-8 w-8 items-center justify-center rounded-full transition-colors hover:opacity-80 ".concat(i),"aria-label":"Previous testimonial",disabled:t,children:(0,l.jsx)("svg",{width:"18",height:"18",viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,l.jsx)("path",{d:"M15.5214 9.82702L4.45699 9.82702L8.47055 13.8406C8.79598 14.166 8.79598 14.6541 8.47055 14.9796C8.14513 15.305 7.657 15.305 7.33157 14.9796L1.90784 9.55583C1.58242 9.23041 1.58242 8.74228 1.90784 8.41685L7.33157 2.99307C7.49428 2.83036 7.71123 2.77612 7.92818 2.77612C8.14513 2.77612 8.36208 2.83036 8.52479 2.99307C8.85023 3.31849 8.85023 3.80663 8.52479 4.13205L4.51123 8.14567L15.5214 8.14567C15.9553 8.14567 16.335 8.52533 16.335 8.95923C16.335 9.39312 15.9553 9.82702 15.5214 9.82702Z",fill:"currentColor"})})})},{onClick:()=>{var e;return null==(e=s.current)?void 0:e.slickPrev()},background:t}),(0,l.jsx)(function(e){let{onClick:n,disabled:t,background:a}=e,i="";return i="Deep Blue"===a?t?"border-2 border-white bg-transparent text-white":"bg-ultra-light-purple text-deep-blue":"bg-deep-blue text-white",(0,l.jsx)("button",{onClick:n,className:"z-10 flex h-8 w-8 items-center justify-center rounded-full transition-colors hover:opacity-80 ".concat(i),"aria-label":"Next testimonial",disabled:t,children:(0,l.jsx)("svg",{width:"18",height:"19",viewBox:"0 0 18 19",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:(0,l.jsx)("path",{d:"M2.4786 8.76087H13.543L9.52945 4.74731C9.20402 4.42189 9.20402 3.93375 9.52945 3.60833C9.85487 3.2829 10.343 3.2829 10.6684 3.60833L16.0922 9.03205C16.4176 9.35748 16.4176 9.84561 16.0922 10.171L10.6684 15.5948C10.5057 15.7575 10.2888 15.8118 10.0718 15.8118C9.85487 15.8118 9.63792 15.7575 9.47521 15.5948C9.14977 15.2694 9.14977 14.7813 9.47521 14.4558L13.4888 10.4422H2.4786C2.0447 10.4422 1.66504 10.0626 1.66504 9.62866C1.66504 9.19477 2.0447 8.76087 2.4786 8.76087Z",fill:"currentColor"})})})},{onClick:()=>{var e;return null==(e=s.current)?void 0:e.slickNext()},background:t})]})}),(0,l.jsx)(r.A,{ref:s,...{dots:!1,infinite:!0,speed:500,swipe:!0,draggable:!0,variableWidth:!1,slidesToShow:1,slidesToScroll:1,centerMode:!0,centerPadding:"20%",arrows:!1,cssEase:"cubic-bezier(0.25, 0.1, 0.25, 1)",responsive:[{breakpoint:1024,settings:{centerPadding:"15%"}},{breakpoint:768,settings:{centerPadding:"10%"}}]},className:"slider-container",children:n.items.map((e,n)=>{var a;return(0,l.jsx)("div",{className:"cursor-pointer px-2 transition-all duration-300",onClick:()=>o(n),role:"button",tabIndex:0,onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),o(n))},children:(0,l.jsx)("div",{className:"relative mx-auto overflow-hidden rounded-[30px] ".concat("Deep Blue"===t?"bg-ultra-dark-purple":"bg-deep-blue"," px-4 py-6 text-white lg:px-6 lg:py-8"),children:(0,l.jsxs)("div",{className:"h-full",children:[(0,l.jsx)("div",{className:"flex h-full flex-col justify-center",children:(null==(a=e.backgroundImage)?void 0:a.url)&&(0,l.jsxs)("div",{className:"preview-content flex items-center justify-center",children:[(0,l.jsx)(i.default,{src:e.backgroundImage.url,alt:e.backgroundImage.description||e.backgroundImage.title||"",width:150,height:70,className:"absolute left-0 top-0 h-full w-full object-cover opacity-50"}),(0,l.jsx)(i.default,{src:e.companyLogo.url,alt:e.backgroundImage.description||e.backgroundImage.title||"",width:150,height:70,className:"z-[1] h-full max-h-[100px] w-full max-w-[250px]"})]})}),(0,l.jsxs)("div",{className:"active-content flex flex-col justify-between gap-16",children:[(0,l.jsx)("h3",{className:"paragraph-small !semibold mx-auto max-w-[360px] text-center",children:e.overview}),(0,l.jsxs)("blockquote",{className:"mx-auto max-w-[80%] text-center font-seasonvf-serif text-[20px] leading-tight lg:text-[48px]",children:["“",e.quote,"”"]}),(0,l.jsxs)("div",{className:"flex w-full flex-row items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex flex-col md:flex-row md:gap-2",children:[(0,l.jsx)("p",{className:"paragraph-2xs lg:paragraph !semibold",children:e.author}),(0,l.jsx)("p",{className:"paragraph-2xs lg:paragraph text-dark-tan",children:e.title})]}),(0,l.jsx)(i.default,{src:e.companyLogo.url,alt:e.companyLogo.title||"",width:150,height:70,className:"h-[30px] w-auto max-w-[100px] lg:max-w-none"})]})]})]})})},e.id)})})]})]})})})})}},79184:(e,n,t)=>{t.r(n),t.d(n,{CenteredTextAndImage:()=>x});var l=t(54568),a=t(61773),i=t(85906),r=t(7620),s=t(52112),o=t(48258),c=t(61864),d=t(5474),u=t(85610),m=t.n(u);let p={White:"bg-white",Black:"bg-black dark",Tan:"bg-tan","Eerie Black":"bg-eerie-black dark",Purple:"bg-dp-purple dark","Light Tan":"bg-light-tan","Primary Gradient":"bg-gradient-to-b from-dp-purple via-light-purple to-light-purple","Deep Blue":"bg-deep-blue"},h={container:(e,n,t)=>m()("\n      ".concat("section-header"===n?"py-[64px] lg:py-[96px] lg:pt-0 lg:pb-0":"bottom"!==t?"py-[64px] lg:py-[96px]":"container","\n      ").concat(p[e],"\n  ")),content:(e,n,t,l)=>m()("\n      container mx-auto px-4 lg:px-8\n      ".concat("bottom"===t&&"default"===n?e&&l?"pt-14 lg:pt-84px":e&&!l?"lg:pt-16px":"py-14 lg:py-84px":"","\n    ")),contentInner:(e,n,t)=>m()("\n      mx-auto \n      \n      ".concat("section-header"===n?"max-w-[800px]":"max-w-[1280px]","\n      \n      items-center\n      ").concat("Deep Blue"===e?"text-white":"","\n      \n      ").concat(t?"block":"flex flex-col ","\n    ")),imageWrapper:(e,n)=>m()("\n    ".concat("default"===e&&"bottom"===n?"":"relative w-full md:static md:h-auto md:max-h-none md:max-w-[900px]")),image:(e,n,t,l)=>m()("\n    ".concat("default"===n&&"bottom"===t||"default"===n&&l?"rounded-[30px] overflow-hidden":"h-auto rounded-[16px] object-contain md:static md:h-auto  md:w-full md:max-w-[900px] md:object-contain","\n      w-full \n      ").concat(e?"box-border border-[1px] border-tan":"","\n  ")),pill:(e,n)=>m()("\n      btn-large\n      mr-45px\n      pb-6px\n      border-b-2\n\n      lg:mr-90px\n\n      [&:last-of-type]:mr-0\n\n      ".concat(n?"text-white":"text-black","\n      ").concat(e&&(n?"!border-b-white":"!border-b-black"),"\n      ").concat(!e&&"\n          border-b-transparent\n          ".concat(n?"hover:border-b-white":"hover:border-b-black","\n        "),"\n  ")),imgHolder:m()("\n    w-full\n    h-20\n    bg-black\n  "),swiper:m()("\n    flex\n    w-full\n    overflow-hidden\n\n    [&>.swiper-wrapper]:flex\n  "),swiperItem:m()("\n    w-[80%]\n    overflow-hidden\n  "),swiperImage:m()("\n    rounded-[20px]\n  "),swiperLabelContainer:m()("\n    w-full\n    flex\n    justify-center\n    lg:my-16 \n    my-8\n  "),swiperLabel:m()("\n    relative\n    text-center\n    text-sm\n    text-dp-purple\n    font-bold\n\n    before:absolute\n    before:w-full\n    before:h-0.5\n    before:bg-dp-purple\n    before:bottom-[-6px]\n  "),imageOnlySection:e=>m()("\n    mb-8\n    hidden\n    md:mb-0\n    md:block\n    ".concat(p[e],"\n  "))},x=e=>{var n,t,u,m,p,x,g,f,v,b,w,j,y,N,k,C,T,B,I,A,_,L,P,D,S,W,E,F,R,M,O,H,U,z,V,K;let{imagesCollection:G,alignment:$="center",heading:q,secondaryHeading:Y,secondaryHeadingGradient:Z,copy:Q,buttonsCollection:X,backgroundColor:J="White",id:ee="centeredTextAndImage",eyebrow:en,eyebrowImage:et,imageLocation:el="bottom",variant:ea="default"}=e,ei=null!=ea?ea:"default",er=null!=el?el:"bottom",{isMd:es}=(0,i.d)(),eo=(0,r.useRef)(null),[ec,ed]=(0,r.useState)(80);(0,r.useEffect)(()=>{let e=()=>{let e=document.querySelector("header");e&&ed(e.getBoundingClientRect().height)};e(),window.addEventListener("resize",e);let n=setTimeout(e,400);return()=>{window.removeEventListener("resize",e),clearTimeout(n)}},[]);let eu="Purple"===J,em=["Black","Eerie Black","Purple","Deep Blue"].includes(J),ep=(null!=(K=null==G?void 0:G.items)?K:[]).length>0,eh=null==G||null==(n=G.items)?void 0:n.some(e=>e.heading&&""!==e.heading.trim()||!1),ex={mobile:{width:256,height:224},tablet:{width:704,height:320},desktop:{width:1280,height:640}},eg=ep&&!q&&!Q&&(!X||(null==(t=X.items)?void 0:t.length)===0)&&!(null==(u=G.items[0])?void 0:u.heading)&&"centered-deep-blue"!==ei,ef="background"===er&&ep,ev=ef?null==G||null==(p=G.items[0])||null==(m=p.imageWithFocalArea)?void 0:m.image.url:null,eb="Primary Gradient"===J;return(0,l.jsxs)("section",{className:"".concat(eg?h.imageOnlySection(J):h.container(J,ei,er)," ").concat(eb?"flex items-center":"hero"===ei?"flex h-[660px] items-center":""," ").concat(ef&&!eb?"relative min-h-[500px] w-full":""),"data-testid":ee,"data-section":"centered-text-and-images",style:{...ef&&ev?{backgroundImage:"url(".concat(ev,")"),backgroundSize:"cover",backgroundPosition:"center",backgroundRepeat:"no-repeat",position:"relative",zIndex:0}:{},...eb?{height:"calc(100vh - ".concat(ec,"px)")}:{}},children:[ef&&(0,l.jsx)("div",{className:"absolute inset-0 z-0","aria-hidden":"true",style:{zIndex:1}}),eg?(0,l.jsx)(a.default,{src:null==G||null==(g=G.items[0])||null==(x=g.imageWithFocalArea)?void 0:x.image.url,alt:null==G||null==(v=G.items[0])||null==(f=v.imageWithFocalArea)?void 0:f.image.title,width:null==G||null==(w=G.items[0])||null==(b=w.imageWithFocalArea)?void 0:b.image.width,height:null==G||null==(y=G.items[0])||null==(j=y.imageWithFocalArea)?void 0:j.image.height,className:"height-auto h-auto w-full"}):(0,l.jsx)("div",{className:"".concat(h.content(ep,ei,er,eh)," ").concat(ef?"relative z-10":""),style:ef?{position:"relative",zIndex:2}:void 0,children:(0,l.jsxs)("div",{className:"".concat(h.contentInner(J,ei,eh)," ").concat(ef||eb?"text-white":""),children:["top"===er&&ep&&!eh&&!ef&&(0,l.jsx)("div",{className:"relative h-[300px] max-h-[350px] w-full md:static md:h-auto md:max-h-none md:max-w-[900px]",children:(0,l.jsx)(a.default,{src:null==G||null==(k=G.items[0])||null==(N=k.imageWithFocalArea)?void 0:N.image.url,alt:null==G||null==(T=G.items[0])||null==(C=T.imageWithFocalArea)?void 0:C.image.title,width:null==G||null==(I=G.items[0])||null==(B=I.imageWithFocalArea)?void 0:B.image.width,height:null==G||null==(_=G.items[0])||null==(A=_.imageWithFocalArea)?void 0:A.image.height,className:"absolute left-0 top-0 h-full w-[100vw] max-w-none object-cover object-left md:static md:h-auto md:w-full md:max-w-[900px] md:object-contain"})}),et&&(0,l.jsx)(a.default,{src:et.url,alt:et.alt||"",width:et.width||40,height:et.height||40,style:{margin:"0 auto",display:"block",paddingBottom:"2rem"}}),(0,l.jsx)(o.d,{...(()=>{let e={eyebrow:en,alignment:$,heading:q,isDark:em,copy:Q,isBgPurple:eu,buttonsCollection:X,isBtnSecondary:!1,secondaryHeading:Y,secondaryHeadingGradient:Z};return"centered-deep-blue"===ei?{...e,className:"Default",customH2Classes:"text-center text-deep-blue h1 font-semibold",customContentClasses:"paragraph mb-8 text-center text-deep-blue",promoCardRenderTitleAsH2:!0,customButtonContainerClasses:"flex justify-center",customButtonClasses:"btn-primary-secondary-styles-lg btn-primary-dark"}:"Deep Blue"===J?{...e,className:"Default",customH2Classes:"text-center text-white h1 font-semibold",customContentClasses:"paragraph mb-8 text-center text-white",promoCardRenderTitleAsH2:!0,customButtonContainerClasses:"flex justify-center",customButtonClasses:"btn-primary-secondary-styles-lg btn-primary-dark"}:"section-header"===ei?{...e,className:"Default",customH2Classes:"text-center text-deep-blue h1",promoCardRenderTitleAsH2:!0}:{...e,className:"Default",customH2Classes:"max-w-4xl h1",promoCardRenderTitleAsH2:!1}})()}),"bottom"===er&&ep&&!eh&&!ef&&(0,l.jsx)("div",{className:h.imageWrapper(ei,er),children:(0,l.jsx)(d.s,{className:h.image(null==G||null==(P=G.items[0])||null==(L=P.imageWithFocalArea)?void 0:L.showBorder,ei,er),src:null==G||null==(S=G.items[0])||null==(D=S.imageWithFocalArea)?void 0:D.image.url,alt:null==G||null==(E=G.items[0])||null==(W=E.imageWithFocalArea)?void 0:W.image.title,focusArea:null==G||null==(R=G.items[0])||null==(F=R.imageWithFocalArea)?void 0:F.focusArea,width:null==G||null==(O=G.items[0])||null==(M=O.imageWithFocalArea)?void 0:M.image.width,height:null==G||null==(U=G.items[0])||null==(H=U.imageWithFocalArea)?void 0:H.image.height,artDirection:ex})}),es&&ep&&eh&&!ef&&(0,l.jsx)(c.t,{activePillClass:h.pill(!0,em),pillClass:h.pill(!1,em),tabs:null==G||null==(z=G.items)?void 0:z.map(e=>{var n,t,a,i,r,s,o,c,u,m,p;return{title:e.heading||"",content:(0,l.jsx)(d.s,{className:h.image(null==(n=e.imageWithFocalArea)?void 0:n.showBorder,ei,er,eh),src:null==(a=e.imageWithFocalArea)||null==(t=a.image)?void 0:t.url,alt:null==(r=e.imageWithFocalArea)||null==(i=r.image)?void 0:i.title,focusArea:null==(s=e.imageWithFocalArea)?void 0:s.focusArea,width:null==(c=e.imageWithFocalArea)||null==(o=c.image)?void 0:o.width,height:null==(m=e.imageWithFocalArea)||null==(u=m.image)?void 0:u.height,artDirection:ex}),showBorder:null==(p=e.imageWithFocalArea)?void 0:p.showBorder}})}),ep&&eh&&!es&&!ef&&(0,l.jsx)(s.RC,{...{spaceBetween:12,slidesPerView:1.25,centeredSlides:!0},className:h.swiper,ref:eo,children:null==G||null==(V=G.items)?void 0:V.map((e,n)=>{var t,a,i,r,o,c,u,m,p;return(0,l.jsxs)(s.qr,{className:h.swiperItem,children:[(0,l.jsx)("div",{className:h.swiperLabelContainer,children:(0,l.jsx)("p",{tabIndex:0,className:h.swiperLabel,children:e.heading})}),(0,l.jsx)(d.s,{src:null==(a=e.imageWithFocalArea)||null==(t=a.image)?void 0:t.url,alt:null==(r=e.imageWithFocalArea)||null==(i=r.image)?void 0:i.title,focusArea:null==(o=e.imageWithFocalArea)?void 0:o.focusArea,artDirection:ex,width:null==(u=e.imageWithFocalArea)||null==(c=u.image)?void 0:c.width,height:null==(p=e.imageWithFocalArea)||null==(m=p.image)?void 0:m.height})]},n)})})]})})]})}},79364:(e,n,t)=>{t.d(n,{e:()=>l.TileCardText});var l=t(36819)},86718:(e,n,t)=>{t.r(n),t.d(n,{ImageBlock:()=>c});var l=t(54568),a=t(7620),i=t(5474),r=t(85610),s=t.n(r);let o={imageContainer:e=>s()("\n      relative\n      w-full\n      h-full\n      overflow-hidden\n\n      ".concat(e,"\n      \n    ")),imageWithFocalPoint:e=>s()("\n      max-w-4xl\n      w-full\n      ".concat(e?"[&_img]:box-border [&_img]:border-[1px] [&_img]:border-tan":"","\n    ")),image:(e,n,t)=>s()("\n      ".concat(e?"object-cover\n        ".concat(n,"\n        ").concat(t,"\n        "):"","\n      rounded-[30px]      \n    ")),captions:s()("my-[18px] text-center text-xs container mx-auto px-8"),noFocalPointContainer:e=>s()("\n      my-0\n      mx-auto\n      p-0\n      w-full\n      max-w-4xl\n      ".concat(e?"[&_img]:box-border [&_img]:border-[1px] [&_img]:border-tan":"","\n    "))},c=e=>{var n,t,r,s,c,d,u;let{className:m="",focalPoint:p,imageLazyLoading:h,artDirection:x,imageWithFocalArea:g,centerSmallImage:f=!1,imagePriority:v,caption:b,altText:w}=e,j=(0,a.useRef)(null);return(0,a.useEffect)(()=>{(null==j?void 0:j.current)&&p&&(j.current.style.left=(null==p?void 0:p.x)+"px",j.current.style.top=(null==p?void 0:p.y)+"px")},[p]),p?(0,l.jsx)("div",{className:o.imageContainer(m),children:(0,l.jsxs)("div",{ref:j,className:o.imageWithFocalPoint(null==g?void 0:g.showBorder),children:[(0,l.jsx)(i.s,{className:o.image(!1,m,f),src:null==g||null==(n=g.image)?void 0:n.url,focusArea:null==g?void 0:g.focusArea,width:null==g||null==(t=g.image)?void 0:t.width,height:null==g||null==(r=g.image)?void 0:r.height,alt:null!=w?w:(null==g?void 0:g.image.title)||"Placeholder",loading:h?"lazy":"eager",artDirection:x,imagePriority:v}),b?(0,l.jsx)("div",{className:o.captions,children:b}):null]})}):(0,l.jsxs)("div",{className:o.noFocalPointContainer(null==g?void 0:g.showBorder),children:[(0,l.jsx)(i.s,{className:o.image(!0,m,f),src:null==g||null==(s=g.image)?void 0:s.url,focusArea:null==g?void 0:g.focusArea,width:null==g||null==(c=g.image)?void 0:c.width,height:null==g||null==(d=g.image)?void 0:d.height,alt:(null==g||null==(u=g.image)?void 0:u.title)||"Placeholder",loading:h?"lazy":"eager",artDirection:x,imagePriority:v}),b?(0,l.jsx)("div",{className:o.captions,children:b}):null]})}},89520:(e,n,t)=>{t.r(n),t.d(n,{SEOHero:()=>w});var l=t(54568),a=t(7620),i=t(61773),r=t(85906),s=t(80460),o=t(89578),c=t(36561),d=t(5474),u=t(79900),m=t(41066),p=t(31328),h=t(44103),x=t(36935),g=t(85610),f=t.n(g),v=t(10193);let b={container:e=>f()("\n      container\n\n      ".concat(v.t[e],"\n    ")),content:"grid-container !grid-cols-4 md:!grid-cols-16 lg:!grid-cols-16",disclaimer:"col-span-full mt-6",details:(e,n)=>f()("\n\n    ".concat(!e?"flex flex-col justify-center items-start":"","\n    md:col-span-7\n    ").concat("Image Left"===n&&"md:col-start-10","\n    col-span-12\n    relative\n  ")),buttonContainer:f()("\n      flex\n      gap-6\n      items-center\n      w-full\n      justify-center\n      sm:justify-start\n    "),figure:e=>f()("\n    md:col-span-8\n    col-span-12\n    mt-8\n    overflow-hidden\n    relative\n    md:mt-0\n    ".concat("Image Left"===e&&"md:-order-1","\n    ").concat("Image Right"===e&&"md:col-start-9","\n  ")),imageBlock:e=>f()("\n    rounded-[30px]\n    ".concat(e&&"\n        box-border\n        border\n        border-tan\n      ","\n  ")),form:'[&_button[type="submit"]]:w-auto',formWrapper:"mt-8 md:pt-3"},w=e=>{var n,t,g,f,v,w;let{id:j,background:y="White",description:N,eyebrow:k,marketoForm:C,title:T,titleDescription:B,subtitle:I,titleTextColor:A,layout:_,cta:L,imageWithFocalArea:P,imagePriority:D,disclaimer:S}=e,W=["Black","Eerie Black","Dialpad Purple"].includes(y),E="Dialpad Purple"===y,F=null==C?void 0:C.id,R=null==C?void 0:C.backgroundColor,M=(0,a.useContext)(s.GatedAssetsContext),O=(null==M||null==(n=M.uploadImageOrPdf)?void 0:n.url)||(null==M?void 0:M.linkToAsset)||"",H=M.unGated||!1,{isLg:U}=(0,r.d)(),z=U?"large":"small";return"Phone Numbers"===_?(0,l.jsxs)("div",{className:"px-4 py-8 lg:py-16",children:[(0,l.jsxs)("div",{className:"mx-auto flex max-w-[1280px] flex-col lg:flex-row lg:items-start lg:justify-between",children:[(0,l.jsxs)("div",{className:"text-center lg:w-5/12 lg:text-left",children:[(0,l.jsx)("h1",{className:"mb-4 text-5xl font-bold lg:mb-8 ".concat("purple"===A?"text-dp-purple":""),children:T}),B&&(0,l.jsx)("div",{className:"paragraph mb-8 lg:mb-16",children:(0,l.jsx)(m.default,{contentBody:B})}),(0,l.jsx)("div",{className:"hidden lg:block",children:(0,l.jsxs)("div",{className:"text-left",children:[(0,l.jsx)("h2",{className:"mb-2 text-[32px]",children:I}),(0,l.jsx)("div",{className:"paragraph mb-8",children:(0,l.jsx)(m.default,{contentBody:N})}),(0,l.jsx)("div",{className:"flex w-full flex-col items-center gap-4 md:flex-row md:flex-wrap md:justify-start",children:h.X.map(e=>{let{country:n,number:t,icon:a}=e;return(0,l.jsxs)(p.$n,{href:"tel:".concat(t),variant:"telephone",size:z,children:[(0,l.jsx)("span",{children:a}),(0,l.jsx)("span",{children:t})]},n)})})]})})]}),(0,l.jsx)("div",{className:"rounded-[15px] ".concat("light tan"===R?"bg-light-tan":"bg-white"," p-[32px_16px] shadow-lg lg:w-6/12 lg:rounded-[24px] lg:p-[48px_40px] [&_.marketo-caret-icon]:top-1/2 [&_.marketo-caret-icon]:-translate-y-1/2"),children:(0,l.jsx)(c.E,{formId:C.id,variant:"default",submitText:"Contact sales"})})]}),(0,l.jsx)("div",{className:"mt-8 lg:hidden",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h2",{className:"mb-4 text-[32px]",children:I}),(0,l.jsx)("div",{className:"paragraph mb-8",children:(0,l.jsx)(m.default,{contentBody:N})}),(0,l.jsx)("div",{className:"flex w-full flex-col items-center justify-center gap-4 md:flex-row md:flex-wrap",children:h.X.map(e=>{let{country:n,number:t}=e;return(0,l.jsxs)(p.$n,{href:"tel:".concat(t),variant:"telephone",size:z,children:[x.F[n]&&(0,l.jsx)("span",{className:"mr-2",children:(0,l.jsx)(i.default,{src:x.F[n],alt:"".concat(n," flag"),width:20,height:20,className:"inline h-5 w-5",unoptimized:!0})}),(0,l.jsx)("span",{children:t})]},n)})})]})})]}):(0,l.jsx)("section",{className:"".concat(b.container(y)," seo-hero"),"data-section":"seo-hero","data-testid":j,children:(0,l.jsxs)("div",{className:b.content,children:[(0,l.jsxs)("div",{className:b.details(!!F,_),children:[(0,l.jsx)(u.K,{description:N,eyebrow:k,isDark:W,title:T,variant:"poster",buttonCollection:L.items}),H&&(0,l.jsx)(o.CTAButtonsOrLinks,{buttonsCollection:{items:[{ctaText:M.unGatedAssetDownloadButtonText||"Download Now",ctaType:"Button",ctaUrl:O}]},isDark:!1,isBgPurple:!1,isBtnSecondary:!1,withDefaultButtonSpacing:!1,size:"large"}),!H&&F&&(0,l.jsx)("div",{className:b.formWrapper,children:(0,l.jsx)(c.E,{formId:C.id,isPurple:E,variant:"hero",customThankYouPageLink:null!=(w=null==C?void 0:C.customThankYouPageLink)?w:void 0})})]}),(0,l.jsx)("figure",{className:b.figure(_),children:(0,l.jsx)(d.s,{className:b.imageBlock(null==P?void 0:P.showBorder),src:null==P||null==(t=P.image)?void 0:t.url,alt:null==P||null==(g=P.image)?void 0:g.title,width:null==P||null==(f=P.image)?void 0:f.width,height:null==P||null==(v=P.image)?void 0:v.height,artDirection:{mobile:{width:256,height:256},tablet:{width:704,height:704},desktop:{width:624,height:736}},focusArea:null==P?void 0:P.focusArea,imagePriority:D})}),S&&(0,l.jsx)("div",{className:b.disclaimer,children:(0,l.jsx)(m.default,{contentBody:S})})]})})}},89784:(e,n,t)=>{t.r(n),t.d(n,{CustomersFilters:()=>c});var l=t(54568),a=t(7620),i=t(68061),r=t(85610),s=t.n(r);let o={title:s()("\n    h3\n    mb-2\n    lg:w-[741px]\n  "),description:s()("\n    paragraph\n    mb-[30px]\n    lg:w-[741px]\n  "),caretIcon:s()("\n    absolute\n    right-[15px]\n    h-[9px]\n    w-[16px]\n  "),hint:e=>s()("\n    font-normal\n    paragraph-sm\n\n    ".concat(e?"text-expressive-fuchsia":"text-dim-gray","\n  ")),icon:s()("\n    absolute\n    right-0\n  "),label:e=>s()("\n    xs:w-full\n    md:w-fit\n    relative\n\n    ".concat(e?"[&_span:first-child]:text-expressive-fuchsia":"[&:has(:focus)_span:first-child]:text-dp-purple","\n  ")),labelText:s()(" \n      h5-mobile\n      uppercase\n      text-black\n  "),select:e=>s()("\n      bg-transparent\n      border\n      cursor-pointer\n      font-semibold\n      pl-3\n      pr-10\n      py-[16.5px]\n      rounded-xl\n      w-full\n      flex\n      items-center\n\n      focus:border-2\n      focus:py-[15.5px]\n\n      focus-visible:outline-none\n      \n      text-ellipsis\n\n      border-black\n\n      ".concat(e?"\n          border-2\n          border-expressive-fuchsia\n          py-[15.5px]\n        ":"\n          border-dim-gray\n          focus:border-dp-purple\n        ","\n  ")),selectWrapper:s()("\n    flex\n    flex-col\n    mb-1.5\n    relative\n    w-full\n    max-w-full\n    md:max-w-fit\n    min-w-[200px]\n\n    sm:max-w-full\n  ")},c=e=>{let{selectProps:n,title:t,description:i,id:r,onChange:s,filtersSelected:c,onClearFilters:u}=e,[m,p]=(0,a.useState)(!1),h=e=>{p(!e)},x=(e,n)=>{let t=e.target;null==s||s(e,n),h(t)};return(0,l.jsx)("div",{className:"mx-auto flex max-w-content-max flex-col !overflow-visible px-4 lg:px-8","data-testid":r,children:(0,l.jsx)("div",{className:"flex flex-col gap-2",children:(0,l.jsxs)(l.Fragment,{children:[t&&(0,l.jsx)("h3",{className:o.title,children:t}),i&&(0,l.jsx)("p",{className:o.description,children:i}),(0,l.jsx)("div",{className:"flex flex-col items-center gap-12 md:flex-row",children:(0,l.jsxs)("div",{className:"flex w-full flex-1 flex-col flex-wrap gap-8 md:flex-row",children:[n&&n.length>0&&n.map((e,n)=>(0,l.jsx)(d,{item:e,hasError:m,handleChange:x,options:e.options||[],filtersSelected:c},n)),(0,l.jsx)("button",{className:"min-w-fit underline underline-offset-2",onClick:()=>{null==u||u(),p(!1)},children:"Clear all"})]})})]})})})},d=e=>{var n;let{item:t,hasError:r,handleChange:s,options:c,filtersSelected:d}=e,[u,m]=(0,a.useState)(!1);return(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)("div",{className:o.label(r),onMouseOver:()=>m(!0),onMouseLeave:()=>m(!1),children:[(0,l.jsx)("span",{className:o.labelText,children:t.label}),(0,l.jsx)("div",{className:o.selectWrapper,children:(0,l.jsxs)("div",{className:o.select(r),onClick:()=>m(!u),children:[(0,l.jsx)("p",{children:"Selection (".concat((null==(n=d[t.label])?void 0:n.length)||"0",")")}),(0,l.jsx)(i.NA,{className:o.caretIcon}),r&&(0,l.jsx)(i.IJ,{className:o.icon})]})}),u&&(0,l.jsx)("div",{className:"absolute left-0 top-full z-50 flex w-full flex-col divide-y divide-slate-200 text-ellipsis rounded-xl border border-black bg-white p-4 font-semibold",children:c.map((e,n)=>{var a;return(0,l.jsx)("div",{className:"flex cursor-pointer items-center py-2",children:(0,l.jsxs)("label",{className:"flex w-full cursor-pointer select-none items-center font-normal",htmlFor:e.value,children:[(0,l.jsx)("input",{className:"marketo-checkbox !mb-0",type:"checkbox",value:e.value,onChange:e=>s(e,t.label),checked:!!(null==d||null==(a=d[t.label])?void 0:a.includes(e.value)),name:e.value,id:e.value}),e.label]})},n)})})]})})}},90706:(e,n,t)=>{t.r(n),t.d(n,{PricingTierContext:()=>j,PricingTiers:()=>y,SEARCH_PARAM_BILLING_CYCLES:()=>b,SEARCH_PARAM_BILLING_CYCLE_DEFAULT:()=>w});var l=t(54568),a=t(99474),i=t(62942),r=t(7620),s=t(6520),o=t(62904),c=t(126),d=t(46379),u=t(2886),m=t(6279),p=t(72068),h=t(85610),x=t.n(h);let g={container:"container mx-auto px-4",content:"grid-container",title:x()("\n    h2-desktop\n    \n    font-semibold\n    mb-10\n\n  "),controls:()=>x()("\n    min-w-[244px]\n    pt-20px\n    md:pt-32px\n    lg:pt-0\n    pb-14\n    md:pb-7\n    flex\n    justify-center\n    items-center\n    md:items-start\n    gap-1\n    md:gap-6\n    flex-col\n    md:flex-row\n  "),toggle:()=>x()("\n    flex\n    flex-col\n    text-right\n    mb-1\n  "),discount:x()("\n    font-semibold\n    text-xs\n    mt-1\n    mr-6\n  "),toggleHelperText:x()("\n    paragraph-sm\n    mt-[5px]\n    flex\n    w-full\n    justify-around\n    text-center\n    font-semibold\n    [&>*]:flex-1\n  "),dropdown:()=>x()("\n    h-37px\n    w-fit\n    md:max-w-[244px]\n    z-[1]\n  "),copyText:x()("\n    h4-desktop\n    mb-4\n    \n    font-semibold\n    lg:max-w-[212px]\n  "),pricingContainer:x()("\n  lg:grid-container \n  mx-auto \n  !px-0\n  md:max-w-[720px] \n  lg:max-w-[1344px]\n  !flex\n  !flex-col\n"),pricingCardsContainer:e=>x()("\n    flex\n    flex-col\n    lg:flex-row\n    gap-[30px]\n    lg:gap-[25px]\n    col-span-full\n\n    ".concat(e?"\n      lg:col-start-5\n      lg:col-span-12\n    ":"","\n    \n  "))},f=[{label:"USD $",value:"usd"},{label:"CAD $",value:"cad"},{label:"EUR €",value:"eur"},{label:"GBP \xa3",value:"gbp"},{label:"NZD $",value:"nzd"},{label:"AUD $",value:"aud"}],v=["Monthly","Annual"],b=["monthly","yearly"],w=b[1],j=(0,r.createContext)({billingIndex:0,currency:"usd"}),y=e=>{let{id:n,sidebar:t,showPeriodToggle:h=!0,showCurrencyDropdown:x=!0,planTiersCollection:w,annualDiscount:y,scrollId:N,userSelectedCurrency:k,userBillingCycleSelected:C,setUserSelectedCurrency:T,setUserBillingCycleSelected:B,tabAnchor:I,planFeaturesDetails:A}=e,_=(0,i.useSearchParams)(),L=null==_?void 0:_.get("currency"),P=null==_?void 0:_.get("billing"),D=(0,a.useLocale)(),S=(0,i.useParams)()||{},[W]=(0,s.A)(["dp_regional_pricing"]),E=null!=L?L:"usd",[F,R]=(0,r.useState)(null!=k?k:E),[M,O]=(0,r.useState)("usd"),H=P?b.indexOf(P):1,[U,z]=(0,r.useState)("jp"===D?0:void 0!==C&&C>=0?Number(C):H),[V,K]=(0,r.useState)("jp"===D?"jpy":null!=k?k:E);(0,r.useEffect)(()=>{if(k)R(k),K(k);else if("jp"===D)R("jpy"),K("jpy");else if(L&&L!==F)R(L),K(L);else{let e=d.ST[W.dp_regional_pricing]||"usd";R(e),K(e)}},[W,L,D]);let G=e=>{let n=b[e],t=new URL(window.location.href);t.searchParams.set("billing",n),window.history.pushState(null,"",t.toString()),B&&B(e),z(e)},$=e=>{T&&T(e),T||K(e);let n=new URL(window.location.href);n.searchParams.set("currency",e),window.history.pushState(null,"",n.toString())};function q(e){return null!=e&&void 0!=e&&-1!==b.indexOf(e)}(0,r.useEffect)(()=>{if("jp"===D)K("jpy"),R("jpy");else{let e=d.ST[W.dp_regional_pricing]||"usd";K(e),R(e)}},[W,D]);let Y="jp"===D?0:null!=C&&C>=0&&q(b[C])?C:S.billing&&q(S.billing)?b.indexOf(S.billing):1;(0,r.useEffect)(()=>{O((()=>{if(k)return k;let e=S.currency;if(function(e){return null!=e&&void 0!=e&&e in d.Wi}(e))return e;let n=W.dp_regional_pricing;return d.ST[n]||"usd"})())},[S.currency,k,W.dp_regional_pricing]);let Z=(0,r.useMemo)(()=>({items:w.items}),[w]),Q=(0,r.useMemo)(()=>{var e;return{features:null!=(e=null==A?void 0:A.features.map(e=>({...e,features:e.featureList})))?e:[]}},[A]),X=(0,r.useMemo)(()=>({items:w.items.map(e=>{var n,t,l,a,i,r,s,o,c;let m=e.price?(0,u.y)(e.price,M,0):0;return{...e,price:e.title,description1:"".concat(d.Wi[M]||"$").concat(m," ").concat((null==M?void 0:M.toUpperCase())||"USD"),ctaCollection:{items:[{ctaUrl:null==(n=e.ctasCollection.items[0])?void 0:n.ctaUrl,ctaText:null==(t=e.ctasCollection.items[0])?void 0:t.ctaText,ctaType:null==(l=e.ctasCollection.items[0])?void 0:l.ctaType,pageLink:{type:"PageBuilder",slug:"view-all"}}]},viewAllCTA:{ctaText:null==(i=e.ctasCollection)||null==(a=i.items[1])?void 0:a.ctaText,ctaUrl:null==(s=e.ctasCollection)||null==(r=s.items[1])?void 0:r.ctaUrl,ctaType:null==(c=e.ctasCollection)||null==(o=c.items[1])?void 0:o.ctaType,pageLink:{type:"PageBuilder",slug:"view-all"}}}})}),[w,M]),J=(0,r.useMemo)(()=>Z.items.map(e=>{var n,t,a;return null==e||null==(a=e.footnotes)||null==(t=a.json)||null==(n=t.content)?void 0:n.map((e,n)=>(0,l.jsx)("div",{className:"flex flex-col gap-1",children:e.content.map((n,t)=>{if(t%2==0){var a;return(0,l.jsxs)("div",{children:[(0,l.jsx)("sup",{children:n.value})," ",null==(a=e.content[t+1])?void 0:a.value]},n.value+t)}return null})},n))}),[Z]),ee=(0,r.useMemo)(()=>({variant:"Pricing Modal",hideMobile:!1,content:(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"relative min-w-[1000px]",children:[(0,l.jsx)(m.A,{showCurrencyDropdown:!1,showPeriodToggle:!1,title:"Compare plan features",tierCards:X,activeToggleIndex:Y}),(0,l.jsx)(p.V,{features:Q,columns:Z.items.length})]}),(0,l.jsx)("div",{className:"flex gap-4 lg:grid lg:grid-cols-2",children:(0,l.jsx)("div",{children:(0,l.jsx)("div",{className:"text-base text-dim-gray",children:J[0]})})})]})}),[X,J,Y,Z,Q]);return(0,l.jsx)(j.Provider,{value:{billingIndex:void 0!==C&&C>=0?C:U,currency:null!=k?k:V},children:(0,l.jsxs)("div",{className:g.container,"data-testid":n,id:N,children:[!t&&(0,l.jsx)("div",{className:"flex flex-col items-center",children:en()}),(0,l.jsxs)("div",{className:g.pricingContainer,children:[t&&en(),(0,l.jsx)("div",{className:g.pricingCardsContainer(!!t),children:w.items.map((e,n)=>(0,l.jsx)(d.Li,{...e,selectedCurrency:null!=k?k:V,activeToggleIndex:void 0!==C&&C>=0?C:U,modalInfo:ee},e.title+n))})]})]})});function en(){return(0,l.jsxs)("div",{className:g.controls(),children:["jp"!==D&&h&&(0,l.jsxs)("div",{className:g.toggle(),children:[(0,l.jsx)(c.a,{togglesWithIds:v.map(e=>({label:e,id:"".concat(I,"-").concat(e.toLowerCase())})),isDark:!1,variant:"Pricing",activeIndex:void 0!==C&&C>=0?C:U,setActiveIndex:G,preRender:!0,"aria-label":"Pricing period","data-selector-name":"pricing-period-toggle","aria-checked":1===U}),(0,l.jsx)("div",{className:y&&(void 0!==C&&C>=0?C:U)?"visible":"invisible",children:(0,l.jsx)("p",{className:g.discount,children:y})})]}),x&&(0,l.jsx)("div",{"data-selector-name":"pricing-currency-selector",children:(0,l.jsx)(o.m,{className:g.dropdown(),label:"Select currency",options:"jp"===D?[...f,{label:"JPY \xa5",value:"jpy"}]:f,selectedValue:null!=k?k:V,onChange:$,size:"small",transformOrigin:"top",variant:"pricing","aria-label":"Currency"})})]})}}},91879:(e,n,t)=>{t.r(n),t.d(n,{PartnerTickerBanner:()=>m});var l=t(54568),a=t(91039),i=t(7620),r=t(5474),s=t(71034),o=t(85610);let c={White:"bg-white",Tan:"bg-tan",Black:"dark bg-black","Light Tan":"bg-light-tan","Eerie Black":"dark bg-eerie-black"},d={container:e=>"\n    container\n    ".concat(c[e],"\n  "),content:"\n    flex\n    flex-col\n  ",slickslider:()=>"\n    mt-0\n  ",slickSlide:()=>"\n    h-full\n    relative\n  ",slickLogoContainer:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default";return"\n    w-auto\n    flex\n    items-center\n    justify-center\n    ".concat("large"===e?"!mx-4 min-h-[150px] h-[150px] mx-[15px]":"min-h-[32px] max-h-[32px] h-[32px] mx-[50px]","\n  ")},slickLogo:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default";return"\n    object-contain\n    w-auto\n    ".concat("large"===e?"min-h-[150px] max-h-[150px] h-[150px]":"min-h-[32px] max-h-[32px] h-[32px]","\n  ")},partnerTickerDescription:()=>"\n    text-[32px]\n    medium\n    text-center\n    mb-16\n    dark:text-white\n    px-4\n    lg:px-8\n    leading-[140%]\n  "},u=(0,a.default)(()=>t.e(3131).then(t.bind(t,3131)),{loadableGenerated:{webpack:()=>[3131]},ssr:!0}),m=e=>{var n;let{imageCollection:t,id:a,partnerCopy:o,backgroundColor:c,size:m="default"}=e,[p,h]=(0,i.useState)(!1),[x,g]=(0,i.useState)(0),f=(0,i.useRef)(null),v=()=>{setTimeout(()=>{var e;(null==(e=f.current)?void 0:e.contains(document.activeElement))||(g(e=>e+1),h(!1))},10)};return(0,l.jsx)("section",{className:d.container(c),"data-testid":a,ref:f,onBlur:v,"data-section":"partner-ticker-banner",children:(0,l.jsxs)("div",{className:d.content,children:[(0,l.jsxs)("p",{className:d.partnerTickerDescription(),children:[" ",o," "]}),(0,l.jsx)(u,{autoFill:!0,className:d.slickslider(),pauseOnHover:!0,play:!p,children:null==t||null==(n=t.items)?void 0:n.map((e,n)=>{let{url:t}=e;return(0,l.jsx)("div",{className:d.slickSlide(),children:(0,l.jsx)("div",{className:d.slickLogoContainer(m),children:t?(0,l.jsx)(s.N,{href:t,onFocus:()=>{h(!0)},onBlur:v,className:"flex h-[48px] items-center justify-center",children:(0,l.jsx)(r.s,{src:e.image.url,alt:e.image.title||"Banner Image",width:"300",height:"300",className:d.slickLogo(m)})}):(0,l.jsx)(r.s,{src:e.image.url,alt:e.image.title||"Banner Image",width:"300",height:"300",className:d.slickLogo(m)})})},n)})})]})},x)}}}]);